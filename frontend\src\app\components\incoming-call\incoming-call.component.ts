import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { IncomingCall, CallType } from '../../models/message.model';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.css'],
})
export class IncomingCallComponent implements OnInit, OnDestroy {
  incomingCall: IncomingCall | null = null;
  isProcessing: boolean = false;
  currentUserId: string | null = null;

  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;

  constructor(
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    // Obtenir l'ID de l'utilisateur actuel
    this.currentUserId = this.getCurrentUserId();

    // S'abonner aux appels entrants
    const incomingCallSub = this.callService.incomingCall$.subscribe(
      (call: any) => {
        if (call === null || call === undefined) {
          this.incomingCall = null;
          return;
        }

        // Afficher le template des deux côtés (sender et receiver)
        this.incomingCall = call;
        this.isProcessing = false;

        const userRole = this.isReceiver(call) ? 'receiver' : 'sender';
        console.log(
          `📞 [IncomingCall] Incoming call displayed for ${userRole}:`,
          {
            callId: call.id,
            caller: call.caller?.username,
            type: call.type,
            currentUserId: this.currentUserId,
            userRole: userRole,
          }
        );
      }
    );

    this.subscriptions.push(incomingCallSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Accepte l'appel entrant
   */
  acceptCall(): void {
    if (!this.incomingCall || this.isProcessing) return;

    console.log('✅ [IncomingCall] Accepting call:', this.incomingCall.id);
    this.isProcessing = true;

    this.callService.acceptCall(this.incomingCall).subscribe({
      next: (call: any) => {
        console.log('✅ [IncomingCall] Call accepted successfully:', call);
        this.isProcessing = false;
        // L'interface d'appel actif va automatiquement s'afficher
      },
      error: (error: any) => {
        console.error('❌ [IncomingCall] Error accepting call:', error);
        this.logger.error('Error accepting call:', error);
        this.isProcessing = false;

        // Optionnel : Afficher un message d'erreur à l'utilisateur
        alert("Erreur lors de l'acceptation de l'appel. Veuillez réessayer.");
      },
    });
  }

  /**
   * Rejette l'appel entrant
   */
  rejectCall(): void {
    if (!this.incomingCall || this.isProcessing) return;

    console.log('❌ [IncomingCall] Rejecting call:', this.incomingCall.id);
    this.isProcessing = true;

    this.callService
      .rejectCall(this.incomingCall.id, 'User rejected')
      .subscribe({
        next: (result: any) => {
          console.log('✅ [IncomingCall] Call rejected successfully:', result);
          this.isProcessing = false;
        },
        error: (error: any) => {
          console.error('❌ [IncomingCall] Error rejecting call:', error);
          this.logger.error('Error rejecting call:', error);
          this.isProcessing = false;
        },
      });
  }

  /**
   * Obtient le nom de l'appelant
   */
  getCallerName(): string {
    return this.incomingCall?.caller?.username || 'Utilisateur inconnu';
  }

  /**
   * Obtient l'avatar de l'appelant
   */
  getCallerAvatar(): string {
    return (
      this.incomingCall?.caller?.image || '/assets/images/default-avatar.png'
    );
  }

  /**
   * Obtient le type d'appel (audio/vidéo)
   */
  getCallTypeText(): string {
    if (!this.incomingCall) return '';

    return this.incomingCall.type === CallType.VIDEO
      ? 'Appel vidéo'
      : 'Appel audio';
  }

  /**
   * Obtient l'icône du type d'appel
   */
  getCallTypeIcon(): string {
    if (!this.incomingCall) return 'fa-phone';

    return this.incomingCall.type === CallType.VIDEO ? 'fa-video' : 'fa-phone';
  }

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(): boolean {
    return this.incomingCall?.type === CallType.VIDEO;
  }

  /**
   * Vérifie si l'utilisateur actuel est le sender de l'appel
   */
  isSender(): boolean {
    if (!this.incomingCall || !this.currentUserId) return false;
    const result = !this.isReceiver(this.incomingCall);
    console.log('📞 [IncomingCall] isSender() result:', result);
    return result;
  }

  /**
   * Vérifie si l'utilisateur actuel est le receiver de l'appel
   */
  isReceiverUser(): boolean {
    if (!this.incomingCall || !this.currentUserId) return false;
    const result = this.isReceiver(this.incomingCall);
    console.log('📞 [IncomingCall] isReceiverUser() result:', result);
    return result;
  }

  /**
   * Obtient l'ID de l'utilisateur actuel depuis localStorage
   */
  private getCurrentUserId(): string | null {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString === 'null' || userString === 'undefined') {
        return null;
      }
      const user = JSON.parse(userString);
      return user._id || user.id || user.userId || null;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return null;
    }
  }

  /**
   * Vérifie si l'utilisateur actuel est le receiver de l'appel
   */
  private isReceiver(call: IncomingCall): boolean {
    if (!call || !this.currentUserId) {
      console.log(
        '📞 [IncomingCall] isReceiver: Missing call or currentUserId',
        {
          hasCall: !!call,
          currentUserId: this.currentUserId,
        }
      );
      return false;
    }

    const recipientId = call.recipient?.id || call.recipient?._id;
    const callerId = call.caller?.id || call.caller?._id;

    // L'utilisateur est le receiver si son ID correspond au recipient
    const isRecipient = String(recipientId) === String(this.currentUserId);

    console.log('📞 [IncomingCall] DETAILED ROLE CHECK:', {
      currentUserId: this.currentUserId,
      currentUserIdType: typeof this.currentUserId,
      recipientId,
      recipientIdType: typeof recipientId,
      callerId,
      callerIdType: typeof callerId,
      recipientIdString: String(recipientId),
      currentUserIdString: String(this.currentUserId),
      isRecipient,
      callerName: call.caller?.username,
      recipientName: call.recipient?.username,
      fullCall: call,
    });

    return isRecipient;
  }
}

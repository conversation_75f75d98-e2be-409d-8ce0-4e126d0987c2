{"ast": null, "code": "import { Subscription } from 'rxjs';\nimport { CallType, CallStatus } from '../../models/message.model';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../services/call.service\";\nimport * as i2 from \"../../services/logger.service\";\nimport * as i3 from \"@angular/common\";\nconst _c0 = [\"localVideo\"];\nconst _c1 = [\"remoteVideo\"];\nfunction ActiveCallComponent_div_0_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 17);\n    i0.ɵɵelement(2, \"video\", 18, 19)(4, \"video\", 20, 21);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"muted\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"muted\", true);\n  }\n}\nfunction ActiveCallComponent_div_0_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 3);\n    i0.ɵɵelement(1, \"video\", 22, 19)(3, \"video\", 22, 21);\n    i0.ɵɵelementStart(5, \"div\", 23);\n    i0.ɵɵelement(6, \"div\", 24)(7, \"div\", 25)(8, \"div\", 26)(9, \"div\", 27)(10, \"div\", 28);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"muted\", false);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"muted\", true);\n  }\n}\nfunction ActiveCallComponent_div_0_button_17_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_button_17_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const ctx_r8 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r8.toggleCamera());\n    });\n    i0.ɵɵelement(1, \"i\", 13);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r3 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassMap(ctx_r3.isVideoMuted ? \"bg-red-500 hover:bg-red-600 shadow-red-500/50\" : \"bg-blue-500 hover:bg-blue-600 shadow-blue-500/50\");\n    i0.ɵɵproperty(\"title\", ctx_r3.isVideoMuted ? \"Activer la cam\\u00E9ra\" : \"D\\u00E9sactiver la cam\\u00E9ra\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r3.isVideoMuted ? \"fa-video-slash\" : \"fa-video\");\n  }\n}\nfunction ActiveCallComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r11 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4);\n    i0.ɵɵelement(4, \"img\", 5)(5, \"div\", 6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h2\", 7);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 8);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 9);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, ActiveCallComponent_div_0_div_12_Template, 6, 2, \"div\", 10);\n    i0.ɵɵtemplate(13, ActiveCallComponent_div_0_div_13_Template, 11, 2, \"div\", 10);\n    i0.ɵɵelementStart(14, \"div\", 11)(15, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_15_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r10 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r10.toggleMicrophone());\n    });\n    i0.ɵɵelement(16, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(17, ActiveCallComponent_div_0_button_17_Template, 2, 5, \"button\", 14);\n    i0.ɵɵelementStart(18, \"button\", 12);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_18_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r12 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r12.toggleSpeaker());\n    });\n    i0.ɵɵelement(19, \"i\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 15);\n    i0.ɵɵlistener(\"click\", function ActiveCallComponent_div_0_Template_button_click_20_listener() {\n      i0.ɵɵrestoreView(_r11);\n      const ctx_r13 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r13.endCall());\n    });\n    i0.ɵɵelement(21, \"i\", 16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"src\", ctx_r0.getOtherParticipantAvatar(), i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.getOtherParticipantName());\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getOtherParticipantName(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.getCallStatusText(), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.callDuration, \" \");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r0.isVideoCall());\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(ctx_r0.isAudioMuted ? \"bg-red-500 hover:bg-red-600 shadow-red-500/50\" : \"bg-blue-500 hover:bg-blue-600 shadow-blue-500/50\");\n    i0.ɵɵproperty(\"title\", ctx_r0.isAudioMuted ? \"Activer le micro\" : \"Couper le micro\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isAudioMuted ? \"fa-microphone-slash\" : \"fa-microphone\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.isVideoCall());\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isSpeakerOn ? \"bg-blue-500 hover:bg-blue-600 shadow-blue-500/50\" : \"bg-gray-600 hover:bg-gray-700 shadow-gray-600/50\");\n    i0.ɵɵproperty(\"title\", ctx_r0.isSpeakerOn ? \"D\\u00E9sactiver le haut-parleur\" : \"Activer le haut-parleur\");\n    i0.ɵɵadvance(1);\n    i0.ɵɵclassMap(ctx_r0.isSpeakerOn ? \"fa-volume-up\" : \"fa-volume-mute\");\n  }\n}\nexport class ActiveCallComponent {\n  constructor(callService, logger) {\n    this.callService = callService;\n    this.logger = logger;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.currentUserId = null;\n    // États des contrôles\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    this.subscription = new Subscription();\n    this.callStartTime = null;\n    this.logger.info('ActiveCall', '🚀 ActiveCall component initialized');\n  }\n  ngOnInit() {\n    this.logger.info('ActiveCall', '📞 Setting up active call subscription');\n    // Obtenir l'ID de l'utilisateur actuel\n    this.currentUserId = this.getCurrentUserId();\n    // S'abonner aux appels actifs\n    this.subscription.add(this.callService.activeCall$.subscribe(call => {\n      if (call === null || call === undefined) {\n        this.activeCall = null;\n        this.stopCallTimer();\n        return;\n      }\n      // Afficher le template des deux côtés (sender et receiver)\n      this.activeCall = call;\n      if (call.status === CallStatus.CONNECTED) {\n        this.logger.info('ActiveCall', '✅ Call connected, starting timer');\n        this.startCallTimer();\n      } else {\n        this.logger.debug('ActiveCall', '⏹️ Call not connected, stopping timer');\n        this.stopCallTimer();\n      }\n      const userRole = this.isReceiver(call) ? 'receiver' : 'sender';\n      console.log(`📞 [ActiveCall] Active call displayed for ${userRole}:`, {\n        callId: call.id,\n        status: call.status,\n        currentUserId: this.currentUserId,\n        userRole: userRole\n      });\n    }));\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après l'initialisation de la vue\n    if (this.localVideo && this.remoteVideo) {\n      this.callService.setVideoElements(this.localVideo.nativeElement, this.remoteVideo.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopCallTimer();\n  }\n  /**\n   * Démarre le timer de durée d'appel\n   */\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.durationInterval = setInterval(() => {\n      if (this.callStartTime) {\n        const now = new Date();\n        const diff = now.getTime() - this.callStartTime.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const seconds = Math.floor(diff % 60000 / 1000);\n        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      }\n    }, 1000);\n  }\n  /**\n   * Arrête le timer de durée d'appel\n   */\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n    this.callDuration = '00:00';\n    this.callStartTime = null;\n  }\n  /**\n   * Bascule l'état du microphone\n   */\n  toggleMicrophone() {\n    this.isAudioMuted = !this.isAudioMuted;\n    console.log('Microphone:', this.isAudioMuted ? 'Muted' : 'Unmuted');\n  }\n  /**\n   * Bascule l'état de la caméra\n   */\n  toggleCamera() {\n    this.isVideoMuted = !this.isVideoMuted;\n    console.log('Camera:', this.isVideoMuted ? 'Muted' : 'Unmuted');\n  }\n  /**\n   * Bascule l'état du haut-parleur\n   */\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    console.log('Speaker:', this.isSpeakerOn ? 'On' : 'Off');\n  }\n  /**\n   * Termine l'appel\n   */\n  endCall() {\n    if (!this.activeCall) return;\n    console.log('Ending call:', this.activeCall.id);\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: () => {\n        console.log('Call ended successfully');\n      },\n      error: error => {\n        console.error('Error ending call:', error);\n      }\n    });\n  }\n  /**\n   * Vérifie si c'est un appel vidéo\n   */\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO;\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est le sender de l'appel\n   */\n  isSender() {\n    if (!this.activeCall || !this.currentUserId) return false;\n    return !this.isReceiver(this.activeCall);\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  isReceiverUser() {\n    if (!this.activeCall || !this.currentUserId) return false;\n    return this.isReceiver(this.activeCall);\n  }\n  /**\n   * Obtient le texte du statut de l'appel\n   */\n  getCallStatusText() {\n    if (!this.activeCall) return '';\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Sonnerie...';\n      case CallStatus.CONNECTED:\n        return 'Connecté';\n      case CallStatus.ENDED:\n        return 'Terminé';\n      case CallStatus.MISSED:\n        return 'Manqué';\n      case CallStatus.REJECTED:\n        return 'Rejeté';\n      case CallStatus.FAILED:\n        return 'Échec';\n      default:\n        return 'En cours...';\n    }\n  }\n  /**\n   * Obtient le nom de l'autre participant\n   */\n  getOtherParticipantName() {\n    if (!this.activeCall) return '';\n    return this.activeCall.recipient?.username || this.activeCall.caller?.username || 'Utilisateur';\n  }\n  /**\n   * Obtient l'avatar de l'autre participant\n   */\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) return '/assets/images/default-avatar.png';\n    return this.activeCall.recipient?.image || this.activeCall.caller?.image || '/assets/images/default-avatar.png';\n  }\n  /**\n   * Obtient l'ID de l'utilisateur actuel depuis localStorage\n   */\n  getCurrentUserId() {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        return null;\n      }\n      const user = JSON.parse(userString);\n      return user._id || user.id || user.userId || null;\n    } catch (error) {\n      this.logger.error('ActiveCall', 'Error getting current user ID:', error);\n      return null;\n    }\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  isReceiver(call) {\n    if (!call || !this.currentUserId) return false;\n    const recipientId = call.recipient?.id || call.recipient?._id;\n    const callerId = call.caller?.id || call.caller?._id;\n    // L'utilisateur est le receiver si son ID correspond au recipient\n    const isRecipient = String(recipientId) === String(this.currentUserId);\n    this.logger.debug('ActiveCall', 'Checking if user is receiver:', {\n      currentUserId: this.currentUserId,\n      recipientId,\n      callerId,\n      isRecipient\n    });\n    return isRecipient;\n  }\n  static {\n    this.ɵfac = function ActiveCallComponent_Factory(t) {\n      return new (t || ActiveCallComponent)(i0.ɵɵdirectiveInject(i1.CallService), i0.ɵɵdirectiveInject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ActiveCallComponent,\n      selectors: [[\"app-active-call\"]],\n      viewQuery: function ActiveCallComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.localVideo = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.remoteVideo = _t.first);\n        }\n      },\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[9998]\", 4, \"ngIf\"], [1, \"fixed\", \"inset-0\", \"bg-black\", \"bg-opacity-90\", \"flex\", \"items-center\", \"justify-center\", \"z-[9998]\"], [1, \"bg-gray-900\", \"rounded-2xl\", \"p-8\", \"max-w-md\", \"w-full\", \"mx-4\", \"text-center\", \"shadow-2xl\", \"border\", \"border-gray-700\"], [1, \"mb-6\"], [1, \"relative\", \"inline-block\"], [1, \"w-24\", \"h-24\", \"rounded-full\", \"mx-auto\", \"mb-4\", \"border-4\", \"border-blue-500\", \"shadow-lg\", 3, \"src\", \"alt\"], [1, \"absolute\", \"-bottom-2\", \"-right-2\", \"w-6\", \"h-6\", \"bg-green-500\", \"rounded-full\", \"border-2\", \"border-gray-900\", \"animate-pulse\"], [1, \"text-2xl\", \"font-bold\", \"text-white\", \"mb-2\"], [1, \"text-blue-400\", \"text-lg\", \"font-medium\"], [1, \"text-gray-400\", \"text-sm\", \"mt-2\"], [\"class\", \"mb-6\", 4, \"ngIf\"], [1, \"flex\", \"justify-center\", \"space-x-6\", \"mt-8\"], [1, \"w-16\", \"h-16\", \"rounded-full\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"shadow-lg\", 3, \"title\", \"click\"], [1, \"fas\", \"text-white\", \"text-xl\"], [\"class\", \"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\", 3, \"class\", \"title\", \"click\", 4, \"ngIf\"], [\"title\", \"Raccrocher\", 1, \"w-16\", \"h-16\", \"rounded-full\", \"bg-red-500\", \"hover:bg-red-600\", \"flex\", \"items-center\", \"justify-center\", \"transition-all\", \"duration-300\", \"transform\", \"hover:scale-110\", \"shadow-lg\", \"shadow-red-500/50\", 3, \"click\"], [1, \"fas\", \"fa-phone-slash\", \"text-white\", \"text-xl\"], [1, \"relative\", \"bg-gray-800\", \"rounded-lg\", \"overflow-hidden\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"w-full\", \"h-48\", \"object-cover\", 3, \"muted\"], [\"remoteVideo\", \"\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"absolute\", \"top-2\", \"right-2\", \"w-20\", \"h-15\", \"object-cover\", \"rounded\", \"border-2\", \"border-blue-500\", 3, \"muted\"], [\"localVideo\", \"\"], [\"autoplay\", \"\", \"playsinline\", \"\", 2, \"display\", \"none\", 3, \"muted\"], [1, \"flex\", \"justify-center\", \"items-center\", \"space-x-2\", \"mb-4\"], [1, \"w-2\", \"h-8\", \"bg-blue-500\", \"rounded\", \"animate-pulse\"], [1, \"w-2\", \"h-12\", \"bg-blue-400\", \"rounded\", \"animate-pulse\", 2, \"animation-delay\", \"0.1s\"], [1, \"w-2\", \"h-6\", \"bg-blue-500\", \"rounded\", \"animate-pulse\", 2, \"animation-delay\", \"0.2s\"], [1, \"w-2\", \"h-10\", \"bg-blue-400\", \"rounded\", \"animate-pulse\", 2, \"animation-delay\", \"0.3s\"], [1, \"w-2\", \"h-4\", \"bg-blue-500\", \"rounded\", \"animate-pulse\", 2, \"animation-delay\", \"0.4s\"]],\n      template: function ActiveCallComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ActiveCallComponent_div_0_Template, 22, 18, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.activeCall && ctx.activeCall.status === \"CONNECTED\");\n        }\n      },\n      dependencies: [i3.NgIf],\n      styles: [\"\\n\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_pulse-ring {\\n  0% {\\n    transform: scale(1);\\n    opacity: 1;\\n  }\\n  100% {\\n    transform: scale(1.5);\\n    opacity: 0;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_audio-wave {\\n  0%, 100% {\\n    height: 15px;\\n  }\\n  50% {\\n    height: 40px;\\n  }\\n}\\n\\n\\n\\n.glow-effect[_ngcontent-%COMP%] {\\n  box-shadow: 0 0 20px rgba(59, 130, 246, 0.5);\\n}\\n\\n.glow-effect[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 0 30px rgba(59, 130, 246, 0.7);\\n}\\n\\n\\n\\n.audio-bar[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_audio-wave 1.5s ease-in-out infinite;\\n}\\n\\n.audio-bar[_ngcontent-%COMP%]:nth-child(2) { animation-delay: 0.1s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(3) { animation-delay: 0.2s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(4) { animation-delay: 0.3s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(5) { animation-delay: 0.4s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(6) { animation-delay: 0.5s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(7) { animation-delay: 0.6s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(8) { animation-delay: 0.7s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(9) { animation-delay: 0.8s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(10) { animation-delay: 0.9s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(11) { animation-delay: 1.0s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(12) { animation-delay: 1.1s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(13) { animation-delay: 1.2s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(14) { animation-delay: 1.3s; }\\n.audio-bar[_ngcontent-%COMP%]:nth-child(15) { animation-delay: 1.4s; }\\n\\n\\n\\n.backdrop-blur-custom[_ngcontent-%COMP%] {\\n  backdrop-filter: blur(10px);\\n  -webkit-backdrop-filter: blur(10px);\\n}\\n\\n\\n\\nbutton[_ngcontent-%COMP%] {\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\nbutton[_ngcontent-%COMP%]:active {\\n  transform: scale(0.95);\\n}\\n\\n\\n\\nbutton[_ngcontent-%COMP%]:hover {\\n  filter: brightness(1.1);\\n}\\n\\n\\n\\n.fas[_ngcontent-%COMP%] {\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_spin {\\n  from {\\n    transform: rotate(0deg);\\n  }\\n  to {\\n    transform: rotate(360deg);\\n  }\\n}\\n\\n.animate-spin[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_spin 1s linear infinite;\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_custom-pulse {\\n  0%, 100% {\\n    opacity: 1;\\n    transform: scale(1);\\n  }\\n  50% {\\n    opacity: 0.7;\\n    transform: scale(1.05);\\n  }\\n}\\n\\n.custom-pulse[_ngcontent-%COMP%] {\\n  animation: _ngcontent-%COMP%_custom-pulse 2s ease-in-out infinite;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .w-48[_ngcontent-%COMP%] {\\n    width: 8rem;\\n  }\\n  \\n  .h-36[_ngcontent-%COMP%] {\\n    height: 6rem;\\n  }\\n  \\n  .text-4xl[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  \\n  .w-16[_ngcontent-%COMP%] {\\n    width: 3rem;\\n  }\\n  \\n  .h-16[_ngcontent-%COMP%] {\\n    height: 3rem;\\n  }\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_gradient-shift {\\n  0% {\\n    background-position: 0% 50%;\\n  }\\n  50% {\\n    background-position: 100% 50%;\\n  }\\n  100% {\\n    background-position: 0% 50%;\\n  }\\n}\\n\\n.animated-gradient[_ngcontent-%COMP%] {\\n  background: linear-gradient(-45deg, #1f2937, #1e3a8a, #7c3aed, #1f2937);\\n  background-size: 400% 400%;\\n  animation: _ngcontent-%COMP%_gradient-shift 15s ease infinite;\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Subscription", "CallType", "CallStatus", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ɵɵlistener", "ActiveCallComponent_div_0_button_17_Template_button_click_0_listener", "ɵɵrestoreView", "_r9", "ctx_r8", "ɵɵnextContext", "ɵɵresetView", "toggleCamera", "ɵɵclassMap", "ctx_r3", "isVideoMuted", "ɵɵtext", "ɵɵtemplate", "ActiveCallComponent_div_0_div_12_Template", "ActiveCallComponent_div_0_div_13_Template", "ActiveCallComponent_div_0_Template_button_click_15_listener", "_r11", "ctx_r10", "toggleMicrophone", "ActiveCallComponent_div_0_button_17_Template", "ActiveCallComponent_div_0_Template_button_click_18_listener", "ctx_r12", "toggleSpeaker", "ActiveCallComponent_div_0_Template_button_click_20_listener", "ctx_r13", "endCall", "ctx_r0", "getOtherParticipantAvatar", "ɵɵsanitizeUrl", "getOtherParticipantName", "ɵɵtextInterpolate1", "getCallStatusText", "callDuration", "isVideoCall", "isAudioMuted", "isSpeakerOn", "ActiveCallComponent", "constructor", "callService", "logger", "activeCall", "currentUserId", "subscription", "callStartTime", "info", "ngOnInit", "getCurrentUserId", "add", "activeCall$", "subscribe", "call", "undefined", "stopCallTimer", "status", "CONNECTED", "startCallTimer", "debug", "userRole", "isReceiver", "console", "log", "callId", "id", "ngAfterViewInit", "localVideo", "remoteVideo", "setVideoElements", "nativeElement", "ngOnDestroy", "unsubscribe", "Date", "durationInterval", "setInterval", "now", "diff", "getTime", "minutes", "Math", "floor", "seconds", "toString", "padStart", "clearInterval", "next", "error", "type", "VIDEO", "isSender", "isReceiverUser", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "recipient", "username", "caller", "image", "userString", "localStorage", "getItem", "user", "JSON", "parse", "_id", "userId", "recipientId", "callerId", "isRecipient", "String", "ɵɵdirectiveInject", "i1", "CallService", "i2", "LoggerService", "selectors", "viewQuery", "ActiveCallComponent_Query", "rf", "ctx", "ActiveCallComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts", "C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.html"], "sourcesContent": ["import {\n  Component,\n  On<PERSON>nit,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewInit,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  activeCall: Call | null = null;\n  callDuration = '00:00';\n  currentUserId: string | null = null;\n\n  // États des contrôles\n  isAudioMuted = false;\n  isVideoMuted = false;\n  isSpeakerOn = true;\n\n  private subscription: Subscription = new Subscription();\n  private callStartTime: Date | null = null;\n  private durationInterval: any;\n\n  constructor(private callService: CallService, private logger: LoggerService) {\n    this.logger.info('ActiveCall', '🚀 ActiveCall component initialized');\n  }\n\n  ngOnInit(): void {\n    this.logger.info('ActiveCall', '📞 Setting up active call subscription');\n\n    // Obtenir l'ID de l'utilisateur actuel\n    this.currentUserId = this.getCurrentUserId();\n\n    // S'abonner aux appels actifs\n    this.subscription.add(\n      this.callService.activeCall$.subscribe((call) => {\n        if (call === null || call === undefined) {\n          this.activeCall = null;\n          this.stopCallTimer();\n          return;\n        }\n\n        // Afficher le template des deux côtés (sender et receiver)\n        this.activeCall = call;\n\n        if (call.status === CallStatus.CONNECTED) {\n          this.logger.info('ActiveCall', '✅ Call connected, starting timer');\n          this.startCallTimer();\n        } else {\n          this.logger.debug(\n            'ActiveCall',\n            '⏹️ Call not connected, stopping timer'\n          );\n          this.stopCallTimer();\n        }\n\n        const userRole = this.isReceiver(call) ? 'receiver' : 'sender';\n        console.log(`📞 [ActiveCall] Active call displayed for ${userRole}:`, {\n          callId: call.id,\n          status: call.status,\n          currentUserId: this.currentUserId,\n          userRole: userRole,\n        });\n      })\n    );\n  }\n\n  ngAfterViewInit(): void {\n    // Configurer les éléments vidéo après l'initialisation de la vue\n    if (this.localVideo && this.remoteVideo) {\n      this.callService.setVideoElements(\n        this.localVideo.nativeElement,\n        this.remoteVideo.nativeElement\n      );\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscription.unsubscribe();\n    this.stopCallTimer();\n  }\n\n  /**\n   * Démarre le timer de durée d'appel\n   */\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.durationInterval = setInterval(() => {\n      if (this.callStartTime) {\n        const now = new Date();\n        const diff = now.getTime() - this.callStartTime.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const seconds = Math.floor((diff % 60000) / 1000);\n        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds\n          .toString()\n          .padStart(2, '0')}`;\n      }\n    }, 1000);\n  }\n\n  /**\n   * Arrête le timer de durée d'appel\n   */\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n    this.callDuration = '00:00';\n    this.callStartTime = null;\n  }\n\n  /**\n   * Bascule l'état du microphone\n   */\n  toggleMicrophone(): void {\n    this.isAudioMuted = !this.isAudioMuted;\n    console.log('Microphone:', this.isAudioMuted ? 'Muted' : 'Unmuted');\n  }\n\n  /**\n   * Bascule l'état de la caméra\n   */\n  toggleCamera(): void {\n    this.isVideoMuted = !this.isVideoMuted;\n    console.log('Camera:', this.isVideoMuted ? 'Muted' : 'Unmuted');\n  }\n\n  /**\n   * Bascule l'état du haut-parleur\n   */\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    console.log('Speaker:', this.isSpeakerOn ? 'On' : 'Off');\n  }\n\n  /**\n   * Termine l'appel\n   */\n  endCall(): void {\n    if (!this.activeCall) return;\n\n    console.log('Ending call:', this.activeCall.id);\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: () => {\n        console.log('Call ended successfully');\n      },\n      error: (error: any) => {\n        console.error('Error ending call:', error);\n      },\n    });\n  }\n\n  /**\n   * Vérifie si c'est un appel vidéo\n   */\n  isVideoCall(): boolean {\n    return this.activeCall?.type === CallType.VIDEO;\n  }\n\n  /**\n   * Vérifie si l'utilisateur actuel est le sender de l'appel\n   */\n  isSender(): boolean {\n    if (!this.activeCall || !this.currentUserId) return false;\n    return !this.isReceiver(this.activeCall);\n  }\n\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  isReceiverUser(): boolean {\n    if (!this.activeCall || !this.currentUserId) return false;\n    return this.isReceiver(this.activeCall);\n  }\n\n  /**\n   * Obtient le texte du statut de l'appel\n   */\n  getCallStatusText(): string {\n    if (!this.activeCall) return '';\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Sonnerie...';\n      case CallStatus.CONNECTED:\n        return 'Connecté';\n      case CallStatus.ENDED:\n        return 'Terminé';\n      case CallStatus.MISSED:\n        return 'Manqué';\n      case CallStatus.REJECTED:\n        return 'Rejeté';\n      case CallStatus.FAILED:\n        return 'Échec';\n      default:\n        return 'En cours...';\n    }\n  }\n\n  /**\n   * Obtient le nom de l'autre participant\n   */\n  getOtherParticipantName(): string {\n    if (!this.activeCall) return '';\n\n    return (\n      this.activeCall.recipient?.username ||\n      this.activeCall.caller?.username ||\n      'Utilisateur'\n    );\n  }\n\n  /**\n   * Obtient l'avatar de l'autre participant\n   */\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) return '/assets/images/default-avatar.png';\n\n    return (\n      this.activeCall.recipient?.image ||\n      this.activeCall.caller?.image ||\n      '/assets/images/default-avatar.png'\n    );\n  }\n\n  /**\n   * Obtient l'ID de l'utilisateur actuel depuis localStorage\n   */\n  private getCurrentUserId(): string | null {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        return null;\n      }\n      const user = JSON.parse(userString);\n      return user._id || user.id || user.userId || null;\n    } catch (error) {\n      this.logger.error('ActiveCall', 'Error getting current user ID:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  private isReceiver(call: Call): boolean {\n    if (!call || !this.currentUserId) return false;\n\n    const recipientId = call.recipient?.id || call.recipient?._id;\n    const callerId = call.caller?.id || call.caller?._id;\n\n    // L'utilisateur est le receiver si son ID correspond au recipient\n    const isRecipient = String(recipientId) === String(this.currentUserId);\n\n    this.logger.debug('ActiveCall', 'Checking if user is receiver:', {\n      currentUserId: this.currentUserId,\n      recipientId,\n      callerId,\n      isRecipient,\n    });\n\n    return isRecipient;\n  }\n}\n", "<!-- Interface d'appel actif -->\n<div\n  class=\"fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[9998]\"\n  *ngIf=\"activeCall && activeCall.status === 'CONNECTED'\"\n>\n  <div\n    class=\"bg-gray-900 rounded-2xl p-8 max-w-md w-full mx-4 text-center shadow-2xl border border-gray-700\"\n  >\n    <!-- Avatar et informations de l'utilisateur -->\n    <div class=\"mb-6\">\n      <div class=\"relative inline-block\">\n        <img\n          [src]=\"getOtherParticipantAvatar()\"\n          [alt]=\"getOtherParticipantName()\"\n          class=\"w-24 h-24 rounded-full mx-auto mb-4 border-4 border-blue-500 shadow-lg\"\n        />\n        <!-- Indicateur d'appel actif -->\n        <div\n          class=\"absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-900 animate-pulse\"\n        ></div>\n      </div>\n\n      <h2 class=\"text-2xl font-bold text-white mb-2\">\n        {{ getOtherParticipantName() }}\n      </h2>\n\n      <p class=\"text-blue-400 text-lg font-medium\">\n        {{ getCallStatusText() }}\n      </p>\n\n      <!-- Durée de l'appel -->\n      <p class=\"text-gray-400 text-sm mt-2\">\n        {{ callDuration }}\n      </p>\n    </div>\n\n    <!-- Zone vidéo pour les appels vidéo -->\n    <div *ngIf=\"isVideoCall()\" class=\"mb-6\">\n      <div class=\"relative bg-gray-800 rounded-lg overflow-hidden\">\n        <!-- Vidéo distante (principale) -->\n        <video\n          #remoteVideo\n          class=\"w-full h-48 object-cover\"\n          autoplay\n          playsinline\n          [muted]=\"false\"\n        ></video>\n\n        <!-- Vidéo locale (petite fenêtre) -->\n        <video\n          #localVideo\n          class=\"absolute top-2 right-2 w-20 h-15 object-cover rounded border-2 border-blue-500\"\n          autoplay\n          playsinline\n          [muted]=\"true\"\n        ></video>\n      </div>\n    </div>\n\n    <!-- Zone audio pour les appels audio -->\n    <div *ngIf=\"!isVideoCall()\" class=\"mb-6\">\n      <!-- Éléments vidéo cachés pour l'audio -->\n      <video\n        #remoteVideo\n        style=\"display: none\"\n        autoplay\n        playsinline\n        [muted]=\"false\"\n      ></video>\n      <video\n        #localVideo\n        style=\"display: none\"\n        autoplay\n        playsinline\n        [muted]=\"true\"\n      ></video>\n\n      <!-- Visualisation audio -->\n      <div class=\"flex justify-center items-center space-x-2 mb-4\">\n        <div class=\"w-2 h-8 bg-blue-500 rounded animate-pulse\"></div>\n        <div\n          class=\"w-2 h-12 bg-blue-400 rounded animate-pulse\"\n          style=\"animation-delay: 0.1s\"\n        ></div>\n        <div\n          class=\"w-2 h-6 bg-blue-500 rounded animate-pulse\"\n          style=\"animation-delay: 0.2s\"\n        ></div>\n        <div\n          class=\"w-2 h-10 bg-blue-400 rounded animate-pulse\"\n          style=\"animation-delay: 0.3s\"\n        ></div>\n        <div\n          class=\"w-2 h-4 bg-blue-500 rounded animate-pulse\"\n          style=\"animation-delay: 0.4s\"\n        ></div>\n      </div>\n    </div>\n\n    <!-- Contrôles d'appel -->\n    <div class=\"flex justify-center space-x-6 mt-8\">\n      <!-- Bouton Microphone -->\n      <button\n        (click)=\"toggleMicrophone()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\"\n        [class]=\"\n          isAudioMuted\n            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50'\n            : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'\n        \"\n        [title]=\"isAudioMuted ? 'Activer le micro' : 'Couper le micro'\"\n      >\n        <i\n          class=\"fas text-white text-xl\"\n          [class]=\"isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'\"\n        ></i>\n      </button>\n\n      <!-- Bouton Caméra (seulement pour appels vidéo) -->\n      <button\n        *ngIf=\"isVideoCall()\"\n        (click)=\"toggleCamera()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\"\n        [class]=\"\n          isVideoMuted\n            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50'\n            : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'\n        \"\n        [title]=\"isVideoMuted ? 'Activer la caméra' : 'Désactiver la caméra'\"\n      >\n        <i\n          class=\"fas text-white text-xl\"\n          [class]=\"isVideoMuted ? 'fa-video-slash' : 'fa-video'\"\n        ></i>\n      </button>\n\n      <!-- Bouton Haut-parleur -->\n      <button\n        (click)=\"toggleSpeaker()\"\n        class=\"w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg\"\n        [class]=\"\n          isSpeakerOn\n            ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'\n            : 'bg-gray-600 hover:bg-gray-700 shadow-gray-600/50'\n        \"\n        [title]=\"\n          isSpeakerOn ? 'Désactiver le haut-parleur' : 'Activer le haut-parleur'\n        \"\n      >\n        <i\n          class=\"fas text-white text-xl\"\n          [class]=\"isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'\"\n        ></i>\n      </button>\n\n      <!-- Bouton Raccrocher -->\n      <button\n        (click)=\"endCall()\"\n        class=\"w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg shadow-red-500/50\"\n        title=\"Raccrocher\"\n      >\n        <i class=\"fas fa-phone-slash text-white text-xl\"></i>\n      </button>\n    </div>\n  </div>\n</div>\n"], "mappings": "AAQA,SAASA,YAAY,QAAQ,MAAM;AAGnC,SAAeC,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;;;;;;;;;IC0BnEC,EAAA,CAAAC,cAAA,aAAwC;IAGpCD,EAAA,CAAAE,SAAA,oBAMS;IAUXF,EAAA,CAAAG,YAAA,EAAM;;;IAXFH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IASfL,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,UAAA,eAAc;;;;;IAMpBL,EAAA,CAAAC,cAAA,aAAyC;IAEvCD,EAAA,CAAAE,SAAA,oBAMS;IAUTF,EAAA,CAAAC,cAAA,cAA6D;IAC3DD,EAAA,CAAAE,SAAA,cAA6D;IAiB/DF,EAAA,CAAAG,YAAA,EAAM;;;IA7BJH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,UAAA,gBAAe;IAOfL,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAAK,UAAA,eAAc;;;;;;IA6ChBL,EAAA,CAAAC,cAAA,iBAUC;IARCD,EAAA,CAAAM,UAAA,mBAAAC,qEAAA;MAAAP,EAAA,CAAAQ,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAV,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAF,MAAA,CAAAG,YAAA,EAAc;IAAA,EAAC;IASxBb,EAAA,CAAAE,SAAA,YAGK;IACPF,EAAA,CAAAG,YAAA,EAAS;;;;IAXPH,EAAA,CAAAc,UAAA,CAAAC,MAAA,CAAAC,YAAA,wGAIC;IACDhB,EAAA,CAAAK,UAAA,UAAAU,MAAA,CAAAC,YAAA,+DAAqE;IAInEhB,EAAA,CAAAI,SAAA,GAAsD;IAAtDJ,EAAA,CAAAc,UAAA,CAAAC,MAAA,CAAAC,YAAA,iCAAsD;;;;;;IAnIhEhB,EAAA,CAAAC,cAAA,aAGC;IAOOD,EAAA,CAAAE,SAAA,aAIE;IAKJF,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,YAA+C;IAC7CD,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAG,YAAA,EAAK;IAELH,EAAA,CAAAC,cAAA,WAA6C;IAC3CD,EAAA,CAAAiB,MAAA,GACF;IAAAjB,EAAA,CAAAG,YAAA,EAAI;IAGJH,EAAA,CAAAC,cAAA,YAAsC;IACpCD,EAAA,CAAAiB,MAAA,IACF;IAAAjB,EAAA,CAAAG,YAAA,EAAI;IAINH,EAAA,CAAAkB,UAAA,KAAAC,yCAAA,kBAoBM;IAGNnB,EAAA,CAAAkB,UAAA,KAAAE,yCAAA,mBAqCM;IAGNpB,EAAA,CAAAC,cAAA,eAAgD;IAG5CD,EAAA,CAAAM,UAAA,mBAAAe,4DAAA;MAAArB,EAAA,CAAAQ,aAAA,CAAAc,IAAA;MAAA,MAAAC,OAAA,GAAAvB,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAW,OAAA,CAAAC,gBAAA,EAAkB;IAAA,EAAC;IAS5BxB,EAAA,CAAAE,SAAA,aAGK;IACPF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAkB,UAAA,KAAAO,4CAAA,qBAeS;IAGTzB,EAAA,CAAAC,cAAA,kBAWC;IAVCD,EAAA,CAAAM,UAAA,mBAAAoB,4DAAA;MAAA1B,EAAA,CAAAQ,aAAA,CAAAc,IAAA;MAAA,MAAAK,OAAA,GAAA3B,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAe,OAAA,CAAAC,aAAA,EAAe;IAAA,EAAC;IAWzB5B,EAAA,CAAAE,SAAA,aAGK;IACPF,EAAA,CAAAG,YAAA,EAAS;IAGTH,EAAA,CAAAC,cAAA,kBAIC;IAHCD,EAAA,CAAAM,UAAA,mBAAAuB,4DAAA;MAAA7B,EAAA,CAAAQ,aAAA,CAAAc,IAAA;MAAA,MAAAQ,OAAA,GAAA9B,EAAA,CAAAW,aAAA;MAAA,OAASX,EAAA,CAAAY,WAAA,CAAAkB,OAAA,CAAAC,OAAA,EAAS;IAAA,EAAC;IAInB/B,EAAA,CAAAE,SAAA,aAAqD;IACvDF,EAAA,CAAAG,YAAA,EAAS;;;;IAtJLH,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,UAAA,QAAA2B,MAAA,CAAAC,yBAAA,IAAAjC,EAAA,CAAAkC,aAAA,CAAmC,QAAAF,MAAA,CAAAG,uBAAA;IAWrCnC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoC,kBAAA,MAAAJ,MAAA,CAAAG,uBAAA,QACF;IAGEnC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoC,kBAAA,MAAAJ,MAAA,CAAAK,iBAAA,QACF;IAIErC,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAoC,kBAAA,MAAAJ,MAAA,CAAAM,YAAA,MACF;IAIItC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAA2B,MAAA,CAAAO,WAAA,GAAmB;IAuBnBvC,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAK,UAAA,UAAA2B,MAAA,CAAAO,WAAA,GAAoB;IA6CtBvC,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAc,UAAA,CAAAkB,MAAA,CAAAQ,YAAA,wGAIC;IACDxC,EAAA,CAAAK,UAAA,UAAA2B,MAAA,CAAAQ,YAAA,0CAA+D;IAI7DxC,EAAA,CAAAI,SAAA,GAAgE;IAAhEJ,EAAA,CAAAc,UAAA,CAAAkB,MAAA,CAAAQ,YAAA,2CAAgE;IAMjExC,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAK,UAAA,SAAA2B,MAAA,CAAAO,WAAA,GAAmB;IAoBpBvC,EAAA,CAAAI,SAAA,GAIC;IAJDJ,EAAA,CAAAc,UAAA,CAAAkB,MAAA,CAAAS,WAAA,2GAIC;IACDzC,EAAA,CAAAK,UAAA,UAAA2B,MAAA,CAAAS,WAAA,iEAEC;IAICzC,EAAA,CAAAI,SAAA,GAAyD;IAAzDJ,EAAA,CAAAc,UAAA,CAAAkB,MAAA,CAAAS,WAAA,qCAAyD;;;ADrInE,OAAM,MAAOC,mBAAmB;EAiB9BC,YAAoBC,WAAwB,EAAUC,MAAqB;IAAvD,KAAAD,WAAW,GAAXA,WAAW;IAAuB,KAAAC,MAAM,GAANA,MAAM;IAb5D,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAR,YAAY,GAAG,OAAO;IACtB,KAAAS,aAAa,GAAkB,IAAI;IAEnC;IACA,KAAAP,YAAY,GAAG,KAAK;IACpB,KAAAxB,YAAY,GAAG,KAAK;IACpB,KAAAyB,WAAW,GAAG,IAAI;IAEV,KAAAO,YAAY,GAAiB,IAAInD,YAAY,EAAE;IAC/C,KAAAoD,aAAa,GAAgB,IAAI;IAIvC,IAAI,CAACJ,MAAM,CAACK,IAAI,CAAC,YAAY,EAAE,qCAAqC,CAAC;EACvE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACN,MAAM,CAACK,IAAI,CAAC,YAAY,EAAE,wCAAwC,CAAC;IAExE;IACA,IAAI,CAACH,aAAa,GAAG,IAAI,CAACK,gBAAgB,EAAE;IAE5C;IACA,IAAI,CAACJ,YAAY,CAACK,GAAG,CACnB,IAAI,CAACT,WAAW,CAACU,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC9C,IAAIA,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAKC,SAAS,EAAE;QACvC,IAAI,CAACX,UAAU,GAAG,IAAI;QACtB,IAAI,CAACY,aAAa,EAAE;QACpB;;MAGF;MACA,IAAI,CAACZ,UAAU,GAAGU,IAAI;MAEtB,IAAIA,IAAI,CAACG,MAAM,KAAK5D,UAAU,CAAC6D,SAAS,EAAE;QACxC,IAAI,CAACf,MAAM,CAACK,IAAI,CAAC,YAAY,EAAE,kCAAkC,CAAC;QAClE,IAAI,CAACW,cAAc,EAAE;OACtB,MAAM;QACL,IAAI,CAAChB,MAAM,CAACiB,KAAK,CACf,YAAY,EACZ,uCAAuC,CACxC;QACD,IAAI,CAACJ,aAAa,EAAE;;MAGtB,MAAMK,QAAQ,GAAG,IAAI,CAACC,UAAU,CAACR,IAAI,CAAC,GAAG,UAAU,GAAG,QAAQ;MAC9DS,OAAO,CAACC,GAAG,CAAC,6CAA6CH,QAAQ,GAAG,EAAE;QACpEI,MAAM,EAAEX,IAAI,CAACY,EAAE;QACfT,MAAM,EAAEH,IAAI,CAACG,MAAM;QACnBZ,aAAa,EAAE,IAAI,CAACA,aAAa;QACjCgB,QAAQ,EAAEA;OACX,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEAM,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,EAAE;MACvC,IAAI,CAAC3B,WAAW,CAAC4B,gBAAgB,CAC/B,IAAI,CAACF,UAAU,CAACG,aAAa,EAC7B,IAAI,CAACF,WAAW,CAACE,aAAa,CAC/B;;EAEL;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC1B,YAAY,CAAC2B,WAAW,EAAE;IAC/B,IAAI,CAACjB,aAAa,EAAE;EACtB;EAEA;;;EAGQG,cAAcA,CAAA;IACpB,IAAI,CAACZ,aAAa,GAAG,IAAI2B,IAAI,EAAE;IAC/B,IAAI,CAACC,gBAAgB,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,IAAI,CAAC7B,aAAa,EAAE;QACtB,MAAM8B,GAAG,GAAG,IAAIH,IAAI,EAAE;QACtB,MAAMI,IAAI,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAG,IAAI,CAAChC,aAAa,CAACgC,OAAO,EAAE;QACzD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,KAAK,CAAC;QACxC,MAAMK,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEJ,IAAI,GAAG,KAAK,GAAI,IAAI,CAAC;QACjD,IAAI,CAAC1C,YAAY,GAAG,GAAG4C,OAAO,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAClEC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQ7B,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACmB,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;IAE9B,IAAI,CAACvC,YAAY,GAAG,OAAO;IAC3B,IAAI,CAACW,aAAa,GAAG,IAAI;EAC3B;EAEA;;;EAGAzB,gBAAgBA,CAAA;IACd,IAAI,CAACgB,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCyB,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAAC1B,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;EACrE;EAEA;;;EAGA3B,YAAYA,CAAA;IACV,IAAI,CAACG,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCiD,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAAClD,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;EACjE;EAEA;;;EAGAY,aAAaA,CAAA;IACX,IAAI,CAACa,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpCwB,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACzB,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC;EAC1D;EAEA;;;EAGAV,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAACe,UAAU,EAAE;IAEtBmB,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAACpB,UAAU,CAACsB,EAAE,CAAC;IAE/C,IAAI,CAACxB,WAAW,CAACb,OAAO,CAAC,IAAI,CAACe,UAAU,CAACsB,EAAE,CAAC,CAACb,SAAS,CAAC;MACrDkC,IAAI,EAAEA,CAAA,KAAK;QACTxB,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACxC,CAAC;MACDwB,KAAK,EAAGA,KAAU,IAAI;QACpBzB,OAAO,CAACyB,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;KACD,CAAC;EACJ;EAEA;;;EAGAnD,WAAWA,CAAA;IACT,OAAO,IAAI,CAACO,UAAU,EAAE6C,IAAI,KAAK7F,QAAQ,CAAC8F,KAAK;EACjD;EAEA;;;EAGAC,QAAQA,CAAA;IACN,IAAI,CAAC,IAAI,CAAC/C,UAAU,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE,OAAO,KAAK;IACzD,OAAO,CAAC,IAAI,CAACiB,UAAU,CAAC,IAAI,CAAClB,UAAU,CAAC;EAC1C;EAEA;;;EAGAgD,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAChD,UAAU,IAAI,CAAC,IAAI,CAACC,aAAa,EAAE,OAAO,KAAK;IACzD,OAAO,IAAI,CAACiB,UAAU,CAAC,IAAI,CAAClB,UAAU,CAAC;EACzC;EAEA;;;EAGAT,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACS,UAAU,EAAE,OAAO,EAAE;IAE/B,QAAQ,IAAI,CAACA,UAAU,CAACa,MAAM;MAC5B,KAAK5D,UAAU,CAACgG,OAAO;QACrB,OAAO,aAAa;MACtB,KAAKhG,UAAU,CAAC6D,SAAS;QACvB,OAAO,UAAU;MACnB,KAAK7D,UAAU,CAACiG,KAAK;QACnB,OAAO,SAAS;MAClB,KAAKjG,UAAU,CAACkG,MAAM;QACpB,OAAO,QAAQ;MACjB,KAAKlG,UAAU,CAACmG,QAAQ;QACtB,OAAO,QAAQ;MACjB,KAAKnG,UAAU,CAACoG,MAAM;QACpB,OAAO,OAAO;MAChB;QACE,OAAO,aAAa;;EAE1B;EAEA;;;EAGAhE,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACW,UAAU,EAAE,OAAO,EAAE;IAE/B,OACE,IAAI,CAACA,UAAU,CAACsD,SAAS,EAAEC,QAAQ,IACnC,IAAI,CAACvD,UAAU,CAACwD,MAAM,EAAED,QAAQ,IAChC,aAAa;EAEjB;EAEA;;;EAGApE,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAACa,UAAU,EAAE,OAAO,mCAAmC;IAEhE,OACE,IAAI,CAACA,UAAU,CAACsD,SAAS,EAAEG,KAAK,IAChC,IAAI,CAACzD,UAAU,CAACwD,MAAM,EAAEC,KAAK,IAC7B,mCAAmC;EAEvC;EAEA;;;EAGQnD,gBAAgBA,CAAA;IACtB,IAAI;MACF,MAAMoD,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAI,CAACF,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtE,OAAO,IAAI;;MAEb,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnC,OAAOG,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACvC,EAAE,IAAIuC,IAAI,CAACI,MAAM,IAAI,IAAI;KAClD,CAAC,OAAOrB,KAAK,EAAE;MACd,IAAI,CAAC7C,MAAM,CAAC6C,KAAK,CAAC,YAAY,EAAE,gCAAgC,EAAEA,KAAK,CAAC;MACxE,OAAO,IAAI;;EAEf;EAEA;;;EAGQ1B,UAAUA,CAACR,IAAU;IAC3B,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACT,aAAa,EAAE,OAAO,KAAK;IAE9C,MAAMiE,WAAW,GAAGxD,IAAI,CAAC4C,SAAS,EAAEhC,EAAE,IAAIZ,IAAI,CAAC4C,SAAS,EAAEU,GAAG;IAC7D,MAAMG,QAAQ,GAAGzD,IAAI,CAAC8C,MAAM,EAAElC,EAAE,IAAIZ,IAAI,CAAC8C,MAAM,EAAEQ,GAAG;IAEpD;IACA,MAAMI,WAAW,GAAGC,MAAM,CAACH,WAAW,CAAC,KAAKG,MAAM,CAAC,IAAI,CAACpE,aAAa,CAAC;IAEtE,IAAI,CAACF,MAAM,CAACiB,KAAK,CAAC,YAAY,EAAE,+BAA+B,EAAE;MAC/Df,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCiE,WAAW;MACXC,QAAQ;MACRC;KACD,CAAC;IAEF,OAAOA,WAAW;EACpB;;;uBAlQWxE,mBAAmB,EAAA1C,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;YAAnB9E,mBAAmB;MAAA+E,SAAA;MAAAC,SAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCjBhC5H,EAAA,CAAAkB,UAAA,IAAA4G,kCAAA,mBAoKM;;;UAlKH9H,EAAA,CAAAK,UAAA,SAAAwH,GAAA,CAAA/E,UAAA,IAAA+E,GAAA,CAAA/E,UAAA,CAAAa,MAAA,iBAAqD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import { Component, OnInit, On<PERSON><PERSON>roy } from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { IncomingCall, CallType } from '../../models/message.model';

@Component({
  selector: 'app-incoming-call',
  templateUrl: './incoming-call.component.html',
  styleUrls: ['./incoming-call.component.css'],
})
export class IncomingCallComponent implements OnInit, OnDestroy {
  incomingCall: IncomingCall | null = null;
  isProcessing: boolean = false;
  currentUserId: string | null = null;

  private subscriptions: Subscription[] = [];

  // Exposer les énums au template
  CallType = CallType;

  constructor(
    private callService: CallService,
    private logger: LoggerService
  ) {}

  ngOnInit(): void {
    // Obtenir l'ID de l'utilisateur actuel
    this.currentUserId = this.getCurrentUserId();

    console.log(
      '🔍 [IncomingCall] Component initialized with currentUserId:',
      this.currentUserId
    );

    // S'abonner aux appels entrants
    const incomingCallSub = this.callService.incomingCall$.subscribe(
      (call: any) => {
        console.log('📞 [IncomingCall] Received call data:', {
          call,
          currentUserId: this.currentUserId,
          hasCall: !!call,
          callerId: call?.caller?.id || call?.caller?._id,
          recipientId: call?.recipient?.id || call?.recipient?._id,
        });

        if (!call) {
          console.log('📞 [IncomingCall] No call data, hiding template');
          this.incomingCall = null;
          return;
        }

        // TEMPORAIRE : Afficher tous les appels pour diagnostiquer
        console.log(
          '📞 [IncomingCall] DIAGNOSTIC MODE - Showing all incoming calls'
        );
        this.incomingCall = call;
        this.isProcessing = false;

        console.log(
          '📞 [IncomingCall] Template should now be visible for call:',
          {
            callId: call.id,
            caller: call.caller?.username,
            type: call.type,
          }
        );

        // Vérifier si l'utilisateur actuel est le receiver (pas le sender)
        // if (call && this.isReceiver(call)) {
        //   this.incomingCall = call;
        //   this.isProcessing = false;

        //   console.log(
        //     '📞 [IncomingCall] Incoming call received for receiver:',
        //     {
        //       callId: call.id,
        //       caller: call.caller?.username,
        //       type: call.type,
        //       currentUserId: this.currentUserId,
        //       recipientId: call.recipient?.id || call.recipient?._id,
        //     }
        //   );
        // } else if (call) {
        //   // Si l'utilisateur est le sender, ne pas afficher le template
        //   console.log(
        //     '📞 [IncomingCall] Call initiated by current user (sender), hiding template'
        //   );
        //   this.incomingCall = null;
        // } else {
        //   this.incomingCall = null;
        // }
      }
    );

    this.subscriptions.push(incomingCallSub);
  }

  ngOnDestroy(): void {
    this.subscriptions.forEach((sub) => sub.unsubscribe());
  }

  /**
   * Accepte l'appel entrant
   */
  acceptCall(): void {
    if (!this.incomingCall || this.isProcessing) return;

    console.log('✅ [IncomingCall] Accepting call:', this.incomingCall.id);
    this.isProcessing = true;

    this.callService.acceptCall(this.incomingCall).subscribe({
      next: (call: any) => {
        console.log('✅ [IncomingCall] Call accepted successfully:', call);
        this.isProcessing = false;
        // L'interface d'appel actif va automatiquement s'afficher
      },
      error: (error: any) => {
        console.error('❌ [IncomingCall] Error accepting call:', error);
        this.logger.error('Error accepting call:', error);
        this.isProcessing = false;

        // Optionnel : Afficher un message d'erreur à l'utilisateur
        alert("Erreur lors de l'acceptation de l'appel. Veuillez réessayer.");
      },
    });
  }

  /**
   * Rejette l'appel entrant
   */
  rejectCall(): void {
    if (!this.incomingCall || this.isProcessing) return;

    console.log('❌ [IncomingCall] Rejecting call:', this.incomingCall.id);
    this.isProcessing = true;

    this.callService
      .rejectCall(this.incomingCall.id, 'User rejected')
      .subscribe({
        next: (result: any) => {
          console.log('✅ [IncomingCall] Call rejected successfully:', result);
          this.isProcessing = false;
        },
        error: (error: any) => {
          console.error('❌ [IncomingCall] Error rejecting call:', error);
          this.logger.error('Error rejecting call:', error);
          this.isProcessing = false;
        },
      });
  }

  /**
   * Obtient le nom de l'appelant
   */
  getCallerName(): string {
    return this.incomingCall?.caller?.username || 'Utilisateur inconnu';
  }

  /**
   * Obtient l'avatar de l'appelant
   */
  getCallerAvatar(): string {
    return (
      this.incomingCall?.caller?.image || '/assets/images/default-avatar.png'
    );
  }

  /**
   * Obtient le type d'appel (audio/vidéo)
   */
  getCallTypeText(): string {
    if (!this.incomingCall) return '';

    return this.incomingCall.type === CallType.VIDEO
      ? 'Appel vidéo'
      : 'Appel audio';
  }

  /**
   * Obtient l'icône du type d'appel
   */
  getCallTypeIcon(): string {
    if (!this.incomingCall) return 'fa-phone';

    return this.incomingCall.type === CallType.VIDEO ? 'fa-video' : 'fa-phone';
  }

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(): boolean {
    return this.incomingCall?.type === CallType.VIDEO;
  }

  /**
   * Obtient l'ID de l'utilisateur actuel depuis localStorage
   */
  private getCurrentUserId(): string | null {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString === 'null' || userString === 'undefined') {
        return null;
      }
      const user = JSON.parse(userString);
      return user._id || user.id || user.userId || null;
    } catch (error) {
      console.error('Error getting current user ID:', error);
      return null;
    }
  }

  /**
   * Vérifie si l'utilisateur actuel est le receiver de l'appel
   */
  private isReceiver(call: IncomingCall): boolean {
    if (!call || !this.currentUserId) return false;

    const recipientId = call.recipient?.id || call.recipient?._id;
    const callerId = call.caller?.id || call.caller?._id;

    // L'utilisateur est le receiver si son ID correspond au recipient
    const isRecipient = String(recipientId) === String(this.currentUserId);

    console.log('📞 [IncomingCall] Checking if user is receiver:', {
      currentUserId: this.currentUserId,
      recipientId,
      callerId,
      isRecipient,
    });

    return isRecipient;
  }
}

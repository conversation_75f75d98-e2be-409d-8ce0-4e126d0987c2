{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\nexport let CallModule = class CallModule {};\nCallModule = __decorate([NgModule({\n  declarations: [ActiveCallComponent, IncomingCallComponent],\n  imports: [CommonModule],\n  exports: [ActiveCallComponent, IncomingCallComponent]\n})], CallModule);", "map": {"version": 3, "names": ["NgModule", "CommonModule", "ActiveCallComponent", "IncomingCallComponent", "CallModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\call\\call.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ActiveCallComponent } from '../active-call/active-call.component';\nimport { IncomingCallComponent } from '../incoming-call/incoming-call.component';\n\n@NgModule({\n  declarations: [ActiveCallComponent, IncomingCallComponent],\n  imports: [CommonModule],\n  exports: [ActiveCallComponent, IncomingCallComponent],\n})\nexport class CallModule {}\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,sCAAsC;AAC1E,SAASC,qBAAqB,QAAQ,0CAA0C;AAOzE,WAAMC,UAAU,GAAhB,MAAMA,UAAU,GAAG;AAAbA,UAAU,GAAAC,UAAA,EALtBL,QAAQ,CAAC;EACRM,YAAY,EAAE,CAACJ,mBAAmB,EAAEC,qBAAqB,CAAC;EAC1DI,OAAO,EAAE,CAACN,YAAY,CAAC;EACvBO,OAAO,EAAE,CAACN,mBAAmB,EAAEC,qBAAqB;CACrD,CAAC,C,EACWC,UAAU,CAAG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
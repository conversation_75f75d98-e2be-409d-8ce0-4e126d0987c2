{"ast": null, "code": "import { BehaviorSubject, Observable, of, throwError, retry, EMPTY } from 'rxjs';\nimport { map, catchError, tap, filter } from 'rxjs/operators';\nimport { MessageType, CallType, CallStatus } from '../models/message.model';\nimport { GET_CONVERSATIONS_QUERY, GET_NOTIFICATIONS_QUERY, NOTIFICATION_SUBSCRIPTION, GET_CONVERSATION_QUERY, SEND_MESSAGE_MUTATION, MARK_AS_READ_MUTATION, MESSAGE_SENT_SUBSCRIPTION, USER_STATUS_SUBSCRIPTION, GET_USER_QUERY, GET_ALL_USER_QUERY, CONVERSATION_UPDATED_SUBSCRIPTION, SEARCH_MESSAGES_QUERY, GET_UNREAD_MESSAGES_QUERY, SET_USER_ONLINE_MUTATION, SET_USER_OFFLINE_MUTATION, START_TYPING_MUTATION, STOP_TYPING_MUTATION, TYPING_INDICATOR_SUBSCRIPTION, GET_CURRENT_USER_QUERY, REACT_TO_MESSAGE_MUTATION, FORWARD_MESSAGE_MUTATION, PIN_MESSAGE_MUTATION, CREATE_GROUP_MUTATION, UPDATE_GROUP_MUTATION, DELETE_GROUP_MUTATION, LEAVE_GROUP_MUTATION, GET_GROUP_QUERY, GET_USER_GROUPS_QUERY, EDIT_MESSAGE_MUTATION, DELETE_MESSAGE_MUTATION, GET_MESSAGES_QUERY, GET_NOTIFICATIONS_ATTACHAMENTS, MARK_NOTIFICATION_READ_MUTATION, NOTIFICATIONS_READ_SUBSCRIPTION, CREATE_CONVERSATION_MUTATION, DELETE_NOTIFICATION_MUTATION, DELETE_MULTIPLE_NOTIFICATIONS_MUTATION, DELETE_ALL_NOTIFICATIONS_MUTATION,\n// Requêtes et mutations pour les appels\nCALL_HISTORY_QUERY, CALL_DETAILS_QUERY, CALL_STATS_QUERY, SEND_CALL_SIGNAL_MUTATION, CALL_SIGNAL_SUBSCRIPTION, INCOMING_CALL_SUBSCRIPTION, GET_VOICE_MESSAGES_QUERY } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class MessageService {\n  constructor(apollo, logger, zone) {\n    this.apollo = apollo;\n    this.logger = logger;\n    this.zone = zone;\n    // État partagé\n    this.activeConversation = new BehaviorSubject(null);\n    this.notifications = new BehaviorSubject([]);\n    this.notificationCache = new Map();\n    this.notificationCount = new BehaviorSubject(0);\n    this.onlineUsers = new Map();\n    this.subscriptions = [];\n    this.CACHE_DURATION = 300000;\n    this.lastFetchTime = 0;\n    // Propriétés pour les appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    this.localStream = null;\n    this.remoteStream = null;\n    this.peerConnection = null;\n    // Observables publics pour les appels\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    this.localStream$ = new BehaviorSubject(null);\n    this.remoteStream$ = new BehaviorSubject(null);\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }]\n    };\n    this.usersCache = [];\n    // Pagination metadata for user list\n    this.currentUserPagination = {\n      totalCount: 0,\n      totalPages: 0,\n      currentPage: 1,\n      hasNextPage: false,\n      hasPreviousPage: false\n    };\n    // Observables publics\n    this.activeConversation$ = this.activeConversation.asObservable();\n    this.notifications$ = this.notifications.asObservable();\n    this.notificationCount$ = this.notificationCount.asObservable();\n    // Propriétés pour la gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    this.muted = false;\n    // --------------------------------------------------------------------------\n    // Section 2: Méthodes pour les Notifications\n    // --------------------------------------------------------------------------\n    // Propriétés pour la pagination des notifications\n    this.notificationPagination = {\n      currentPage: 1,\n      limit: 10,\n      hasMoreNotifications: true\n    };\n    this.toSafeISOString = date => {\n      if (!date) return undefined;\n      return typeof date === 'string' ? date : date.toISOString();\n    };\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  loadNotificationsFromLocalStorage() {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications);\n        this.notificationCache.clear();\n        notifications.forEach(notification => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  initSubscriptions() {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n\n    this.subscribeToUserStatus();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    return this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.incomingCall) {\n        return null;\n      }\n      // Gérer l'appel entrant\n      this.handleIncomingCall(data.incomingCall);\n      return data.incomingCall;\n    }), catchError(error => {\n      this.logger.error('Error in incoming call subscription', error);\n      return of(null);\n    }));\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  preloadSounds() {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  loadSound(name, path) {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name, loop = false) {\n    if (this.muted) {\n      return;\n    }\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      sound.loop = loop;\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch(error => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted) {\n    this.muted = muted;\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted() {\n    return this.muted;\n  }\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound() {\n    console.log('MessageService: Tentative de lecture du son de notification');\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n      console.log('MessageService: Son de notification mélodieux généré avec succès');\n    } catch (error) {\n      console.error('MessageService: Erreur lors de la génération du son:', error);\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch(err => {\n          console.error('MessageService: Erreur lors de la lecture du fichier son:', err);\n        });\n      } catch (audioError) {\n        console.error('MessageService: Exception lors de la lecture du fichier son:', audioError);\n      }\n    }\n  }\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  playNotificationMelody1(audioContext) {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  playNotificationMelody2(audioContext) {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  playNotificationMelody3(audioContext) {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n  // 🎵 SON 4: Triple note (Discord style)\n  playNotificationMelody4(audioContext) {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n  // 🎵 SON 5: Cloche douce (Slack style)\n  playNotificationMelody5(audioContext) {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  playNotificationTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.3, audioContext.currentTime + startTime + 0.02);\n    gainNode.gain.linearRampToValueAtTime(0.2, audioContext.currentTime + startTime + duration * 0.7);\n    gainNode.gain.linearRampToValueAtTime(0, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  playBellTone(audioContext, startTime, frequency, duration) {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(frequency, audioContext.currentTime + startTime);\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(0.4, audioContext.currentTime + startTime + 0.01);\n    gainNode.gain.exponentialRampToValueAtTime(0.01, audioContext.currentTime + startTime + duration);\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl) {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n      audio.onended = () => {\n        resolve();\n      };\n      audio.onerror = error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n      audio.play().catch(error => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages() {\n    this.logger.debug('[MessageService] Getting voice messages');\n    return this.apollo.watchQuery({\n      query: GET_VOICE_MESSAGES_QUERY,\n      fetchPolicy: 'network-only' // Ne pas utiliser le cache pour cette requête\n    }).valueChanges.pipe(map(result => {\n      const voiceMessages = result.data?.getVoiceMessages || [];\n      this.logger.debug(`[MessageService] Retrieved ${voiceMessages.length} voice messages`);\n      return voiceMessages;\n    }), catchError(error => {\n      this.logger.error('[MessageService] Error fetching voice messages:', error);\n      return throwError(() => new Error('Failed to fetch voice messages'));\n    }));\n  }\n  // Message methods\n  getMessages(senderId, receiverId, conversationId, page = 1, limit = 10) {\n    return this.apollo.watchQuery({\n      query: GET_MESSAGES_QUERY,\n      variables: {\n        senderId,\n        receiverId,\n        conversationId,\n        limit,\n        page\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const messages = result.data?.getMessages || [];\n      return messages.map(msg => this.normalizeMessage(msg));\n    }), catchError(error => {\n      this.logger.error('Error fetching messages:', error);\n      return throwError(() => new Error('Failed to fetch messages'));\n    }));\n  }\n  editMessage(messageId, newContent) {\n    return this.apollo.mutate({\n      mutation: EDIT_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        newContent\n      }\n    }).pipe(map(result => {\n      if (!result.data?.editMessage) {\n        throw new Error('Failed to edit message');\n      }\n      return this.normalizeMessage(result.data.editMessage);\n    }), catchError(error => {\n      this.logger.error('Error editing message:', error);\n      return throwError(() => new Error('Failed to edit message'));\n    }));\n  }\n  deleteMessage(messageId) {\n    return this.apollo.mutate({\n      mutation: DELETE_MESSAGE_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.deleteMessage) {\n        throw new Error('Failed to delete message');\n      }\n      return this.normalizeMessage(result.data.deleteMessage);\n    }), catchError(error => {\n      this.logger.error('Error deleting message:', error);\n      return throwError(() => new Error('Failed to delete message'));\n    }));\n  }\n  markMessageAsRead(messageId) {\n    return this.apollo.mutate({\n      mutation: MARK_AS_READ_MUTATION,\n      variables: {\n        messageId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.markMessageAsRead) throw new Error('Failed to mark message as read');\n      return {\n        ...result.data.markMessageAsRead,\n        readAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error marking message as read:', error);\n      return throwError(() => new Error('Failed to mark message as read'));\n    }));\n  }\n  reactToMessage(messageId, emoji) {\n    return this.apollo.mutate({\n      mutation: REACT_TO_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        emoji\n      }\n    }).pipe(map(result => {\n      if (!result.data?.reactToMessage) throw new Error('Failed to react to message');\n      return result.data.reactToMessage;\n    }), catchError(error => {\n      console.error('Error reacting to message:', error);\n      return throwError(() => new Error('Failed to react to message'));\n    }));\n  }\n  forwardMessage(messageId, conversationIds) {\n    return this.apollo.mutate({\n      mutation: FORWARD_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationIds\n      }\n    }).pipe(map(result => {\n      if (!result.data?.forwardMessage) throw new Error('Failed to forward message');\n      return result.data.forwardMessage.map(msg => ({\n        ...msg,\n        timestamp: msg.timestamp ? this.normalizeDate(msg.timestamp) : new Date()\n      }));\n    }), catchError(error => {\n      console.error('Error forwarding message:', error);\n      return throwError(() => new Error('Failed to forward message'));\n    }));\n  }\n  pinMessage(messageId, conversationId) {\n    return this.apollo.mutate({\n      mutation: PIN_MESSAGE_MUTATION,\n      variables: {\n        messageId,\n        conversationId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.pinMessage) throw new Error('Failed to pin message');\n      return {\n        ...result.data.pinMessage,\n        pinnedAt: new Date()\n      };\n    }), catchError(error => {\n      console.error('Error pinning message:', error);\n      return throwError(() => new Error('Failed to pin message'));\n    }));\n  }\n  searchMessages(query, conversationId, filters = {}) {\n    return this.apollo.watchQuery({\n      query: SEARCH_MESSAGES_QUERY,\n      variables: {\n        query,\n        conversationId,\n        ...filters,\n        dateFrom: this.toSafeISOString(filters.dateFrom),\n        dateTo: this.toSafeISOString(filters.dateTo)\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.searchMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error searching messages:', error);\n      return throwError(() => new Error('Failed to search messages'));\n    }));\n  }\n  getUnreadMessages(userId) {\n    return this.apollo.watchQuery({\n      query: GET_UNREAD_MESSAGES_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => result.data?.getUnreadMessages?.map(msg => ({\n      ...msg,\n      timestamp: this.safeDate(msg.timestamp),\n      sender: this.normalizeUser(msg.sender)\n    })) || []), catchError(error => {\n      console.error('Error fetching unread messages:', error);\n      return throwError(() => new Error('Failed to fetch unread messages'));\n    }));\n  }\n  setActiveConversation(conversationId) {\n    this.activeConversation.next(conversationId);\n  }\n  getConversations() {\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATIONS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const conversations = result.data?.getConversations || [];\n      return conversations.map(conv => this.normalizeConversation(conv));\n    }), catchError(error => {\n      console.error('Error fetching conversations:', error);\n      return throwError(() => new Error('Failed to load conversations'));\n    }));\n  }\n  getConversation(conversationId, limit, page) {\n    this.logger.info(`[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`);\n    const variables = {\n      conversationId\n    };\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(`[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`);\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(`[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`);\n    return this.apollo.watchQuery({\n      query: GET_CONVERSATION_QUERY,\n      variables: variables,\n      fetchPolicy: 'network-only',\n      errorPolicy: 'all'\n    }).valueChanges.pipe(retry(2),\n    // Réessayer 2 fois en cas d'erreur\n    map(result => {\n      this.logger.debug(`[MessageService] Conversation response received:`, result);\n      const conv = result.data?.getConversation;\n      if (!conv) {\n        this.logger.error(`[MessageService] Conversation not found: ${conversationId}`);\n        throw new Error('Conversation not found');\n      }\n      this.logger.debug(`[MessageService] Normalizing conversation: ${conversationId}`);\n      const normalizedConversation = this.normalizeConversation(conv);\n      this.logger.info(`[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${normalizedConversation.participants?.length || 0}, messages: ${normalizedConversation.messages?.length || 0}`);\n      return normalizedConversation;\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error fetching conversation:`, error);\n      return throwError(() => new Error('Failed to load conversation'));\n    }));\n  }\n  createConversation(userId) {\n    this.logger.info(`[MessageService] Creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to create a conversation'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_CONVERSATION_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      this.logger.debug(`[MessageService] Conversation creation response:`, result);\n      const conversation = result.data?.createConversation;\n      if (!conversation) {\n        this.logger.error(`[MessageService] Failed to create conversation with user: ${userId}`);\n        throw new Error('Failed to create conversation');\n      }\n      try {\n        const normalizedConversation = this.normalizeConversation(conversation);\n        this.logger.info(`[MessageService] Conversation created successfully: ${normalizedConversation.id}`);\n        return normalizedConversation;\n      } catch (error) {\n        this.logger.error(`[MessageService] Error normalizing created conversation:`, error);\n        throw new Error('Error processing created conversation');\n      }\n    }), catchError(error => {\n      this.logger.error(`[MessageService] Error creating conversation with user ${userId}:`, error);\n      return throwError(() => new Error(`Failed to create conversation: ${error.message}`));\n    }));\n  }\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId) {\n    this.logger.info(`[MessageService] Getting or creating conversation with user: ${userId}`);\n    if (!userId) {\n      this.logger.error(`[MessageService] Cannot get/create conversation: userId is undefined`);\n      return throwError(() => new Error('User ID is required to get/create a conversation'));\n    }\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(map(conversations => {\n      // Récupérer l'ID de l'utilisateur actuel\n      const currentUserId = this.getCurrentUserId();\n      // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n      const existingConversation = conversations.find(conv => {\n        if (conv.isGroup) return false;\n        // Vérifier si la conversation contient les deux utilisateurs\n        const participantIds = conv.participants?.map(p => p.id || p._id) || [];\n        return participantIds.includes(userId) && participantIds.includes(currentUserId);\n      });\n      if (existingConversation) {\n        this.logger.info(`[MessageService] Found existing conversation: ${existingConversation.id}`);\n        return existingConversation;\n      }\n      // Si aucune conversation n'est trouvée, en créer une nouvelle\n      throw new Error('No existing conversation found');\n    }), catchError(error => {\n      this.logger.info(`[MessageService] No existing conversation found, creating new one: ${error.message}`);\n      return this.createConversation(userId);\n    }));\n  }\n  getNotifications(refresh = false, page = 1, limit = 10) {\n    this.logger.info('MessageService', `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`);\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY\n    });\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug('MessageService', 'Resetting pagination due to refresh');\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug('MessageService', `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`);\n    return this.apollo.watchQuery({\n      query: GET_NOTIFICATIONS_QUERY,\n      variables: {\n        page: page,\n        limit: limit\n      },\n      fetchPolicy: refresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Notifications response received');\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      const notifications = result.data?.getUserNotifications || [];\n      this.logger.debug('MessageService', `Received ${notifications.length} notifications from server for page ${page}`);\n      // Vérifier s'il y a plus de notifications à charger\n      this.notificationPagination.hasMoreNotifications = notifications.length >= limit;\n      if (notifications.length === 0) {\n        this.logger.info('MessageService', 'No notifications received from server');\n        this.notificationPagination.hasMoreNotifications = false;\n      }\n      // Filtrer les notifications supprimées\n      const filteredNotifications = notifications.filter(notif => !deletedNotificationIds.has(notif.id));\n      this.logger.debug('MessageService', `Filtered out ${notifications.length - filteredNotifications.length} deleted notifications`);\n      // Afficher les notifications reçues pour le débogage\n      filteredNotifications.forEach((notif, index) => {\n        console.log(`Notification ${index + 1} (page ${page}):`, {\n          id: notif.id || notif._id,\n          type: notif.type,\n          content: notif.content,\n          isRead: notif.isRead\n        });\n      });\n      // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n      // Mettre à jour le cache avec les nouvelles notifications\n      this.updateCache(filteredNotifications);\n      // Récupérer toutes les notifications du cache et les TRIER\n      const cachedNotifications = Array.from(this.notificationCache.values());\n      // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n      const sortedNotifications = this.sortNotificationsByDate(cachedNotifications);\n      console.log(`📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`);\n      // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n      this.notifications.next(sortedNotifications);\n      // Mettre à jour le compteur de notifications non lues\n      this.updateUnreadCount();\n      // Sauvegarder les notifications dans le localStorage\n      this.saveNotificationsToLocalStorage();\n      return cachedNotifications;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error loading notifications:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      return throwError(() => new Error('Failed to load notifications'));\n    }));\n  }\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  getDeletedNotificationIds() {\n    try {\n      const deletedIds = new Set();\n      const savedNotifications = localStorage.getItem('notifications');\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(JSON.parse(savedNotifications).map(n => n.id));\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications = this.apollo.client.readQuery({\n        query: GET_NOTIFICATIONS_QUERY\n      })?.getUserNotifications || [];\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach(notification => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n      return deletedIds;\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la récupération des IDs de notifications supprimées:', error);\n      return new Set();\n    }\n  }\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications() {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications() {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(false, nextPage, this.notificationPagination.limit);\n  }\n  getNotificationById(id) {\n    return this.notifications$.pipe(map(notifications => notifications.find(n => n.id === id)), catchError(error => {\n      this.logger.error('Error finding notification:', error);\n      return throwError(() => new Error('Failed to find notification'));\n    }));\n  }\n  getNotificationCount() {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId) {\n    return this.apollo.query({\n      query: GET_NOTIFICATIONS_ATTACHAMENTS,\n      variables: {\n        id: notificationId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => result.data?.getNotificationAttachments || []), catchError(error => {\n      this.logger.error('Error fetching notification attachments:', error);\n      return throwError(() => new Error('Failed to fetch attachments'));\n    }));\n  }\n  getUnreadNotifications() {\n    return this.notifications$.pipe(map(notifications => notifications.filter(n => !n.isRead)));\n  }\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(notificationId) {\n    this.logger.debug('MessageService', `Suppression de la notification ${notificationId}`);\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n    // Appeler le backend pour supprimer la notification\n    return this.apollo.mutate({\n      mutation: DELETE_NOTIFICATION_MUTATION,\n      variables: {\n        notificationId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteNotification;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de la notification', {\n      success: true,\n      message: 'Notification supprimée localement (erreur serveur)'\n    })));\n  }\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  saveNotificationsToLocalStorage() {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug('MessageService', 'Notifications sauvegardées localement');\n    } catch (error) {\n      this.logger.error('MessageService', 'Erreur lors de la sauvegarde des notifications:', error);\n    }\n  }\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications() {\n    this.logger.debug('MessageService', 'Suppression de toutes les notifications');\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_ALL_NOTIFICATIONS_MUTATION\n    }).pipe(map(result => {\n      const response = result.data?.deleteAllNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression de toutes les notifications:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression de toutes les notifications', {\n      success: true,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(notificationIds) {\n    this.logger.debug('MessageService', `Suppression de ${notificationIds.length} notifications`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo.mutate({\n      mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n      variables: {\n        notificationIds\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteMultipleNotifications;\n      if (!response) {\n        throw new Error('Réponse de suppression invalide');\n      }\n      this.logger.debug('MessageService', 'Résultat de la suppression multiple:', response);\n      return response;\n    }), catchError(error => this.handleDeletionError(error, 'la suppression multiple de notifications', {\n      success: count > 0,\n      count,\n      message: `${count} notifications supprimées localement (erreur serveur)`\n    })));\n  }\n  groupNotificationsByType() {\n    return this.notifications$.pipe(map(notifications => {\n      const groups = new Map();\n      notifications.forEach(notif => {\n        if (!groups.has(notif.type)) {\n          groups.set(notif.type, []);\n        }\n        groups.get(notif.type)?.push(notif);\n      });\n      return groups;\n    }));\n  }\n  markAsRead(notificationIds) {\n    this.logger.debug('MessageService', `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`);\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value\n      });\n    }\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(id => id && typeof id === 'string' && id.trim() !== '');\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n    this.logger.debug('MessageService', 'Sending mutation to mark notifications as read', validIds);\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      }\n    };\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n    return this.apollo.mutate({\n      mutation: MARK_NOTIFICATION_READ_MUTATION,\n      variables: {\n        notificationIds: validIds\n      },\n      optimisticResponse: optimisticResponse,\n      errorPolicy: 'all',\n      fetchPolicy: 'no-cache' // Ne pas utiliser le cache pour cette mutation\n    }).pipe(map(result => {\n      this.logger.debug('MessageService', 'Mutation result', result);\n      console.log('Mutation result:', result);\n      // Si nous avons des erreurs GraphQL, les logger mais continuer\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors:', result.errors);\n        console.error('GraphQL errors:', result.errors);\n      }\n      // Utiliser la réponse du serveur ou notre réponse optimiste\n      const response = result.data?.markNotificationsAsRead ?? optimisticResponse.markNotificationsAsRead;\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error marking notifications as read:', error);\n      console.error('Error in markAsRead:', error);\n      // En cas d'erreur, retourner quand même un succès simulé\n      // puisque nous avons déjà mis à jour l'interface utilisateur\n      return of({\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(0, this.notificationCount.value - validIds.length)\n      });\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n  // --------------------------------------------------------------------------\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId) {\n    return this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      variables: {\n        callId\n      }\n    }).pipe(map(({\n      data\n    }) => {\n      if (!data?.callSignal) {\n        throw new Error('No call signal received');\n      }\n      return data.callSignal;\n    }), tap(signal => {\n      this.callSignals.next(signal);\n      this.handleCallSignal(signal);\n    }), catchError(error => {\n      this.logger.error('Error in call signal subscription', error);\n      return throwError(() => new Error('Call signal subscription failed'));\n    }));\n  }\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(callId, signalType, signalData) {\n    return this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId,\n        signalType,\n        signalData\n      }\n    }).pipe(map(result => {\n      const success = result.data?.sendCallSignal;\n      if (!success) {\n        throw new Error('Failed to send call signal');\n      }\n      return success;\n    }), catchError(error => {\n      this.logger.error('Error sending call signal', error);\n      return throwError(() => new Error('Failed to send call signal'));\n    }));\n  }\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(limit = 20, offset = 0, status, type, startDate, endDate) {\n    return this.apollo.watchQuery({\n      query: CALL_HISTORY_QUERY,\n      variables: {\n        limit,\n        offset,\n        status,\n        type,\n        startDate,\n        endDate\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const history = result.data?.callHistory || [];\n      this.logger.debug(`Retrieved ${history.length} call history items`);\n      return history;\n    }), catchError(error => {\n      this.logger.error('Error fetching call history:', error);\n      return throwError(() => new Error('Failed to fetch call history'));\n    }));\n  }\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId) {\n    return this.apollo.watchQuery({\n      query: CALL_DETAILS_QUERY,\n      variables: {\n        callId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const details = result.data?.callDetails;\n      if (!details) {\n        throw new Error('Call details not found');\n      }\n      this.logger.debug(`Retrieved call details for: ${callId}`);\n      return details;\n    }), catchError(error => {\n      this.logger.error('Error fetching call details:', error);\n      return throwError(() => new Error('Failed to fetch call details'));\n    }));\n  }\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats() {\n    return this.apollo.watchQuery({\n      query: CALL_STATS_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => {\n      const stats = result.data?.callStats;\n      if (!stats) {\n        throw new Error('Call stats not found');\n      }\n      this.logger.debug('Retrieved call stats:', stats);\n      return stats;\n    }), catchError(error => {\n      this.logger.error('Error fetching call stats:', error);\n      return throwError(() => new Error('Failed to fetch call stats'));\n    }));\n  }\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  handleCallSignal(signal) {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  handleIceCandidate(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection.addIceCandidate(new RTCIceCandidate(candidate)).catch(error => {\n        this.logger.error('Error adding ICE candidate', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error);\n    }\n  }\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  handleAnswer(signal) {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection.setRemoteDescription(new RTCSessionDescription(answer)).catch(error => {\n        this.logger.error('Error setting remote description', error);\n      });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error);\n    }\n  }\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  handleEndCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  handleRejectCall(signal) {\n    this.stop('ringtone');\n    this.cleanupCall();\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString()\n      });\n    }\n  }\n  /**\n   * Nettoie les ressources d'appel\n   */\n  cleanupCall() {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  setupMediaDevices(callType) {\n    const constraints = {\n      audio: true,\n      video: callType !== CallType.AUDIO ? {\n        width: {\n          ideal: 1280\n        },\n        height: {\n          ideal: 720\n        }\n      } : false\n    };\n    return new Observable(observer => {\n      navigator.mediaDevices.getUserMedia(constraints).then(stream => {\n        observer.next(stream);\n        observer.complete();\n      }).catch(error => {\n        this.logger.error('Error accessing media devices', error);\n        observer.error(new Error('Failed to access media devices'));\n      });\n    });\n  }\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  generateCallId() {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(forceRefresh = false, search, page = 1, limit = 10, sortBy = 'username', sortOrder = 'asc', isOnline) {\n    this.logger.info('MessageService', `Getting users with params: forceRefresh=${forceRefresh}, search=${search || '(empty)'}, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`);\n    const now = Date.now();\n    const cacheValid = !forceRefresh && this.usersCache.length > 0 && now - this.lastFetchTime <= this.CACHE_DURATION && !search && page === 1 && limit >= this.usersCache.length;\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug('MessageService', `Using cached users (${this.usersCache.length} users)`);\n      return of([...this.usersCache]);\n    }\n    this.logger.debug('MessageService', `Fetching users from server with pagination, fetchPolicy=${forceRefresh ? 'network-only' : 'cache-first'}`);\n    return this.apollo.watchQuery({\n      query: GET_ALL_USER_QUERY,\n      variables: {\n        search,\n        page,\n        limit,\n        sortBy,\n        sortOrder,\n        isOnline: isOnline !== undefined ? isOnline : null\n      },\n      fetchPolicy: forceRefresh ? 'network-only' : 'cache-first'\n    }).valueChanges.pipe(map(result => {\n      this.logger.debug('MessageService', 'Users response received', result);\n      if (result.errors) {\n        this.logger.error('MessageService', 'GraphQL errors in getAllUsers:', result.errors);\n        throw new Error(result.errors.map(e => e.message).join(', '));\n      }\n      if (!result.data?.getAllUsers) {\n        this.logger.warn('MessageService', 'No users data received from server');\n        return [];\n      }\n      const paginatedResponse = result.data.getAllUsers;\n      // Log pagination metadata\n      this.logger.debug('MessageService', 'Pagination metadata:', {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      });\n      // Normalize users with error handling\n      const users = [];\n      for (const user of paginatedResponse.users) {\n        try {\n          if (user) {\n            users.push(this.normalizeUser(user));\n          }\n        } catch (error) {\n          this.logger.warn('MessageService', `Error normalizing user, skipping:`, error);\n        }\n      }\n      this.logger.info('MessageService', `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`);\n      // Update cache only for first page with no filters\n      if (!search && page === 1 && !isOnline) {\n        this.usersCache = [...users];\n        this.lastFetchTime = Date.now();\n        this.logger.debug('MessageService', `User cache updated with ${users.length} users`);\n      }\n      // Store pagination metadata in a property for component access\n      this.currentUserPagination = {\n        totalCount: paginatedResponse.totalCount,\n        totalPages: paginatedResponse.totalPages,\n        currentPage: paginatedResponse.currentPage,\n        hasNextPage: paginatedResponse.hasNextPage,\n        hasPreviousPage: paginatedResponse.hasPreviousPage\n      };\n      return users;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching users:', error);\n      if (error.graphQLErrors) {\n        this.logger.error('MessageService', 'GraphQL errors:', error.graphQLErrors);\n      }\n      if (error.networkError) {\n        this.logger.error('MessageService', 'Network error:', error.networkError);\n      }\n      // Return cache if available (only for first page)\n      if (this.usersCache.length > 0 && page === 1 && !search && !isOnline) {\n        this.logger.warn('MessageService', `Returning ${this.usersCache.length} cached users due to fetch error`);\n        return of([...this.usersCache]);\n      }\n      return throwError(() => new Error(`Failed to fetch users: ${error.message || 'Unknown error'}`));\n    }));\n  }\n  getOneUser(userId) {\n    return this.apollo.watchQuery({\n      query: GET_USER_QUERY,\n      variables: {\n        id: userId\n      },\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getOneUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching user:', error);\n      return throwError(() => new Error('Failed to fetch user'));\n    }));\n  }\n  getCurrentUser() {\n    return this.apollo.watchQuery({\n      query: GET_CURRENT_USER_QUERY,\n      fetchPolicy: 'network-only'\n    }).valueChanges.pipe(map(result => this.normalizeUser(result.data?.getCurrentUser)), catchError(error => {\n      this.logger.error('MessageService', 'Error fetching current user:', error);\n      return throwError(() => new Error('Failed to fetch current user'));\n    }));\n  }\n  setUserOnline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_ONLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOnline) throw new Error('Failed to set user online');\n      return this.normalizeUser(result.data.setUserOnline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user online:', error);\n      return throwError(() => new Error('Failed to set user online'));\n    }));\n  }\n  setUserOffline(userId) {\n    return this.apollo.mutate({\n      mutation: SET_USER_OFFLINE_MUTATION,\n      variables: {\n        userId\n      }\n    }).pipe(map(result => {\n      if (!result.data?.setUserOffline) throw new Error('Failed to set user offline');\n      return this.normalizeUser(result.data.setUserOffline);\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error setting user offline:', error);\n      return throwError(() => new Error('Failed to set user offline'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(name, participantIds, photo, description) {\n    this.logger.debug('MessageService', `Creating group: ${name} with ${participantIds.length} participants`);\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(() => new Error('Nom du groupe et participants requis'));\n    }\n    return this.apollo.mutate({\n      mutation: CREATE_GROUP_MUTATION,\n      variables: {\n        name,\n        participantIds,\n        photo,\n        description\n      }\n    }).pipe(map(result => {\n      const group = result.data?.createGroup;\n      if (!group) {\n        throw new Error('Échec de la création du groupe');\n      }\n      this.logger.info('MessageService', `Group created successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error creating group:', error);\n      return throwError(() => new Error('Échec de la création du groupe'));\n    }));\n  }\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId, input) {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: UPDATE_GROUP_MUTATION,\n      variables: {\n        id: groupId,\n        input\n      }\n    }).pipe(map(result => {\n      const group = result.data?.updateGroup;\n      if (!group) {\n        throw new Error('Échec de la mise à jour du groupe');\n      }\n      this.logger.info('MessageService', `Group updated successfully: ${group.id}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error updating group:', error);\n      return throwError(() => new Error('Échec de la mise à jour du groupe'));\n    }));\n  }\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(groupId) {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: DELETE_GROUP_MUTATION,\n      variables: {\n        id: groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.deleteGroup;\n      if (!response) {\n        throw new Error('Échec de la suppression du groupe');\n      }\n      this.logger.info('MessageService', `Group deleted successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error deleting group:', error);\n      return throwError(() => new Error('Échec de la suppression du groupe'));\n    }));\n  }\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(groupId) {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.mutate({\n      mutation: LEAVE_GROUP_MUTATION,\n      variables: {\n        groupId\n      }\n    }).pipe(map(result => {\n      const response = result.data?.leaveGroup;\n      if (!response) {\n        throw new Error('Échec de la sortie du groupe');\n      }\n      this.logger.info('MessageService', `Left group successfully: ${groupId}`);\n      return response;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error leaving group:', error);\n      return throwError(() => new Error('Échec de la sortie du groupe'));\n    }));\n  }\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId) {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n    return this.apollo.query({\n      query: GET_GROUP_QUERY,\n      variables: {\n        id: groupId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const group = result.data?.getGroup;\n      if (!group) {\n        throw new Error('Groupe non trouvé');\n      }\n      this.logger.info('MessageService', `Group retrieved successfully: ${groupId}`);\n      return group;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting group:', error);\n      return throwError(() => new Error('Échec de la récupération du groupe'));\n    }));\n  }\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId) {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n    return this.apollo.query({\n      query: GET_USER_GROUPS_QUERY,\n      variables: {\n        userId\n      },\n      fetchPolicy: 'network-only'\n    }).pipe(map(result => {\n      const groups = result.data?.getUserGroups || [];\n      this.logger.info('MessageService', `Retrieved ${groups.length} groups for user: ${userId}`);\n      return groups;\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Error getting user groups:', error);\n      return throwError(() => new Error('Échec de la récupération des groupes'));\n    }));\n  }\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId) {\n    console.log(`🔍 DEBUG: subscribeToNewMessages called with conversationId: ${conversationId}`);\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    const tokenValid = this.isTokenValid();\n    console.log(`🔍 DEBUG: Token validation result: ${tokenValid}`);\n    if (!tokenValid) {\n      console.warn('❌ DEBUG: Token invalid - subscription will not be established');\n      this.logger.warn(\"Tentative d'abonnement aux messages avec un token invalide ou expiré\");\n      return of(null);\n    }\n    console.log(`✅ DEBUG: Token valid - proceeding with subscription setup for conversation: ${conversationId}`);\n    this.logger.debug(`🚀 INSTANT MESSAGE: Setting up real-time subscription for conversation: ${conversationId}`);\n    console.log(`🔍 DEBUG: Creating Apollo subscription with variables:`, {\n      conversationId\n    });\n    console.log(`🔍 DEBUG: MESSAGE_SENT_SUBSCRIPTION query:`, MESSAGE_SENT_SUBSCRIPTION);\n    console.log(`🔍 DEBUG: Subscription variables:`, {\n      conversationId\n    });\n    const sub$ = this.apollo.subscribe({\n      query: MESSAGE_SENT_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(tap(result => {\n      console.log(`🔍 DEBUG: Raw subscription result received:`, result);\n      console.log(`🔍 DEBUG: result.data:`, result.data);\n      console.log(`🔍 DEBUG: result.data?.messageSent:`, result.data?.messageSent);\n    }), map(result => {\n      const msg = result.data?.messageSent;\n      if (!msg) {\n        console.log(`❌ DEBUG: No message payload received in result:`, result);\n        this.logger.warn('⚠️ No message payload received');\n        throw new Error('No message payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New message received via WebSocket', msg);\n      // Vérifier que l'ID est présent\n      if (!msg.id && !msg._id) {\n        this.logger.warn('⚠️ Message without ID received, generating temp ID');\n        msg.id = `temp-${Date.now()}`;\n      }\n      try {\n        // NORMALISATION RAPIDE du message\n        const normalizedMessage = this.normalizeMessage(msg);\n        this.logger.debug('✅ INSTANT: Message normalized successfully', normalizedMessage);\n        // TRAITEMENT INSTANTANÉ selon le type\n        if (normalizedMessage.type === MessageType.AUDIO || normalizedMessage.type === MessageType.VOICE_MESSAGE || normalizedMessage.attachments && normalizedMessage.attachments.some(att => att.type === 'AUDIO')) {\n          this.logger.debug('🎤 INSTANT: Voice message received in real-time');\n        }\n        // MISE À JOUR IMMÉDIATE de l'UI\n        this.zone.run(() => {\n          this.logger.debug('📡 INSTANT: Updating conversation UI immediately');\n          this.updateConversationWithNewMessage(conversationId, normalizedMessage);\n        });\n        return normalizedMessage;\n      } catch (err) {\n        this.logger.error('❌ Error normalizing message:', err);\n        // Créer un message minimal mais valide pour éviter les erreurs\n        const minimalMessage = {\n          id: msg.id || msg._id || `temp-${Date.now()}`,\n          content: msg.content || '',\n          type: msg.type || MessageType.TEXT,\n          timestamp: this.safeDate(msg.timestamp),\n          isRead: false,\n          sender: msg.sender ? this.normalizeUser(msg.sender) : {\n            id: this.getCurrentUserId(),\n            username: 'Unknown'\n          }\n        };\n        this.logger.debug('🔧 FALLBACK: Created minimal message', minimalMessage);\n        return minimalMessage;\n      }\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Message subscription error:', error);\n      // Retourner un observable vide au lieu de null\n      return EMPTY;\n    }),\n    // Filtrer les valeurs null\n    filter(message => !!message),\n    // Réessayer après un délai en cas d'erreur\n    retry(3));\n    console.log(`🔍 DEBUG: Setting up subscription observer...`);\n    const sub = sub$.subscribe({\n      next: message => {\n        console.log(`✅ DEBUG: Message received via subscription:`, message);\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: err => {\n        console.error(`❌ DEBUG: Subscription error:`, err);\n        this.logger.error('Error in message subscription:', err);\n      },\n      complete: () => {\n        console.log(`🔚 DEBUG: Subscription completed`);\n      }\n    });\n    // Log pour confirmer que la subscription est créée\n    console.log(`🔗 DEBUG: Subscription object created:`, sub);\n    console.log(`🔗 DEBUG: Apollo client state:`, this.apollo);\n    this.subscriptions.push(sub);\n    console.log(`✅ DEBUG: Subscription established and added to subscriptions list. Total subscriptions: ${this.subscriptions.length}`);\n    return sub$;\n  }\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  updateConversationWithNewMessage(conversationId, message) {\n    this.logger.debug(`⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`);\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: conversation => {\n          this.logger.debug(`✅ BACKGROUND: Conversation ${conversationId} refreshed with ${conversation?.messages?.length || 0} messages`);\n        },\n        error: error => {\n          this.logger.error(`⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`, error);\n        }\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  refreshSenderNotifications() {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: notifications => {\n        console.log('🔄 SENDER: Notifications refreshed successfully', notifications.length);\n      },\n      error: error => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      }\n    });\n  }\n  subscribeToUserStatus() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\");\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n    const sub$ = this.apollo.subscribe({\n      query: USER_STATUS_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement au statut utilisateur:\", result)), map(result => {\n      const user = result.data?.userStatusChanged;\n      if (!user) {\n        this.logger.error('No status payload received');\n        throw new Error('No status payload received');\n      }\n      return this.normalizeUser(user);\n    }), catchError(error => {\n      this.logger.error('Status subscription error:', error);\n      return throwError(() => new Error('Status subscription failed'));\n    }), retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: CONVERSATION_UPDATED_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => {\n      const conv = result.data?.conversationUpdated;\n      if (!conv) throw new Error('No conversation payload received');\n      const normalizedConversation = {\n        ...conv,\n        participants: conv.participants?.map(p => this.normalizeUser(p)) || [],\n        lastMessage: conv.lastMessage ? {\n          ...conv.lastMessage,\n          sender: this.normalizeUser(conv.lastMessage.sender),\n          timestamp: this.safeDate(conv.lastMessage.timestamp),\n          readAt: conv.lastMessage.readAt ? this.safeDate(conv.lastMessage.readAt) : undefined,\n          // Conservez toutes les autres propriétés du message\n          id: conv.lastMessage.id,\n          content: conv.lastMessage.content,\n          type: conv.lastMessage.type,\n          isRead: conv.lastMessage.isRead\n          // ... autres propriétés nécessaires\n        } : null // On conserve null comme dans votre version originale\n      };\n\n      return normalizedConversation; // Assertion de type si nécessaire\n    }), catchError(error => {\n      this.logger.error('MessageService', 'Conversation subscription error:', error);\n      return throwError(() => new Error('Conversation subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(conversationId) {\n    const sub$ = this.apollo.subscribe({\n      query: TYPING_INDICATOR_SUBSCRIPTION,\n      variables: {\n        conversationId\n      }\n    }).pipe(map(result => result.data?.typingIndicator), filter(Boolean), catchError(error => {\n      this.logger.error('MessageService', 'Typing indicator subscription error:', error);\n      return throwError(() => new Error('Typing indicator subscription failed'));\n    }));\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  isTokenValid() {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString()\n        });\n        return false;\n      }\n      return true;\n    } catch (error) {\n      this.logger.error('Erreur lors de la vérification du token:', error);\n      return false;\n    }\n  }\n  subscribeToNotificationsRead() {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications avec un token invalide ou expiré\");\n      return of([]);\n    }\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n    const sub$ = this.apollo.subscribe({\n      query: NOTIFICATIONS_READ_SUBSCRIPTION\n    }).pipe(tap(result => this.logger.debug(\"Données reçues de l'abonnement aux notifications lues:\", result)), map(result => {\n      const notificationIds = result.data?.notificationsRead || [];\n      this.logger.debug('Notifications marquées comme lues:', notificationIds);\n      this.updateNotificationStatus(notificationIds, true);\n      return notificationIds;\n    }), catchError(err => {\n      this.logger.error('Notifications read subscription error:', err);\n      // Retourner un tableau vide au lieu de propager l'erreur\n      return of([]);\n    }),\n    // Réessayer après un délai en cas d'erreur\n    retry(3) // Réessayer 3 fois en cas d'erreur\n    );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications() {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\"Tentative d'abonnement aux notifications sans être connecté\");\n      return EMPTY;\n    }\n    this.logger.debug('🚀 INSTANT NOTIFICATION: Setting up real-time subscription');\n    const source$ = this.apollo.subscribe({\n      query: NOTIFICATION_SUBSCRIPTION\n    });\n    const processed$ = source$.pipe(map(result => {\n      const notification = result.data?.notificationReceived;\n      if (!notification) {\n        throw new Error('No notification payload received');\n      }\n      this.logger.debug('⚡ INSTANT: New notification received', notification);\n      const normalized = this.normalizeNotification(notification);\n      // Vérification rapide du cache\n      if (this.notificationCache.has(normalized.id)) {\n        this.logger.debug(`🔄 Notification ${normalized.id} already in cache, skipping`);\n        throw new Error('Notification already exists in cache');\n      }\n      // TRAITEMENT INSTANTANÉ\n      this.logger.debug('📡 INSTANT: Processing notification immediately');\n      // Vérifier si la notification existe déjà pour éviter les doublons\n      const currentNotifications = this.notifications.value;\n      const existingNotification = currentNotifications.find(n => n.id === normalized.id);\n      if (existingNotification) {\n        this.logger.debug('🔄 DUPLICATE: Notification already exists, skipping:', normalized.id);\n        return normalized;\n      }\n      // Son de notification IMMÉDIAT\n      this.playNotificationSound();\n      // Mise à jour INSTANTANÉE du cache\n      this.updateNotificationCache(normalized);\n      // Émettre IMMÉDIATEMENT la notification EN PREMIER\n      this.zone.run(() => {\n        // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n        const updatedNotifications = [normalized, ...currentNotifications];\n        this.logger.debug(`⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`);\n        this.notifications.next(updatedNotifications);\n        this.notificationCount.next(this.notificationCount.value + 1);\n      });\n      this.logger.debug('✅ INSTANT: Notification processed and emitted', normalized);\n      return normalized;\n    }),\n    // Gestion d'erreurs optimisée\n    catchError(err => {\n      if (err instanceof Error && err.message === 'Notification already exists in cache') {\n        return EMPTY;\n      }\n      this.logger.error('❌ Notification subscription error:', err);\n      return EMPTY;\n    }),\n    // Optimisation: traitement en temps réel\n    tap(notification => {\n      this.logger.debug('⚡ INSTANT: Notification ready for UI update', notification);\n    }));\n    const sub = processed$.subscribe({\n      next: notification => {\n        this.logger.debug('✅ INSTANT: Notification delivered to UI', notification);\n      },\n      error: error => {\n        this.logger.error('❌ CRITICAL: Notification subscription error', error);\n      }\n    });\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n  startCleanupInterval() {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  cleanupExpiredNotifications() {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n    let expiredCount = 0;\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(this.notificationCache.values());\n      const sortedNotifications = this.sortNotificationsByDate(remainingNotifications);\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  sortNotificationsByDate(notifications) {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  getCurrentUserId() {\n    return localStorage.getItem('userId') || '';\n  }\n  normalizeMessage(message) {\n    if (!message) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined message');\n      throw new Error('Message object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error('[MessageService] Message ID is missing', undefined, message);\n        throw new Error('Message ID is required');\n      }\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender ? this.normalizeUser(message.sender) : undefined;\n      } catch (error) {\n        this.logger.warn('[MessageService] Error normalizing message sender, using default values', error);\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true\n        };\n      }\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing message receiver, using default values', error);\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true\n          };\n        }\n      }\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments = message.attachments?.map(att => ({\n        id: att.id || att._id || `attachment-${Date.now()}`,\n        url: att.url || '',\n        type: att.type || 'unknown',\n        name: att.name || 'attachment',\n        size: att.size || 0,\n        duration: att.duration || 0\n      })) || [];\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null\n      };\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id\n      });\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing message:', error instanceof Error ? error : new Error(String(error)), message);\n      throw new Error(`Failed to normalize message: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeUser(user) {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive = user.isActive !== undefined && user.isActive !== null ? user.isActive : true;\n    const role = user.role || 'user';\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount\n    };\n  }\n  normalizeConversation(conv) {\n    if (!conv) {\n      this.logger.error('[MessageService] Cannot normalize null or undefined conversation');\n      throw new Error('Conversation object is required');\n    }\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error('[MessageService] Conversation ID is missing', undefined, conv);\n        throw new Error('Conversation ID is required');\n      }\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing participant, skipping', error);\n          }\n        }\n      } else {\n        this.logger.warn('[MessageService] Conversation has no participants or invalid participants array', conv);\n      }\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length\n        });\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug('[MessageService] Successfully normalized message', {\n                messageId: normalizedMessage.id,\n                content: normalizedMessage.content?.substring(0, 20),\n                sender: normalizedMessage.sender?.username\n              });\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn('[MessageService] Error normalizing message in conversation, skipping', error);\n          }\n        }\n      } else {\n        this.logger.debug('[MessageService] No messages found in conversation or invalid messages array');\n      }\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn('[MessageService] Error normalizing last message, using null', error);\n        }\n      }\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt)\n      };\n      this.logger.debug('[MessageService] Conversation normalized successfully', {\n        conversationId: normalizedConversation.id,\n        participantCount: normalizedParticipants.length,\n        messageCount: normalizedMessages.length\n      });\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error('[MessageService] Error normalizing conversation:', error instanceof Error ? error : new Error(String(error)), conv);\n      throw new Error(`Failed to normalize conversation: ${error instanceof Error ? error.message : String(error)}`);\n    }\n  }\n  normalizeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  safeDate(date) {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  normalizeNotification(notification) {\n    this.logger.debug('MessageService', 'Normalizing notification', notification);\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || notification._id;\n    if (!notificationId) {\n      this.logger.error('MessageService', 'Notification ID is missing', notification);\n      throw new Error('Notification ID is required');\n    }\n    if (!notification.timestamp) {\n      this.logger.warn('MessageService', 'Notification timestamp is missing, using current time', notification);\n      notification.timestamp = new Date();\n    }\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId,\n        id: notificationId,\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId)\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message)\n        })\n      };\n      this.logger.debug('MessageService', 'Normalized notification result', normalized);\n      return normalized;\n    } catch (error) {\n      this.logger.error('MessageService', 'Error in normalizeNotification', error);\n      throw error;\n    }\n  }\n  normalizeSender(sender) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && {\n        image: sender.image\n      })\n    };\n  }\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  normalizeNotMessage(message) {\n    if (!message) return null;\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && {\n        sender: this.normalizeSender(message.sender)\n      })\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  updateCache(notifications, skipDuplicates = true) {\n    const notificationArray = Array.isArray(notifications) ? notifications : [notifications];\n    this.logger.debug('MessageService', `Updating notification cache with ${notificationArray.length} notifications`);\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(notif => notif && (notif.id || notif._id));\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn('MessageService', `Found ${notificationArray.length - validNotifications.length} notifications without valid IDs`);\n    }\n    let addedCount = 0;\n    let skippedCount = 0;\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || notif._id;\n        if (!notifId) {\n          this.logger.error('MessageService', 'Notification without ID:', notif);\n          return;\n        }\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug('MessageService', `Notification ${normalized.id} already exists in cache, skipping`);\n          skippedCount++;\n          return;\n        }\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n        this.logger.debug('MessageService', `Added notification ${normalized.id} to cache`);\n      } catch (error) {\n        this.logger.error('MessageService', `Error processing notification ${index + 1}:`, error);\n      }\n    });\n    this.logger.debug('MessageService', `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`);\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  refreshNotificationObservables() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n    this.logger.debug(`📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`);\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  updateUnreadCount() {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter(n => !n.isRead);\n    const count = unreadNotifications.length;\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(new CustomEvent('notificationCountChanged', {\n        detail: {\n          count\n        }\n      }));\n    });\n  }\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  updateNotificationCache(notification) {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  updateNotificationStatus(ids, isRead) {\n    ids.forEach(id => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  removeNotificationsFromCache(notificationIds) {\n    console.log('🗑️ REMOVE FROM CACHE: Starting removal of', notificationIds.length, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size before:', this.notificationCache.size);\n    let removedCount = 0;\n    notificationIds.forEach(id => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log('🗑️ REMOVE FROM CACHE: Notification not found in cache:', id);\n      }\n    });\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log('🗑️ REMOVE FROM CACHE: Cache size after:', this.notificationCache.size);\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n    return removedCount;\n  }\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  handleDeletionError(error, operation, fallbackResponse) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: START_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.startTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error starting typing indicator', error);\n      return throwError(() => new Error('Failed to start typing indicator'));\n    }));\n  }\n  stopTyping(conversationId) {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n    return this.apollo.mutate({\n      mutation: STOP_TYPING_MUTATION,\n      variables: {\n        input: {\n          conversationId,\n          userId\n        }\n      }\n    }).pipe(map(result => result.data?.stopTyping || false), catchError(error => {\n      this.logger.error('MessageService', 'Error stopping typing indicator', error);\n      return throwError(() => new Error('Failed to stop typing indicator'));\n    }));\n  }\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(receiverId, content, file, messageType = 'TEXT', conversationId) {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId\n    });\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n    // Préparer les variables pour la mutation\n    const variables = {\n      receiverId,\n      content: content || '',\n      type: messageType\n    };\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size\n      });\n    }\n    console.log('📤 [MessageService] Sending mutation with variables:', variables);\n    return this.apollo.mutate({\n      mutation: SEND_MESSAGE_MUTATION,\n      variables,\n      context: {\n        useMultipart: !!file // Utiliser multipart si un fichier est présent\n      }\n    }).pipe(map(result => {\n      console.log('✅ [MessageService] sendMessage mutation result:', result);\n      if (!result.data?.sendMessage) {\n        throw new Error('No message data received from server');\n      }\n      const message = result.data.sendMessage;\n      console.log('📨 [MessageService] Message sent successfully:', {\n        id: message.id,\n        type: message.type,\n        content: message.content?.substring(0, 50),\n        hasAttachments: !!message.attachments?.length\n      });\n      // Normaliser le message reçu\n      const normalizedMessage = this.normalizeMessage(message);\n      console.log('🔧 [MessageService] Message normalized:', normalizedMessage);\n      return normalizedMessage;\n    }), catchError(error => {\n      console.error('❌ [MessageService] sendMessage error:', error);\n      this.logger.error('Error sending message:', error);\n      // Fournir un message d'erreur plus spécifique\n      let errorMessage = \"Erreur lors de l'envoi du message\";\n      if (error.networkError) {\n        errorMessage = 'Erreur de connexion réseau';\n      } else if (error.graphQLErrors?.length > 0) {\n        errorMessage = error.graphQLErrors[0].message || errorMessage;\n      }\n      return throwError(() => new Error(errorMessage));\n    }));\n  }\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp) {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive) {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate = lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours = Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp) {\n    if (!timestamp) return 'Unknown date';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        });\n      }\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit'\n        })}`;\n      }\n      const day = date.toLocaleDateString('fr-FR', {\n        weekday: 'short'\n      }).toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit'\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages, index) {\n    if (index === 0) return true;\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n  getDateFromTimestamp(timestamp) {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (timestamp instanceof Date ? timestamp : new Date(timestamp)).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType) {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword')) return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed')) return 'fa-file-archive';\n    return 'fa-file';\n  }\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType) {\n    if (!mimeType) return 'File';\n    const typeMap = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation': 'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive'\n    };\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message) {\n    if (!message) return false;\n    // Vérifier le type du message\n    if (message.type === MessageType.VOICE_MESSAGE || message.type === MessageType.VOICE_MESSAGE) {\n      return true;\n    }\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || message.metadata?.isVoiceMessage && (type === 'AUDIO' || type === 'audio');\n      });\n    }\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const voiceAttachment = message.attachments.find(att => {\n      const type = att.type?.toString();\n      return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n    });\n    return voiceAttachment?.url || '';\n  }\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message) {\n    if (!message) return 0;\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find(att => {\n        const type = att.type?.toString();\n        return type === 'VOICE_MESSAGE' || type === 'voice_message' || type === 'AUDIO' || type === 'audio';\n      });\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n    return 0;\n  }\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index) {\n    const pattern = [8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18];\n    return pattern[index % pattern.length];\n  }\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds) {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message) {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message) {\n    if (!message) return MessageType.TEXT;\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (attachmentTypeStr === 'file' || attachmentTypeStr === 'FILE') {\n            return MessageType.FILE;\n          } else if (attachmentTypeStr === 'audio' || attachmentTypeStr === 'AUDIO') {\n            return MessageType.AUDIO;\n          } else if (attachmentTypeStr === 'video' || attachmentTypeStr === 'VIDEO') {\n            return MessageType.VIDEO;\n          }\n        }\n        return MessageType.FILE;\n      }\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis() {\n    return ['😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰', '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '😏', '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠', '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '👍', '👎', '👏', '🙌', '👐', '🤲', '🤝', '🙏', '✌️', '🤞', '❤️', '🧡', '💛', '💚', '💙', '💜', '🖤', '💔', '💯', '💢'];\n  }\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message, currentUserId) {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n    try {\n      const isCurrentUser = message.sender?.id === currentUserId || message.sender?._id === currentUserId || message.senderId === currentUserId;\n      const baseClass = isCurrentUser ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm' : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n      const messageType = this.getMessageType(message);\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (attachmentTypeStr === 'FILE' || attachmentTypeStr === 'file') {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n  // ========================================\n  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n  // ========================================\n  // Note: Les méthodes d'appel ont été déplacées vers CallService\n  // pour éviter la duplication de code et centraliser la logique\n  // destroy\n  cleanupSubscriptions() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n  static {\n    this.ɵfac = function MessageService_Factory(t) {\n      return new (t || MessageService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService), i0.ɵɵinject(i0.NgZone));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MessageService,\n      factory: MessageService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "of", "throwError", "retry", "EMPTY", "map", "catchError", "tap", "filter", "MessageType", "CallType", "CallStatus", "GET_CONVERSATIONS_QUERY", "GET_NOTIFICATIONS_QUERY", "NOTIFICATION_SUBSCRIPTION", "GET_CONVERSATION_QUERY", "SEND_MESSAGE_MUTATION", "MARK_AS_READ_MUTATION", "MESSAGE_SENT_SUBSCRIPTION", "USER_STATUS_SUBSCRIPTION", "GET_USER_QUERY", "GET_ALL_USER_QUERY", "CONVERSATION_UPDATED_SUBSCRIPTION", "SEARCH_MESSAGES_QUERY", "GET_UNREAD_MESSAGES_QUERY", "SET_USER_ONLINE_MUTATION", "SET_USER_OFFLINE_MUTATION", "START_TYPING_MUTATION", "STOP_TYPING_MUTATION", "TYPING_INDICATOR_SUBSCRIPTION", "GET_CURRENT_USER_QUERY", "REACT_TO_MESSAGE_MUTATION", "FORWARD_MESSAGE_MUTATION", "PIN_MESSAGE_MUTATION", "CREATE_GROUP_MUTATION", "UPDATE_GROUP_MUTATION", "DELETE_GROUP_MUTATION", "LEAVE_GROUP_MUTATION", "GET_GROUP_QUERY", "GET_USER_GROUPS_QUERY", "EDIT_MESSAGE_MUTATION", "DELETE_MESSAGE_MUTATION", "GET_MESSAGES_QUERY", "GET_NOTIFICATIONS_ATTACHAMENTS", "MARK_NOTIFICATION_READ_MUTATION", "NOTIFICATIONS_READ_SUBSCRIPTION", "CREATE_CONVERSATION_MUTATION", "DELETE_NOTIFICATION_MUTATION", "DELETE_MULTIPLE_NOTIFICATIONS_MUTATION", "DELETE_ALL_NOTIFICATIONS_MUTATION", "CALL_HISTORY_QUERY", "CALL_DETAILS_QUERY", "CALL_STATS_QUERY", "SEND_CALL_SIGNAL_MUTATION", "CALL_SIGNAL_SUBSCRIPTION", "INCOMING_CALL_SUBSCRIPTION", "GET_VOICE_MESSAGES_QUERY", "MessageService", "constructor", "apollo", "logger", "zone", "activeConversation", "notifications", "notificationCache", "Map", "notificationCount", "onlineUsers", "subscriptions", "CACHE_DURATION", "lastFetchTime", "activeCall", "incomingCall", "callSignals", "localStream", "remoteStream", "peerConnection", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "localStream$", "remoteStream$", "rtcConfig", "iceServers", "urls", "usersCache", "currentUserPagination", "totalCount", "totalPages", "currentPage", "hasNextPage", "hasPreviousPage", "activeConversation$", "notifications$", "notificationCount$", "sounds", "isPlaying", "muted", "notificationPagination", "limit", "hasMoreNotifications", "toSafeISOString", "date", "undefined", "toISOString", "loadNotificationsFromLocalStorage", "initSubscriptions", "startCleanupInterval", "preloadSounds", "savedNotifications", "localStorage", "getItem", "JSON", "parse", "clear", "for<PERSON>ach", "notification", "id", "set", "next", "Array", "from", "values", "updateUnreadCount", "error", "runOutsideAngular", "subscribeToNewNotifications", "subscribe", "subscribeToNotificationsRead", "subscribeToIncomingCalls", "subscribeToUserStatus", "query", "pipe", "data", "handleIncomingCall", "call", "play", "loadSound", "name", "path", "audio", "Audio", "load", "addEventListener", "loop", "sound", "currentTime", "catch", "stop", "pause", "stopAllSounds", "Object", "keys", "setMuted", "isMuted", "playNotificationSound", "console", "log", "audioContext", "window", "AudioContext", "webkitAudioContext", "playNotificationMelody1", "volume", "err", "audioError", "playNotificationTone", "playNotificationMelody2", "playNotificationMelody3", "playNotificationMelody4", "playNotificationMelody5", "playBellTone", "startTime", "frequency", "duration", "oscillator", "createOscillator", "gainNode", "createGain", "type", "setValueAtTime", "gain", "linearRampToValueAtTime", "connect", "destination", "start", "exponentialRampToValueAtTime", "playAudio", "audioUrl", "Promise", "resolve", "reject", "onended", "onerror", "getVoiceMessages", "debug", "watch<PERSON><PERSON>y", "fetchPolicy", "valueChanges", "result", "voiceMessages", "length", "Error", "getMessages", "senderId", "receiverId", "conversationId", "page", "variables", "messages", "msg", "normalizeMessage", "editMessage", "messageId", "newContent", "mutate", "mutation", "deleteMessage", "markMessageAsRead", "readAt", "Date", "reactToMessage", "emoji", "forwardMessage", "conversationIds", "timestamp", "normalizeDate", "pinMessage", "pinnedAt", "searchMessages", "filters", "dateFrom", "dateTo", "safeDate", "sender", "normalizeUser", "getUnreadMessages", "userId", "setActiveConversation", "getConversations", "conversations", "conv", "normalizeConversation", "getConversation", "info", "offset", "errorPolicy", "normalizedConversation", "participants", "createConversation", "conversation", "message", "getOrCreateConversation", "currentUserId", "getCurrentUserId", "existingConversation", "find", "isGroup", "participantIds", "p", "_id", "includes", "getNotifications", "refresh", "deletedNotificationIds", "getDeletedNotificationIds", "size", "errors", "e", "join", "getUserNotifications", "filteredNotifications", "notif", "has", "index", "content", "isRead", "updateCache", "cachedNotifications", "sortedNotifications", "sortNotificationsByDate", "saveNotificationsToLocalStorage", "graphQLErrors", "networkError", "deletedIds", "Set", "savedNotificationIds", "n", "serverNotifications", "client", "readQuery", "add", "loadMoreNotifications", "nextPage", "getNotificationById", "getNotificationCount", "value", "getNotificationAttachments", "notificationId", "getUnreadNotifications", "deleteNotification", "warn", "removedCount", "removeNotificationsFromCache", "response", "handleDeletionError", "success", "setItem", "stringify", "deleteAllNotifications", "count", "allNotificationIds", "deleteMultipleNotifications", "notificationIds", "groupNotificationsByType", "groups", "get", "push", "mark<PERSON><PERSON><PERSON>", "readCount", "remainingCount", "validIds", "trim", "provided", "valid", "updateNotificationStatus", "optimisticResponse", "markNotificationsAsRead", "Math", "max", "subscribeToCallSignals", "callId", "callSignal", "signal", "handleCallSignal", "sendCallSignal", "signalType", "signalData", "getCallHistory", "status", "startDate", "endDate", "history", "callHistory", "getCallDetails", "details", "callDetails", "getCallStats", "stats", "callStats", "handleIceCandidate", "handleAnswer", "handleEndCall", "handleRejectCall", "candidate", "addIceCandidate", "RTCIceCandidate", "answer", "setRemoteDescription", "RTCSessionDescription", "cleanupCall", "currentCall", "ENDED", "endTime", "REJECTED", "getTracks", "track", "close", "setupMediaDevices", "callType", "constraints", "video", "AUDIO", "width", "ideal", "height", "observer", "navigator", "mediaDevices", "getUserMedia", "then", "stream", "complete", "generateCallId", "now", "toString", "random", "substring", "getAllUsers", "forceRefresh", "search", "sortBy", "sortOrder", "isOnline", "cacheValid", "paginatedResponse", "users", "user", "getOneUser", "getCurrentUser", "setUserOnline", "setUserOffline", "createGroup", "photo", "description", "group", "updateGroup", "groupId", "input", "deleteGroup", "leaveGroup", "getGroup", "getUserGroups", "subscribeToNewMessages", "tokenValid", "isTokenValid", "sub$", "messageSent", "normalizedMessage", "VOICE_MESSAGE", "attachments", "some", "att", "run", "updateConversationWithNewMessage", "minimalMessage", "TEXT", "username", "sub", "setTimeout", "refreshSenderNotifications", "userStatusChanged", "subscribeToConversationUpdates", "conversationUpdated", "lastMessage", "subscribeToTypingIndicator", "typingIndicator", "Boolean", "token", "parts", "split", "payload", "atob", "exp", "expirationDate", "expiration", "notificationsRead", "source$", "processed$", "notificationReceived", "normalized", "normalizeNotification", "currentNotifications", "existingNotification", "updateNotificationCache", "updatedNotifications", "cleanupInterval", "setInterval", "cleanupExpiredNotifications", "thirtyDaysAgo", "getTime", "expiredCount", "notificationDate", "delete", "remainingNotifications", "sort", "a", "b", "dateA", "dateB", "normalizedSender", "email", "role", "isActive", "normalizedReceiver", "receiver", "normalizedAttachments", "url", "metadata", "String", "image", "bio", "lastActive", "createdAt", "updatedAt", "followingCount", "followersCount", "postCount", "normalizedParticipants", "isArray", "participant", "normalizedMessages", "normalizedLastMessage", "unreadCount", "participantCount", "messageCount", "normalizeSender", "normalizeNotMessage", "skipDuplicates", "notificationArray", "validNotifications", "addedCount", "skippedCount", "notifId", "refreshNotificationObservables", "allNotifications", "unreadNotifications", "dispatchEvent", "CustomEvent", "detail", "ids", "operation", "fallbackResponse", "startTyping", "stopTyping", "sendMessage", "file", "messageType", "hasFile", "fileName", "fileType", "fileSize", "context", "useMultipart", "hasAttachments", "errorMessage", "formatMessageTime", "toLocaleTimeString", "hour", "minute", "hour12", "formatLastActive", "lastActiveDate", "diffHours", "abs", "toLocaleDateString", "formatMessageDate", "today", "toDateString", "yesterday", "setDate", "getDate", "day", "weekday", "toUpperCase", "shouldShowDateHeader", "currentMsg", "prevMsg", "currentDate", "getDateFromTimestamp", "prevDate", "getFileIcon", "mimeType", "startsWith", "getFileType", "typeMap", "key", "entries", "hasImage", "attachment", "isVoiceMessage", "getVoiceMessageUrl", "voiceAttachment", "getVoiceMessageDuration", "getVoiceBarHeight", "pattern", "formatVoiceDuration", "seconds", "minutes", "floor", "remainingSeconds", "padStart", "getImageUrl", "getMessageType", "msgType", "IMAGE", "FILE", "VIDEO", "SYSTEM", "attachmentTypeStr", "getCommonEmojis", "getMessageTypeClass", "isCurrentUser", "baseClass", "cleanupSubscriptions", "unsubscribe", "clearInterval", "ngOnDestroy", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "NgZone", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\message.service.ts"], "sourcesContent": ["import { Injectable, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from '@angular/core';\nimport { <PERSON> } from 'apollo-angular';\nimport {\n  BehaviorSubject,\n  Observable,\n  of,\n  Subscription,\n  throwError,\n  retry,\n  EMPTY,\n} from 'rxjs';\nimport {\n  map,\n  catchError,\n  tap,\n  filter,\n  switchMap,\n  concatMap,\n  toArray,\n} from 'rxjs/operators';\nimport { from } from 'rxjs';\nimport {\n  MessageType,\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSignal,\n  CallOptions,\n  CallFeedback,\n  CallSuccess,\n} from '../models/message.model';\nimport {\n  GET_CONVERSATIONS_QUERY,\n  GET_NOTIFICATIONS_QUERY,\n  NOTIFICATION_SUBSCRIPTION,\n  GET_CONVERSATION_QUERY,\n  SEND_MESSAGE_MUTATION,\n  MARK_AS_READ_MUTATION,\n  MESSAGE_SENT_SUBSCRIPTION,\n  USER_STATUS_SUBSCRIPTION,\n  GET_USER_QUERY,\n  GET_ALL_USER_QUERY,\n  CONVERSATION_UPDATED_SUBSCRIPTION,\n  SEARCH_MESSAGES_QUERY,\n  GET_UNREAD_MESSAGES_QUERY,\n  SET_USER_ONLINE_MUTATION,\n  SET_USER_OFFLINE_MUTATION,\n  START_TYPING_MUTATION,\n  STOP_TYPING_MUTATION,\n  TYPING_INDICATOR_SUBSCRIPTION,\n  GET_CURRENT_USER_QUERY,\n  REACT_TO_MESSAGE_MUTATION,\n  FORWARD_MESSAGE_MUTATION,\n  PIN_MESSAGE_MUTATION,\n  CREATE_GROUP_MUTATION,\n  UPDATE_GROUP_MUTATION,\n  DELETE_GROUP_MUTATION,\n  ADD_GROUP_PARTICIPANTS_MUTATION,\n  REMOVE_GROUP_PARTICIPANTS_MUTATION,\n  LEAVE_GROUP_MUTATION,\n  GET_GROUP_QUERY,\n  GET_USER_GROUPS_QUERY,\n  EDIT_MESSAGE_MUTATION,\n  DELETE_MESSAGE_MUTATION,\n  GET_MESSAGES_QUERY,\n  GET_NOTIFICATIONS_ATTACHAMENTS,\n  MARK_NOTIFICATION_READ_MUTATION,\n  NOTIFICATIONS_READ_SUBSCRIPTION,\n  CREATE_CONVERSATION_MUTATION,\n  DELETE_NOTIFICATION_MUTATION,\n  DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n  DELETE_ALL_NOTIFICATIONS_MUTATION,\n  // Requêtes et mutations pour les appels\n  CALL_HISTORY_QUERY,\n  CALL_DETAILS_QUERY,\n  CALL_STATS_QUERY,\n  INITIATE_CALL_MUTATION,\n  SEND_CALL_SIGNAL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  GET_VOICE_MESSAGES_QUERY,\n} from '../graphql/message.graphql';\nimport {\n  Conversation,\n  Message,\n  Notification,\n  User,\n  Attachment,\n  getNotificationAttachmentsEvent,\n  Group,\n  MessageFilter,\n  TypingIndicatorEvent,\n  GetConversationsResponse,\n  GetConversationResponse,\n  MarkAsReadResponse,\n  ReactToMessageResponse,\n  ForwardMessageResponse,\n  PinMessageResponse,\n  SearchMessagesResponse,\n  SendMessageResponse,\n  GetUnreadMessagesResponse,\n  GetAllUsersResponse,\n  GetOneUserResponse,\n  getCurrentUserResponse,\n  SetUserOnlineResponse,\n  SetUserOfflineResponse,\n  GetGroupResponse,\n  GetUserGroupsResponse,\n  CreateGroupResponse,\n  UpdateGroupResponse,\n  StartTupingResponse,\n  StopTypingResponse,\n  TypingIndicatorEvents,\n  getUserNotificationsResponse,\n  NotificationType,\n  MarkNotificationsAsReadResponse,\n  NotificationReceivedEvent,\n  NotificationsReadEvent,\n} from '../models/message.model';\nimport { LoggerService } from './logger.service';\n@Injectable({\n  providedIn: 'root',\n})\nexport class MessageService implements OnDestroy {\n  // État partagé\n  private activeConversation = new BehaviorSubject<string | null>(null);\n  private notifications = new BehaviorSubject<Notification[]>([]);\n  private notificationCache = new Map<string, Notification>();\n  private cleanupInterval: any;\n  private notificationCount = new BehaviorSubject<number>(0);\n  private onlineUsers = new Map<string, User>();\n  private subscriptions: Subscription[] = [];\n  private readonly CACHE_DURATION = 300000;\n  private lastFetchTime = 0;\n\n  // Propriétés pour les appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private peerConnection: RTCPeerConnection | null = null;\n\n  // Observables publics pour les appels\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n  public localStream$ = new BehaviorSubject<MediaStream | null>(null);\n  public remoteStream$ = new BehaviorSubject<MediaStream | null>(null);\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n    ],\n  };\n  private usersCache: User[] = [];\n\n  // Pagination metadata for user list\n  public currentUserPagination: {\n    totalCount: number;\n    totalPages: number;\n    currentPage: number;\n    hasNextPage: boolean;\n    hasPreviousPage: boolean;\n  } = {\n    totalCount: 0,\n    totalPages: 0,\n    currentPage: 1,\n    hasNextPage: false,\n    hasPreviousPage: false,\n  };\n\n  // Observables publics\n  public activeConversation$ = this.activeConversation.asObservable();\n  public notifications$ = this.notifications.asObservable();\n  public notificationCount$ = this.notificationCount.asObservable();\n\n  // Propriétés pour la gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n  private muted = false;\n\n  constructor(\n    private apollo: Apollo,\n    private logger: LoggerService,\n    private zone: NgZone\n  ) {\n    this.loadNotificationsFromLocalStorage();\n    this.initSubscriptions();\n    this.startCleanupInterval();\n    this.preloadSounds();\n  }\n\n  /**\n   * Charge les notifications depuis le localStorage\n   * @private\n   */\n  private loadNotificationsFromLocalStorage(): void {\n    try {\n      const savedNotifications = localStorage.getItem('notifications');\n      if (savedNotifications) {\n        const notifications = JSON.parse(savedNotifications) as Notification[];\n\n        this.notificationCache.clear();\n\n        notifications.forEach((notification) => {\n          if (notification && notification.id) {\n            this.notificationCache.set(notification.id, notification);\n          }\n        });\n\n        this.notifications.next(Array.from(this.notificationCache.values()));\n        this.updateUnreadCount();\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n  private initSubscriptions(): void {\n    this.zone.runOutsideAngular(() => {\n      this.subscribeToNewNotifications().subscribe();\n      this.subscribeToNotificationsRead().subscribe();\n      this.subscribeToIncomingCalls().subscribe();\n      // 🔥 AJOUT: Subscription générale pour l'utilisateur\n    });\n    this.subscribeToUserStatus();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): Observable<IncomingCall | null> {\n    return this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.incomingCall) {\n            return null;\n          }\n\n          // Gérer l'appel entrant\n          this.handleIncomingCall(data.incomingCall);\n          return data.incomingCall;\n        }),\n        catchError((error) => {\n          this.logger.error('Error in incoming call subscription', error);\n          return of(null);\n        })\n      );\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.incomingCall.next(call);\n    this.play('ringtone', true);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des sons (intégré depuis SoundService)\n  // --------------------------------------------------------------------------\n\n  /**\n   * Précharge les sons utilisés dans l'application\n   */\n  private preloadSounds(): void {\n    this.loadSound('ringtone', 'assets/sounds/ringtone.mp3');\n    this.loadSound('call-end', 'assets/sounds/call-end.mp3');\n    this.loadSound('call-connected', 'assets/sounds/call-connected.mp3');\n    this.loadSound('notification', 'assets/sounds/notification.mp3');\n  }\n\n  /**\n   * Charge un fichier audio\n   * @param name Nom du son\n   * @param path Chemin du fichier\n   */\n  private loadSound(name: string, path: string): void {\n    try {\n      const audio = new Audio(path);\n      audio.load();\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      audio.addEventListener('ended', () => {\n        this.isPlaying[name] = false;\n      });\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Joue un son\n   * @param name Nom du son\n   * @param loop Lecture en boucle\n   */\n  play(name: string, loop: boolean = false): void {\n    if (this.muted) {\n      return;\n    }\n\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      sound.loop = loop;\n\n      if (!this.isPlaying[name]) {\n        sound.currentTime = 0;\n        sound.play().catch((error) => {\n          // Handle error silently\n        });\n        this.isPlaying[name] = true;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête un son\n   * @param name Nom du son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        return;\n      }\n\n      if (this.isPlaying[name]) {\n        sound.pause();\n        sound.currentTime = 0;\n        this.isPlaying[name] = false;\n      }\n    } catch (error) {\n      // Handle error silently\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Active ou désactive le son\n   * @param muted True pour désactiver le son, false pour l'activer\n   */\n  setMuted(muted: boolean): void {\n    this.muted = muted;\n\n    if (muted) {\n      this.stopAllSounds();\n    }\n  }\n\n  /**\n   * Vérifie si le son est désactivé\n   * @returns True si le son est désactivé, false sinon\n   */\n  isMuted(): boolean {\n    return this.muted;\n  }\n\n  /**\n   * Joue le son de notification\n   */\n  playNotificationSound(): void {\n    console.log('MessageService: Tentative de lecture du son de notification');\n\n    if (this.muted) {\n      console.log('MessageService: Son désactivé, notification ignorée');\n      return;\n    }\n\n    // Créer une mélodie agréable avec l'API Web Audio\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // 🎵 TESTEZ DIFFÉRENTS SONS - Décommentez celui que vous voulez tester !\n\n      // SON 1: Mélodie douce (WhatsApp style) - ACTUEL\n      this.playNotificationMelody1(audioContext);\n\n      // SON 2: Mélodie montante (iPhone style) - Décommentez pour tester\n      // this.playNotificationMelody2(audioContext);\n\n      // SON 3: Mélodie descendante (Messenger style) - Décommentez pour tester\n      // this.playNotificationMelody3(audioContext);\n\n      // SON 4: Triple note (Discord style) - Décommentez pour tester\n      // this.playNotificationMelody4(audioContext);\n\n      // SON 5: Cloche douce (Slack style) - Décommentez pour tester\n      // this.playNotificationMelody5(audioContext);\n\n      console.log(\n        'MessageService: Son de notification mélodieux généré avec succès'\n      );\n    } catch (error) {\n      console.error(\n        'MessageService: Erreur lors de la génération du son:',\n        error\n      );\n\n      // Fallback à la méthode originale en cas d'erreur\n      try {\n        const audio = new Audio('assets/sounds/notification.mp3');\n        audio.volume = 0.7; // Volume plus doux\n        audio.play().catch((err) => {\n          console.error(\n            'MessageService: Erreur lors de la lecture du fichier son:',\n            err\n          );\n        });\n      } catch (audioError) {\n        console.error(\n          'MessageService: Exception lors de la lecture du fichier son:',\n          audioError\n        );\n      }\n    }\n  }\n\n  // 🎵 SON 1: Mélodie douce (WhatsApp style)\n  private playNotificationMelody1(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 659.25, 0.15); // E5\n    this.playNotificationTone(audioContext, 0.15, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 2: Mélodie montante (iPhone style)\n  private playNotificationMelody2(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 523.25, 0.12); // C5\n    this.playNotificationTone(audioContext, 0.12, 659.25, 0.12); // E5\n    this.playNotificationTone(audioContext, 0.24, 783.99, 0.16); // G5\n  }\n\n  // 🎵 SON 3: Mélodie descendante (Messenger style)\n  private playNotificationMelody3(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 880, 0.1); // A5\n    this.playNotificationTone(audioContext, 0.1, 659.25, 0.1); // E5\n    this.playNotificationTone(audioContext, 0.2, 523.25, 0.15); // C5\n  }\n\n  // 🎵 SON 4: Triple note (Discord style)\n  private playNotificationMelody4(audioContext: AudioContext): void {\n    this.playNotificationTone(audioContext, 0, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.08, 698.46, 0.08); // F5\n    this.playNotificationTone(audioContext, 0.16, 880, 0.12); // A5\n  }\n\n  // 🎵 SON 5: Cloche douce (Slack style)\n  private playNotificationMelody5(audioContext: AudioContext): void {\n    this.playBellTone(audioContext, 0, 1046.5, 0.4); // C6 - son de cloche\n  }\n\n  /**\n   * Joue une note individuelle pour la mélodie de notification\n   */\n  private playNotificationTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son plus doux\n    oscillator.type = 'sine';\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Configurer le volume avec une enveloppe douce\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.3,\n      audioContext.currentTime + startTime + 0.02\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0.2,\n      audioContext.currentTime + startTime + duration * 0.7\n    );\n    gainNode.gain.linearRampToValueAtTime(\n      0,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n\n  /**\n   * Joue un son de cloche pour les notifications\n   */\n  private playBellTone(\n    audioContext: AudioContext,\n    startTime: number,\n    frequency: number,\n    duration: number\n  ): void {\n    const oscillator = audioContext.createOscillator();\n    const gainNode = audioContext.createGain();\n\n    // Configurer l'oscillateur pour un son de cloche\n    oscillator.type = 'triangle'; // Son plus doux que sine\n    oscillator.frequency.setValueAtTime(\n      frequency,\n      audioContext.currentTime + startTime\n    );\n\n    // Enveloppe de cloche (attaque rapide, déclin lent)\n    gainNode.gain.setValueAtTime(0, audioContext.currentTime + startTime);\n    gainNode.gain.linearRampToValueAtTime(\n      0.4,\n      audioContext.currentTime + startTime + 0.01\n    );\n    gainNode.gain.exponentialRampToValueAtTime(\n      0.01,\n      audioContext.currentTime + startTime + duration\n    );\n\n    // Connecter les nœuds\n    oscillator.connect(gainNode);\n    gainNode.connect(audioContext.destination);\n\n    // Démarrer et arrêter l'oscillateur\n    oscillator.start(audioContext.currentTime + startTime);\n    oscillator.stop(audioContext.currentTime + startTime + duration);\n  }\n  // --------------------------------------------------------------------------\n  // Section 1: Méthodes pour les Messages\n  // --------------------------------------------------------------------------\n\n  /**\n   * Joue un fichier audio\n   * @param audioUrl URL du fichier audio à jouer\n   * @returns Promise qui se résout lorsque la lecture est terminée\n   */\n  playAudio(audioUrl: string): Promise<void> {\n    return new Promise((resolve, reject) => {\n      const audio = new Audio(audioUrl);\n\n      audio.onended = () => {\n        resolve();\n      };\n\n      audio.onerror = (error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      };\n\n      audio.play().catch((error) => {\n        this.logger.error(`[MessageService] Error playing audio:`, error);\n        reject(error);\n      });\n    });\n  }\n\n  /**\n   * Récupère tous les messages vocaux de l'utilisateur\n   * @returns Observable avec la liste des messages vocaux\n   */\n  getVoiceMessages(): Observable<Call[]> {\n    this.logger.debug('[MessageService] Getting voice messages');\n\n    return this.apollo\n      .watchQuery<{ getVoiceMessages: Call[] }>({\n        query: GET_VOICE_MESSAGES_QUERY,\n        fetchPolicy: 'network-only', // Ne pas utiliser le cache pour cette requête\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const voiceMessages = result.data?.getVoiceMessages || [];\n          this.logger.debug(\n            `[MessageService] Retrieved ${voiceMessages.length} voice messages`\n          );\n          return voiceMessages;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            '[MessageService] Error fetching voice messages:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch voice messages'));\n        })\n      );\n  }\n  // Message methods\n  getMessages(\n    senderId: string,\n    receiverId: string,\n    conversationId: string,\n    page: number = 1,\n    limit: number = 10\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<{ getMessages: Message[] }>({\n        query: GET_MESSAGES_QUERY,\n        variables: { senderId, receiverId, conversationId, limit, page },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const messages = result.data?.getMessages || [];\n          return messages.map((msg) => this.normalizeMessage(msg));\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching messages:', error);\n          return throwError(() => new Error('Failed to fetch messages'));\n        })\n      );\n  }\n  editMessage(messageId: string, newContent: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ editMessage: Message }>({\n        mutation: EDIT_MESSAGE_MUTATION,\n        variables: { messageId, newContent },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.editMessage) {\n            throw new Error('Failed to edit message');\n          }\n          return this.normalizeMessage(result.data.editMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error editing message:', error);\n          return throwError(() => new Error('Failed to edit message'));\n        })\n      );\n  }\n\n  deleteMessage(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<{ deleteMessage: Message }>({\n        mutation: DELETE_MESSAGE_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.deleteMessage) {\n            throw new Error('Failed to delete message');\n          }\n          return this.normalizeMessage(result.data.deleteMessage);\n        }),\n        catchError((error) => {\n          this.logger.error('Error deleting message:', error);\n          return throwError(() => new Error('Failed to delete message'));\n        })\n      );\n  }\n\n  markMessageAsRead(messageId: string): Observable<Message> {\n    return this.apollo\n      .mutate<MarkAsReadResponse>({\n        mutation: MARK_AS_READ_MUTATION,\n        variables: { messageId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.markMessageAsRead)\n            throw new Error('Failed to mark message as read');\n          return {\n            ...result.data.markMessageAsRead,\n            readAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error marking message as read:', error);\n          return throwError(() => new Error('Failed to mark message as read'));\n        })\n      );\n  }\n\n  reactToMessage(messageId: string, emoji: string): Observable<Message> {\n    return this.apollo\n      .mutate<ReactToMessageResponse>({\n        mutation: REACT_TO_MESSAGE_MUTATION,\n        variables: { messageId, emoji },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.reactToMessage)\n            throw new Error('Failed to react to message');\n          return result.data.reactToMessage;\n        }),\n        catchError((error) => {\n          console.error('Error reacting to message:', error);\n          return throwError(() => new Error('Failed to react to message'));\n        })\n      );\n  }\n\n  forwardMessage(\n    messageId: string,\n    conversationIds: string[]\n  ): Observable<Message[]> {\n    return this.apollo\n      .mutate<ForwardMessageResponse>({\n        mutation: FORWARD_MESSAGE_MUTATION,\n        variables: { messageId, conversationIds },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.forwardMessage)\n            throw new Error('Failed to forward message');\n          return result.data.forwardMessage.map((msg) => ({\n            ...msg,\n            timestamp: msg.timestamp\n              ? this.normalizeDate(msg.timestamp)\n              : new Date(),\n          }));\n        }),\n        catchError((error) => {\n          console.error('Error forwarding message:', error);\n          return throwError(() => new Error('Failed to forward message'));\n        })\n      );\n  }\n\n  pinMessage(messageId: string, conversationId: string): Observable<Message> {\n    return this.apollo\n      .mutate<PinMessageResponse>({\n        mutation: PIN_MESSAGE_MUTATION,\n        variables: { messageId, conversationId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.pinMessage)\n            throw new Error('Failed to pin message');\n          return {\n            ...result.data.pinMessage,\n            pinnedAt: new Date(),\n          };\n        }),\n        catchError((error) => {\n          console.error('Error pinning message:', error);\n          return throwError(() => new Error('Failed to pin message'));\n        })\n      );\n  }\n\n  searchMessages(\n    query: string,\n    conversationId?: string,\n    filters: MessageFilter = {}\n  ): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<SearchMessagesResponse>({\n        query: SEARCH_MESSAGES_QUERY,\n        variables: {\n          query,\n          conversationId,\n          ...filters,\n          dateFrom: this.toSafeISOString(filters.dateFrom),\n          dateTo: this.toSafeISOString(filters.dateTo),\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.searchMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error searching messages:', error);\n          return throwError(() => new Error('Failed to search messages'));\n        })\n      );\n  }\n\n  getUnreadMessages(userId: string): Observable<Message[]> {\n    return this.apollo\n      .watchQuery<GetUnreadMessagesResponse>({\n        query: GET_UNREAD_MESSAGES_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map(\n          (result) =>\n            result.data?.getUnreadMessages?.map((msg) => ({\n              ...msg,\n              timestamp: this.safeDate(msg.timestamp),\n              sender: this.normalizeUser(msg.sender),\n            })) || []\n        ),\n        catchError((error) => {\n          console.error('Error fetching unread messages:', error);\n          return throwError(() => new Error('Failed to fetch unread messages'));\n        })\n      );\n  }\n\n  setActiveConversation(conversationId: string): void {\n    this.activeConversation.next(conversationId);\n  }\n\n  getConversations(): Observable<Conversation[]> {\n    return this.apollo\n      .watchQuery<GetConversationsResponse>({\n        query: GET_CONVERSATIONS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const conversations = result.data?.getConversations || [];\n          return conversations.map((conv) => this.normalizeConversation(conv));\n        }),\n        catchError((error) => {\n          console.error('Error fetching conversations:', error);\n          return throwError(() => new Error('Failed to load conversations'));\n        })\n      );\n  }\n\n  getConversation(\n    conversationId: string,\n    limit?: number,\n    page?: number\n  ): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting conversation: ${conversationId}, limit: ${limit}, page: ${page}`\n    );\n\n    const variables: any = { conversationId };\n\n    // Ajouter les paramètres de pagination s'ils sont fournis\n    if (limit !== undefined) {\n      variables.limit = limit;\n    } else {\n      variables.limit = 10; // Valeur par défaut\n    }\n\n    // Calculer l'offset à partir de la page si elle est fournie\n    if (page !== undefined) {\n      // La requête GraphQL utilise offset, donc nous devons convertir la page en offset\n      const offset = (page - 1) * variables.limit;\n      variables.offset = offset;\n      this.logger.debug(\n        `[MessageService] Calculated offset: ${offset} from page: ${page} and limit: ${variables.limit}`\n      );\n    } else {\n      variables.offset = 0; // Valeur par défaut\n    }\n\n    this.logger.debug(\n      `[MessageService] Final pagination parameters: limit=${variables.limit}, offset=${variables.offset}`\n    );\n\n    return this.apollo\n      .watchQuery<GetConversationResponse>({\n        query: GET_CONVERSATION_QUERY,\n        variables: variables,\n        fetchPolicy: 'network-only',\n        errorPolicy: 'all',\n      })\n      .valueChanges.pipe(\n        retry(2), // Réessayer 2 fois en cas d'erreur\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation response received:`,\n            result\n          );\n\n          const conv = result.data?.getConversation;\n          if (!conv) {\n            this.logger.error(\n              `[MessageService] Conversation not found: ${conversationId}`\n            );\n            throw new Error('Conversation not found');\n          }\n\n          this.logger.debug(\n            `[MessageService] Normalizing conversation: ${conversationId}`\n          );\n          const normalizedConversation = this.normalizeConversation(conv);\n\n          this.logger.info(\n            `[MessageService] Conversation loaded successfully: ${conversationId}, participants: ${\n              normalizedConversation.participants?.length || 0\n            }, messages: ${normalizedConversation.messages?.length || 0}`\n          );\n          return normalizedConversation;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error fetching conversation:`,\n            error\n          );\n          return throwError(() => new Error('Failed to load conversation'));\n        })\n      );\n  }\n\n  createConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to create a conversation')\n      );\n    }\n\n    return this.apollo\n      .mutate<{ createConversation: Conversation }>({\n        mutation: CREATE_CONVERSATION_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug(\n            `[MessageService] Conversation creation response:`,\n            result\n          );\n\n          const conversation = result.data?.createConversation;\n          if (!conversation) {\n            this.logger.error(\n              `[MessageService] Failed to create conversation with user: ${userId}`\n            );\n            throw new Error('Failed to create conversation');\n          }\n\n          try {\n            const normalizedConversation =\n              this.normalizeConversation(conversation);\n            this.logger.info(\n              `[MessageService] Conversation created successfully: ${normalizedConversation.id}`\n            );\n            return normalizedConversation;\n          } catch (error) {\n            this.logger.error(\n              `[MessageService] Error normalizing created conversation:`,\n              error\n            );\n            throw new Error('Error processing created conversation');\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            `[MessageService] Error creating conversation with user ${userId}:`,\n            error\n          );\n          return throwError(\n            () => new Error(`Failed to create conversation: ${error.message}`)\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère une conversation existante ou en crée une nouvelle si elle n'existe pas\n   * @param userId ID de l'utilisateur avec qui créer/récupérer une conversation\n   * @returns Observable avec la conversation\n   */\n  getOrCreateConversation(userId: string): Observable<Conversation> {\n    this.logger.info(\n      `[MessageService] Getting or creating conversation with user: ${userId}`\n    );\n\n    if (!userId) {\n      this.logger.error(\n        `[MessageService] Cannot get/create conversation: userId is undefined`\n      );\n      return throwError(\n        () => new Error('User ID is required to get/create a conversation')\n      );\n    }\n\n    // D'abord, essayons de trouver une conversation existante entre les deux utilisateurs\n    return this.getConversations().pipe(\n      map((conversations) => {\n        // Récupérer l'ID de l'utilisateur actuel\n        const currentUserId = this.getCurrentUserId();\n\n        // Chercher une conversation directe (non groupe) entre les deux utilisateurs\n        const existingConversation = conversations.find((conv) => {\n          if (conv.isGroup) return false;\n\n          // Vérifier si la conversation contient les deux utilisateurs\n          const participantIds =\n            conv.participants?.map((p) => p.id || p._id) || [];\n          return (\n            participantIds.includes(userId) &&\n            participantIds.includes(currentUserId)\n          );\n        });\n\n        if (existingConversation) {\n          this.logger.info(\n            `[MessageService] Found existing conversation: ${existingConversation.id}`\n          );\n          return existingConversation;\n        }\n\n        // Si aucune conversation n'est trouvée, en créer une nouvelle\n        throw new Error('No existing conversation found');\n      }),\n      catchError((error) => {\n        this.logger.info(\n          `[MessageService] No existing conversation found, creating new one: ${error.message}`\n        );\n        return this.createConversation(userId);\n      })\n    );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 2: Méthodes pour les Notifications\n  // --------------------------------------------------------------------------\n  // Propriétés pour la pagination des notifications\n  private notificationPagination = {\n    currentPage: 1,\n    limit: 10,\n    hasMoreNotifications: true,\n  };\n\n  getNotifications(\n    refresh = false,\n    page = 1,\n    limit = 10\n  ): Observable<Notification[]> {\n    this.logger.info(\n      'MessageService',\n      `Fetching notifications, refresh: ${refresh}, page: ${page}, limit: ${limit}`\n    );\n    this.logger.debug('MessageService', 'Using query', {\n      query: GET_NOTIFICATIONS_QUERY,\n    });\n\n    // Si refresh est true, réinitialiser la pagination mais ne pas vider le cache\n    // pour conserver les suppressions locales\n    if (refresh) {\n      this.logger.debug(\n        'MessageService',\n        'Resetting pagination due to refresh'\n      );\n      this.notificationPagination.currentPage = 1;\n      this.notificationPagination.hasMoreNotifications = true;\n    }\n\n    // Mettre à jour les paramètres de pagination\n    this.notificationPagination.currentPage = page;\n    this.notificationPagination.limit = limit;\n\n    // Récupérer les IDs des notifications supprimées du localStorage\n    const deletedNotificationIds = this.getDeletedNotificationIds();\n    this.logger.debug(\n      'MessageService',\n      `Found ${deletedNotificationIds.size} deleted notification IDs in localStorage`\n    );\n\n    return this.apollo\n      .watchQuery<getUserNotificationsResponse>({\n        query: GET_NOTIFICATIONS_QUERY,\n        variables: {\n          page: page,\n          limit: limit,\n        },\n        fetchPolicy: refresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Notifications response received'\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          const notifications = result.data?.getUserNotifications || [];\n          this.logger.debug(\n            'MessageService',\n            `Received ${notifications.length} notifications from server for page ${page}`\n          );\n\n          // Vérifier s'il y a plus de notifications à charger\n          this.notificationPagination.hasMoreNotifications =\n            notifications.length >= limit;\n\n          if (notifications.length === 0) {\n            this.logger.info(\n              'MessageService',\n              'No notifications received from server'\n            );\n            this.notificationPagination.hasMoreNotifications = false;\n          }\n\n          // Filtrer les notifications supprimées\n          const filteredNotifications = notifications.filter(\n            (notif) => !deletedNotificationIds.has(notif.id)\n          );\n\n          this.logger.debug(\n            'MessageService',\n            `Filtered out ${\n              notifications.length - filteredNotifications.length\n            } deleted notifications`\n          );\n\n          // Afficher les notifications reçues pour le débogage\n          filteredNotifications.forEach((notif, index) => {\n            console.log(`Notification ${index + 1} (page ${page}):`, {\n              id: notif.id || (notif as any)._id,\n              type: notif.type,\n              content: notif.content,\n              isRead: notif.isRead,\n            });\n          });\n\n          // Vérifier si les notifications existent déjà dans le cache avant de les ajouter\n          // Mettre à jour le cache avec les nouvelles notifications\n          this.updateCache(filteredNotifications);\n\n          // Récupérer toutes les notifications du cache et les TRIER\n          const cachedNotifications = Array.from(\n            this.notificationCache.values()\n          );\n\n          // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n          const sortedNotifications =\n            this.sortNotificationsByDate(cachedNotifications);\n\n          console.log(\n            `📊 SORTED: ${sortedNotifications.length} notifications triées (plus récentes en premier)`\n          );\n\n          // Mettre à jour le BehaviorSubject avec les notifications TRIÉES\n          this.notifications.next(sortedNotifications);\n\n          // Mettre à jour le compteur de notifications non lues\n          this.updateUnreadCount();\n\n          // Sauvegarder les notifications dans le localStorage\n          this.saveNotificationsToLocalStorage();\n\n          return cachedNotifications;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error loading notifications:',\n            error\n          );\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          return throwError(() => new Error('Failed to load notifications'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les IDs des notifications supprimées du localStorage\n   * @private\n   * @returns Set contenant les IDs des notifications supprimées\n   */\n  private getDeletedNotificationIds(): Set<string> {\n    try {\n      const deletedIds = new Set<string>();\n      const savedNotifications = localStorage.getItem('notifications');\n\n      // Si aucune notification n'est sauvegardée, retourner un ensemble vide\n      if (!savedNotifications) {\n        return deletedIds;\n      }\n\n      // Récupérer les IDs des notifications sauvegardées\n      const savedNotificationIds = new Set(\n        JSON.parse(savedNotifications).map((n: Notification) => n.id)\n      );\n\n      // Récupérer les notifications du serveur (si disponibles dans le cache Apollo)\n      const serverNotifications =\n        this.apollo.client.readQuery<getUserNotificationsResponse>({\n          query: GET_NOTIFICATIONS_QUERY,\n        })?.getUserNotifications || [];\n\n      // Pour chaque notification du serveur, vérifier si elle est dans les notifications sauvegardées\n      serverNotifications.forEach((notification) => {\n        if (!savedNotificationIds.has(notification.id)) {\n          deletedIds.add(notification.id);\n        }\n      });\n\n      return deletedIds;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la récupération des IDs de notifications supprimées:',\n        error\n      );\n      return new Set<string>();\n    }\n  }\n\n  // Méthode pour vérifier s'il y a plus de notifications à charger\n  hasMoreNotifications(): boolean {\n    return this.notificationPagination.hasMoreNotifications;\n  }\n\n  // Méthode pour charger la page suivante de notifications\n  loadMoreNotifications(): Observable<Notification[]> {\n    const nextPage = this.notificationPagination.currentPage + 1;\n    return this.getNotifications(\n      false,\n      nextPage,\n      this.notificationPagination.limit\n    );\n  }\n  getNotificationById(id: string): Observable<Notification | undefined> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.find((n) => n.id === id)),\n      catchError((error) => {\n        this.logger.error('Error finding notification:', error);\n        return throwError(() => new Error('Failed to find notification'));\n      })\n    );\n  }\n  getNotificationCount(): number {\n    return this.notifications.value?.length || 0;\n  }\n  getNotificationAttachments(notificationId: string): Observable<Attachment[]> {\n    return this.apollo\n      .query<getNotificationAttachmentsEvent>({\n        query: GET_NOTIFICATIONS_ATTACHAMENTS,\n        variables: { id: notificationId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result) => result.data?.getNotificationAttachments || []),\n        catchError((error) => {\n          this.logger.error('Error fetching notification attachments:', error);\n          return throwError(() => new Error('Failed to fetch attachments'));\n        })\n      );\n  }\n  getUnreadNotifications(): Observable<Notification[]> {\n    return this.notifications$.pipe(\n      map((notifications) => notifications.filter((n) => !n.isRead))\n    );\n  }\n\n  /**\n   * Supprime une notification\n   * @param notificationId ID de la notification à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteNotification(\n    notificationId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de la notification ${notificationId}`\n    );\n\n    if (!notificationId) {\n      this.logger.warn('MessageService', 'ID de notification invalide');\n      return throwError(() => new Error('ID de notification invalide'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const removedCount = this.removeNotificationsFromCache([notificationId]);\n\n    // Appeler le backend pour supprimer la notification\n    return this.apollo\n      .mutate<{ deleteNotification: { success: boolean; message: string } }>({\n        mutation: DELETE_NOTIFICATION_MUTATION,\n        variables: { notificationId },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteNotification;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(error, 'la suppression de la notification', {\n            success: true,\n            message: 'Notification supprimée localement (erreur serveur)',\n          })\n        )\n      );\n  }\n\n  /**\n   * Sauvegarde les notifications dans le localStorage\n   * @private\n   */\n  private saveNotificationsToLocalStorage(): void {\n    try {\n      const notifications = Array.from(this.notificationCache.values());\n      localStorage.setItem('notifications', JSON.stringify(notifications));\n      this.logger.debug(\n        'MessageService',\n        'Notifications sauvegardées localement'\n      );\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Erreur lors de la sauvegarde des notifications:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Supprime toutes les notifications de l'utilisateur\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteAllNotifications(): Observable<{\n    success: boolean;\n    count: number;\n    message: string;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      'Suppression de toutes les notifications'\n    );\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.notificationCache.size;\n    const allNotificationIds = Array.from(this.notificationCache.keys());\n    this.removeNotificationsFromCache(allNotificationIds);\n\n    // Appeler le backend pour supprimer toutes les notifications\n    return this.apollo\n      .mutate<{\n        deleteAllNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_ALL_NOTIFICATIONS_MUTATION,\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteAllNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression de toutes les notifications:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression de toutes les notifications',\n            {\n              success: true,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n\n  /**\n   * Supprime plusieurs notifications\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Observable avec le résultat de l'opération\n   */\n  deleteMultipleNotifications(\n    notificationIds: string[]\n  ): Observable<{ success: boolean; count: number; message: string }> {\n    this.logger.debug(\n      'MessageService',\n      `Suppression de ${notificationIds.length} notifications`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'Aucun ID de notification fourni');\n      return throwError(() => new Error('Aucun ID de notification fourni'));\n    }\n\n    // Supprimer localement d'abord pour une meilleure expérience utilisateur\n    const count = this.removeNotificationsFromCache(notificationIds);\n\n    // Appeler le backend pour supprimer les notifications\n    return this.apollo\n      .mutate<{\n        deleteMultipleNotifications: {\n          success: boolean;\n          count: number;\n          message: string;\n        };\n      }>({\n        mutation: DELETE_MULTIPLE_NOTIFICATIONS_MUTATION,\n        variables: { notificationIds },\n      })\n      .pipe(\n        map((result) => {\n          const response = result.data?.deleteMultipleNotifications;\n          if (!response) {\n            throw new Error('Réponse de suppression invalide');\n          }\n\n          this.logger.debug(\n            'MessageService',\n            'Résultat de la suppression multiple:',\n            response\n          );\n\n          return response;\n        }),\n        catchError((error) =>\n          this.handleDeletionError(\n            error,\n            'la suppression multiple de notifications',\n            {\n              success: count > 0,\n              count,\n              message: `${count} notifications supprimées localement (erreur serveur)`,\n            }\n          )\n        )\n      );\n  }\n  groupNotificationsByType(): Observable<\n    Map<NotificationType, Notification[]>\n  > {\n    return this.notifications$.pipe(\n      map((notifications) => {\n        const groups = new Map<NotificationType, Notification[]>();\n        notifications.forEach((notif) => {\n          if (!groups.has(notif.type)) {\n            groups.set(notif.type, []);\n          }\n          groups.get(notif.type)?.push(notif);\n        });\n        return groups;\n      })\n    );\n  }\n  markAsRead(notificationIds: string[]): Observable<{\n    success: boolean;\n    readCount: number;\n    remainingCount: number;\n  }> {\n    this.logger.debug(\n      'MessageService',\n      `Marking notifications as read: ${notificationIds?.join(', ') || 'none'}`\n    );\n\n    if (!notificationIds || notificationIds.length === 0) {\n      this.logger.warn('MessageService', 'No notification IDs provided');\n      return of({\n        success: false,\n        readCount: 0,\n        remainingCount: this.notificationCount.value,\n      });\n    }\n\n    // Vérifier que tous les IDs sont valides\n    const validIds = notificationIds.filter(\n      (id) => id && typeof id === 'string' && id.trim() !== ''\n    );\n\n    if (validIds.length !== notificationIds.length) {\n      this.logger.error('MessageService', 'Some notification IDs are invalid', {\n        provided: notificationIds,\n        valid: validIds,\n      });\n      return throwError(() => new Error('Some notification IDs are invalid'));\n    }\n\n    this.logger.debug(\n      'MessageService',\n      'Sending mutation to mark notifications as read',\n      validIds\n    );\n\n    // Mettre à jour localement d'abord pour une meilleure expérience utilisateur\n    this.updateNotificationStatus(validIds, true);\n\n    // Créer une réponse optimiste\n    const optimisticResponse = {\n      markNotificationsAsRead: {\n        success: true,\n        readCount: validIds.length,\n        remainingCount: Math.max(\n          0,\n          this.notificationCount.value - validIds.length\n        ),\n      },\n    };\n\n    // Afficher des informations de débogage supplémentaires\n    console.log('Sending markNotificationsAsRead mutation with variables:', {\n      notificationIds: validIds,\n    });\n    console.log('Using mutation:', MARK_NOTIFICATION_READ_MUTATION);\n\n    return this.apollo\n      .mutate<MarkNotificationsAsReadResponse>({\n        mutation: MARK_NOTIFICATION_READ_MUTATION,\n        variables: { notificationIds: validIds },\n        optimisticResponse: optimisticResponse,\n        errorPolicy: 'all', // Continuer même en cas d'erreur\n        fetchPolicy: 'no-cache', // Ne pas utiliser le cache pour cette mutation\n      })\n      .pipe(\n        map((result) => {\n          this.logger.debug('MessageService', 'Mutation result', result);\n          console.log('Mutation result:', result);\n\n          // Si nous avons des erreurs GraphQL, les logger mais continuer\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              result.errors\n            );\n            console.error('GraphQL errors:', result.errors);\n          }\n\n          // Utiliser la réponse du serveur ou notre réponse optimiste\n          const response =\n            result.data?.markNotificationsAsRead ??\n            optimisticResponse.markNotificationsAsRead;\n\n          return response;\n        }),\n        catchError((error: Error) => {\n          this.logger.error(\n            'MessageService',\n            'Error marking notifications as read:',\n            error\n          );\n          console.error('Error in markAsRead:', error);\n\n          // En cas d'erreur, retourner quand même un succès simulé\n          // puisque nous avons déjà mis à jour l'interface utilisateur\n          return of({\n            success: true,\n            readCount: validIds.length,\n            remainingCount: Math.max(\n              0,\n              this.notificationCount.value - validIds.length\n            ),\n          });\n        })\n      );\n  }\n  // --------------------------------------------------------------------------\n  // Section 3: Méthodes pour les Appels (SUPPRIMÉES - VOIR SECTION À LA FIN)\n  // --------------------------------------------------------------------------\n\n  /**\n   * S'abonne aux signaux d'appel\n   * @param callId ID de l'appel\n   * @returns Observable avec les signaux d'appel\n   */\n  subscribeToCallSignals(callId: string): Observable<CallSignal> {\n    return this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        variables: { callId },\n      })\n      .pipe(\n        map(({ data }) => {\n          if (!data?.callSignal) {\n            throw new Error('No call signal received');\n          }\n          return data.callSignal;\n        }),\n        tap((signal) => {\n          this.callSignals.next(signal);\n          this.handleCallSignal(signal);\n        }),\n        catchError((error) => {\n          this.logger.error('Error in call signal subscription', error);\n          return throwError(() => new Error('Call signal subscription failed'));\n        })\n      );\n  }\n\n  /**\n   * Envoie un signal d'appel\n   * @param callId ID de l'appel\n   * @param signalType Type de signal\n   * @param signalData Données du signal\n   * @returns Observable avec le résultat de l'opération\n   */\n  sendCallSignal(\n    callId: string,\n    signalType: string,\n    signalData: string\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ sendCallSignal: CallSuccess }>({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId,\n          signalType,\n          signalData,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.sendCallSignal;\n          if (!success) {\n            throw new Error('Failed to send call signal');\n          }\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('Error sending call signal', error);\n          return throwError(() => new Error('Failed to send call signal'));\n        })\n      );\n  }\n\n  /**\n   * Récupère l'historique des appels avec filtres\n   * @param limit Nombre d'appels à récupérer\n   * @param offset Décalage pour la pagination\n   * @param status Filtres de statut\n   * @param type Filtres de type\n   * @param startDate Date de début\n   * @param endDate Date de fin\n   * @returns Observable avec l'historique des appels\n   */\n  getCallHistory(\n    limit: number = 20,\n    offset: number = 0,\n    status?: string[],\n    type?: string[],\n    startDate?: string | null,\n    endDate?: string | null\n  ): Observable<Call[]> {\n    return this.apollo\n      .watchQuery<{ callHistory: Call[] }>({\n        query: CALL_HISTORY_QUERY,\n        variables: {\n          limit,\n          offset,\n          status,\n          type,\n          startDate,\n          endDate,\n        },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const history = result.data?.callHistory || [];\n          this.logger.debug(`Retrieved ${history.length} call history items`);\n          return history;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call history:', error);\n          return throwError(() => new Error('Failed to fetch call history'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les détails d'un appel spécifique\n   * @param callId ID de l'appel\n   * @returns Observable avec les détails de l'appel\n   */\n  getCallDetails(callId: string): Observable<Call> {\n    return this.apollo\n      .watchQuery<{ callDetails: Call }>({\n        query: CALL_DETAILS_QUERY,\n        variables: { callId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const details = result.data?.callDetails;\n          if (!details) {\n            throw new Error('Call details not found');\n          }\n          this.logger.debug(`Retrieved call details for: ${callId}`);\n          return details;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call details:', error);\n          return throwError(() => new Error('Failed to fetch call details'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les statistiques d'appels\n   * @returns Observable avec les statistiques d'appels\n   */\n  getCallStats(): Observable<any> {\n    return this.apollo\n      .watchQuery<{ callStats: any }>({\n        query: CALL_STATS_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          const stats = result.data?.callStats;\n          if (!stats) {\n            throw new Error('Call stats not found');\n          }\n          this.logger.debug('Retrieved call stats:', stats);\n          return stats;\n        }),\n        catchError((error) => {\n          this.logger.error('Error fetching call stats:', error);\n          return throwError(() => new Error('Failed to fetch call stats'));\n        })\n      );\n  }\n\n  /**\n   * Gère un signal d'appel reçu\n   * @param signal Signal d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    switch (signal.type) {\n      case 'ice-candidate':\n        this.handleIceCandidate(signal);\n        break;\n      case 'answer':\n        this.handleAnswer(signal);\n        break;\n      case 'end-call':\n        this.handleEndCall(signal);\n        break;\n      case 'reject':\n        this.handleRejectCall(signal);\n        break;\n      default:\n        this.logger.debug(`Unhandled signal type: ${signal.type}`, signal);\n    }\n  }\n\n  /**\n   * Gère un candidat ICE reçu\n   * @param signal Signal d'appel contenant un candidat ICE\n   */\n  private handleIceCandidate(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for ICE candidate');\n      return;\n    }\n\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.peerConnection\n        .addIceCandidate(new RTCIceCandidate(candidate))\n        .catch((error) => {\n          this.logger.error('Error adding ICE candidate', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing ICE candidate', error as Error);\n    }\n  }\n\n  /**\n   * Gère une réponse SDP reçue\n   * @param signal Signal d'appel contenant une réponse SDP\n   */\n  private handleAnswer(signal: CallSignal): void {\n    if (!this.peerConnection) {\n      this.logger.error('No peer connection available for answer');\n      return;\n    }\n\n    try {\n      const answer = JSON.parse(signal.data);\n      this.peerConnection\n        .setRemoteDescription(new RTCSessionDescription(answer))\n        .catch((error) => {\n          this.logger.error('Error setting remote description', error as Error);\n        });\n    } catch (error) {\n      this.logger.error('Error parsing answer', error as Error);\n    }\n  }\n\n  /**\n   * Gère la fin d'un appel\n   * @param signal Signal d'appel indiquant la fin de l'appel\n   */\n  private handleEndCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.ENDED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Gère le rejet d'un appel\n   * @param signal Signal d'appel indiquant le rejet de l'appel\n   */\n  private handleRejectCall(signal: CallSignal): void {\n    this.stop('ringtone');\n    this.cleanupCall();\n\n    // Mettre à jour l'état de l'appel actif\n    const currentCall = this.activeCall.value;\n    if (currentCall && currentCall.id === signal.callId) {\n      this.activeCall.next({\n        ...currentCall,\n        status: CallStatus.REJECTED,\n        endTime: new Date().toISOString(),\n      });\n    }\n  }\n\n  /**\n   * Nettoie les ressources d'appel\n   */\n  private cleanupCall(): void {\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => track.stop());\n      this.localStream = null;\n      this.localStream$.next(null);\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n    }\n\n    this.remoteStream = null;\n    this.remoteStream$.next(null);\n  }\n\n  /**\n   * Configure les périphériques média pour un appel\n   * @param callType Type d'appel (audio, vidéo)\n   * @returns Observable avec le flux média\n   */\n  private setupMediaDevices(callType: CallType): Observable<MediaStream> {\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video:\n        callType !== CallType.AUDIO\n          ? {\n              width: { ideal: 1280 },\n              height: { ideal: 720 },\n            }\n          : false,\n    };\n\n    return new Observable<MediaStream>((observer) => {\n      navigator.mediaDevices\n        .getUserMedia(constraints)\n        .then((stream) => {\n          observer.next(stream);\n          observer.complete();\n        })\n        .catch((error) => {\n          this.logger.error('Error accessing media devices', error);\n          observer.error(new Error('Failed to access media devices'));\n        });\n    });\n  }\n\n  /**\n   * Génère un ID d'appel unique\n   * @returns ID d'appel unique\n   */\n  private generateCallId(): string {\n    return Date.now().toString() + Math.random().toString(36).substring(2, 9);\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Méthodes pour les Utilisateurs/Groupes\n  // --------------------------------------------------------------------------\n  // User methods\n  getAllUsers(\n    forceRefresh = false,\n    search?: string,\n    page: number = 1,\n    limit: number = 10,\n    sortBy: string = 'username',\n    sortOrder: string = 'asc',\n    isOnline?: boolean\n  ): Observable<User[]> {\n    this.logger.info(\n      'MessageService',\n      `Getting users with params: forceRefresh=${forceRefresh}, search=${\n        search || '(empty)'\n      }, page=${page}, limit=${limit}, sortBy=${sortBy}, sortOrder=${sortOrder}, isOnline=${isOnline}`\n    );\n\n    const now = Date.now();\n    const cacheValid =\n      !forceRefresh &&\n      this.usersCache.length > 0 &&\n      now - this.lastFetchTime <= this.CACHE_DURATION &&\n      !search &&\n      page === 1 &&\n      limit >= this.usersCache.length;\n\n    // Use cache only for first page with no filters\n    if (cacheValid) {\n      this.logger.debug(\n        'MessageService',\n        `Using cached users (${this.usersCache.length} users)`\n      );\n      return of([...this.usersCache]);\n    }\n\n    this.logger.debug(\n      'MessageService',\n      `Fetching users from server with pagination, fetchPolicy=${\n        forceRefresh ? 'network-only' : 'cache-first'\n      }`\n    );\n\n    return this.apollo\n      .watchQuery<any>({\n        query: GET_ALL_USER_QUERY,\n        variables: {\n          search,\n          page,\n          limit,\n          sortBy,\n          sortOrder,\n          isOnline: isOnline !== undefined ? isOnline : null,\n        },\n        fetchPolicy: forceRefresh ? 'network-only' : 'cache-first',\n      })\n      .valueChanges.pipe(\n        map((result) => {\n          this.logger.debug(\n            'MessageService',\n            'Users response received',\n            result\n          );\n\n          if (result.errors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors in getAllUsers:',\n              result.errors\n            );\n            throw new Error(result.errors.map((e) => e.message).join(', '));\n          }\n\n          if (!result.data?.getAllUsers) {\n            this.logger.warn(\n              'MessageService',\n              'No users data received from server'\n            );\n            return [];\n          }\n\n          const paginatedResponse = result.data.getAllUsers;\n\n          // Log pagination metadata\n          this.logger.debug('MessageService', 'Pagination metadata:', {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          });\n\n          // Normalize users with error handling\n          const users: User[] = [];\n          for (const user of paginatedResponse.users) {\n            try {\n              if (user) {\n                users.push(this.normalizeUser(user));\n              }\n            } catch (error) {\n              this.logger.warn(\n                'MessageService',\n                `Error normalizing user, skipping:`,\n                error\n              );\n            }\n          }\n\n          this.logger.info(\n            'MessageService',\n            `Received ${users.length} users from server (page ${paginatedResponse.currentPage} of ${paginatedResponse.totalPages})`\n          );\n\n          // Update cache only for first page with no filters\n          if (!search && page === 1 && !isOnline) {\n            this.usersCache = [...users];\n            this.lastFetchTime = Date.now();\n            this.logger.debug(\n              'MessageService',\n              `User cache updated with ${users.length} users`\n            );\n          }\n\n          // Store pagination metadata in a property for component access\n          this.currentUserPagination = {\n            totalCount: paginatedResponse.totalCount,\n            totalPages: paginatedResponse.totalPages,\n            currentPage: paginatedResponse.currentPage,\n            hasNextPage: paginatedResponse.hasNextPage,\n            hasPreviousPage: paginatedResponse.hasPreviousPage,\n          };\n\n          return users;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching users:', error);\n\n          if (error.graphQLErrors) {\n            this.logger.error(\n              'MessageService',\n              'GraphQL errors:',\n              error.graphQLErrors\n            );\n          }\n\n          if (error.networkError) {\n            this.logger.error(\n              'MessageService',\n              'Network error:',\n              error.networkError\n            );\n          }\n\n          // Return cache if available (only for first page)\n          if (\n            this.usersCache.length > 0 &&\n            page === 1 &&\n            !search &&\n            !isOnline\n          ) {\n            this.logger.warn(\n              'MessageService',\n              `Returning ${this.usersCache.length} cached users due to fetch error`\n            );\n            return of([...this.usersCache]);\n          }\n\n          return throwError(\n            () =>\n              new Error(\n                `Failed to fetch users: ${error.message || 'Unknown error'}`\n              )\n          );\n        })\n      );\n  }\n  getOneUser(userId: string): Observable<User> {\n    return this.apollo\n      .watchQuery<GetOneUserResponse>({\n        query: GET_USER_QUERY,\n        variables: { id: userId },\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getOneUser)),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error fetching user:', error);\n          return throwError(() => new Error('Failed to fetch user'));\n        })\n      );\n  }\n  getCurrentUser(): Observable<User> {\n    return this.apollo\n      .watchQuery<getCurrentUserResponse>({\n        query: GET_CURRENT_USER_QUERY,\n        fetchPolicy: 'network-only',\n      })\n      .valueChanges.pipe(\n        map((result) => this.normalizeUser(result.data?.getCurrentUser)),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error fetching current user:',\n            error\n          );\n          return throwError(() => new Error('Failed to fetch current user'));\n        })\n      );\n  }\n  setUserOnline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOnlineResponse>({\n        mutation: SET_USER_ONLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOnline)\n            throw new Error('Failed to set user online');\n          return this.normalizeUser(result.data.setUserOnline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user online:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user online'));\n        })\n      );\n  }\n  setUserOffline(userId: string): Observable<User> {\n    return this.apollo\n      .mutate<SetUserOfflineResponse>({\n        mutation: SET_USER_OFFLINE_MUTATION,\n        variables: { userId },\n      })\n      .pipe(\n        map((result) => {\n          if (!result.data?.setUserOffline)\n            throw new Error('Failed to set user offline');\n          return this.normalizeUser(result.data.setUserOffline);\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error setting user offline:',\n            error\n          );\n          return throwError(() => new Error('Failed to set user offline'));\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section: Gestion des Groupes\n  // --------------------------------------------------------------------------\n\n  /**\n   * Crée un nouveau groupe\n   */\n  createGroup(\n    name: string,\n    participantIds: string[],\n    photo?: File,\n    description?: string\n  ): Observable<any> {\n    this.logger.debug(\n      'MessageService',\n      `Creating group: ${name} with ${participantIds.length} participants`\n    );\n\n    if (!name || !participantIds || participantIds.length === 0) {\n      return throwError(\n        () => new Error('Nom du groupe et participants requis')\n      );\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: CREATE_GROUP_MUTATION,\n        variables: { name, participantIds, photo, description },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.createGroup;\n          if (!group) {\n            throw new Error('Échec de la création du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group created successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error creating group:', error);\n          return throwError(() => new Error('Échec de la création du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Met à jour un groupe existant\n   */\n  updateGroup(groupId: string, input: any): Observable<any> {\n    this.logger.debug('MessageService', `Updating group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: UPDATE_GROUP_MUTATION,\n        variables: { id: groupId, input },\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.updateGroup;\n          if (!group) {\n            throw new Error('Échec de la mise à jour du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group updated successfully: ${group.id}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error updating group:', error);\n          return throwError(\n            () => new Error('Échec de la mise à jour du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Supprime un groupe\n   */\n  deleteGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Deleting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: DELETE_GROUP_MUTATION,\n        variables: { id: groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.deleteGroup;\n          if (!response) {\n            throw new Error('Échec de la suppression du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group deleted successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error deleting group:', error);\n          return throwError(\n            () => new Error('Échec de la suppression du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Quitte un groupe\n   */\n  leaveGroup(\n    groupId: string\n  ): Observable<{ success: boolean; message: string }> {\n    this.logger.debug('MessageService', `Leaving group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .mutate({\n        mutation: LEAVE_GROUP_MUTATION,\n        variables: { groupId },\n      })\n      .pipe(\n        map((result: any) => {\n          const response = result.data?.leaveGroup;\n          if (!response) {\n            throw new Error('Échec de la sortie du groupe');\n          }\n          this.logger.info(\n            'MessageService',\n            `Left group successfully: ${groupId}`\n          );\n          return response;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error leaving group:', error);\n          return throwError(() => new Error('Échec de la sortie du groupe'));\n        })\n      );\n  }\n\n  /**\n   * Récupère les informations d'un groupe\n   */\n  getGroup(groupId: string): Observable<any> {\n    this.logger.debug('MessageService', `Getting group: ${groupId}`);\n\n    if (!groupId) {\n      return throwError(() => new Error('ID du groupe requis'));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_GROUP_QUERY,\n        variables: { id: groupId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const group = result.data?.getGroup;\n          if (!group) {\n            throw new Error('Groupe non trouvé');\n          }\n          this.logger.info(\n            'MessageService',\n            `Group retrieved successfully: ${groupId}`\n          );\n          return group;\n        }),\n        catchError((error) => {\n          this.logger.error('MessageService', 'Error getting group:', error);\n          return throwError(\n            () => new Error('Échec de la récupération du groupe')\n          );\n        })\n      );\n  }\n\n  /**\n   * Récupère les groupes d'un utilisateur\n   */\n  getUserGroups(userId: string): Observable<any[]> {\n    this.logger.debug('MessageService', `Getting groups for user: ${userId}`);\n\n    if (!userId) {\n      return throwError(() => new Error(\"ID de l'utilisateur requis\"));\n    }\n\n    return this.apollo\n      .query({\n        query: GET_USER_GROUPS_QUERY,\n        variables: { userId },\n        fetchPolicy: 'network-only',\n      })\n      .pipe(\n        map((result: any) => {\n          const groups = result.data?.getUserGroups || [];\n          this.logger.info(\n            'MessageService',\n            `Retrieved ${groups.length} groups for user: ${userId}`\n          );\n          return groups;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error getting user groups:',\n            error\n          );\n          return throwError(\n            () => new Error('Échec de la récupération des groupes')\n          );\n        })\n      );\n  }\n\n  // --------------------------------------------------------------------------\n  // Section 4: Subscriptions et Gestion Temps Réel\n  // --------------------------------------------------------------------------\n  subscribeToNewMessages(conversationId: string): Observable<Message> {\n    console.log(\n      `🔍 DEBUG: subscribeToNewMessages called with conversationId: ${conversationId}`\n    );\n\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    const tokenValid = this.isTokenValid();\n    console.log(`🔍 DEBUG: Token validation result: ${tokenValid}`);\n\n    if (!tokenValid) {\n      console.warn(\n        '❌ DEBUG: Token invalid - subscription will not be established'\n      );\n      this.logger.warn(\n        \"Tentative d'abonnement aux messages avec un token invalide ou expiré\"\n      );\n      return of(null as unknown as Message);\n    }\n\n    console.log(\n      `✅ DEBUG: Token valid - proceeding with subscription setup for conversation: ${conversationId}`\n    );\n    this.logger.debug(\n      `🚀 INSTANT MESSAGE: Setting up real-time subscription for conversation: ${conversationId}`\n    );\n\n    console.log(`🔍 DEBUG: Creating Apollo subscription with variables:`, {\n      conversationId,\n    });\n    console.log(\n      `🔍 DEBUG: MESSAGE_SENT_SUBSCRIPTION query:`,\n      MESSAGE_SENT_SUBSCRIPTION\n    );\n    console.log(`🔍 DEBUG: Subscription variables:`, { conversationId });\n\n    const sub$ = this.apollo\n      .subscribe<{ messageSent: Message }>({\n        query: MESSAGE_SENT_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        tap((result) => {\n          console.log(`🔍 DEBUG: Raw subscription result received:`, result);\n          console.log(`🔍 DEBUG: result.data:`, result.data);\n          console.log(\n            `🔍 DEBUG: result.data?.messageSent:`,\n            result.data?.messageSent\n          );\n        }),\n        map((result) => {\n          const msg = result.data?.messageSent;\n          if (!msg) {\n            console.log(\n              `❌ DEBUG: No message payload received in result:`,\n              result\n            );\n            this.logger.warn('⚠️ No message payload received');\n            throw new Error('No message payload received');\n          }\n\n          this.logger.debug(\n            '⚡ INSTANT: New message received via WebSocket',\n            msg\n          );\n\n          // Vérifier que l'ID est présent\n          if (!msg.id && !msg._id) {\n            this.logger.warn(\n              '⚠️ Message without ID received, generating temp ID'\n            );\n            msg.id = `temp-${Date.now()}`;\n          }\n\n          try {\n            // NORMALISATION RAPIDE du message\n            const normalizedMessage = this.normalizeMessage(msg);\n\n            this.logger.debug(\n              '✅ INSTANT: Message normalized successfully',\n              normalizedMessage\n            );\n\n            // TRAITEMENT INSTANTANÉ selon le type\n            if (\n              normalizedMessage.type === MessageType.AUDIO ||\n              normalizedMessage.type === MessageType.VOICE_MESSAGE ||\n              (normalizedMessage.attachments &&\n                normalizedMessage.attachments.some(\n                  (att) => att.type === 'AUDIO'\n                ))\n            ) {\n              this.logger.debug(\n                '🎤 INSTANT: Voice message received in real-time'\n              );\n            }\n\n            // MISE À JOUR IMMÉDIATE de l'UI\n            this.zone.run(() => {\n              this.logger.debug(\n                '📡 INSTANT: Updating conversation UI immediately'\n              );\n              this.updateConversationWithNewMessage(\n                conversationId,\n                normalizedMessage\n              );\n            });\n\n            return normalizedMessage;\n          } catch (err) {\n            this.logger.error('❌ Error normalizing message:', err);\n\n            // Créer un message minimal mais valide pour éviter les erreurs\n            const minimalMessage: Message = {\n              id: msg.id || msg._id || `temp-${Date.now()}`,\n              content: msg.content || '',\n              type: msg.type || MessageType.TEXT,\n              timestamp: this.safeDate(msg.timestamp),\n              isRead: false,\n              sender: msg.sender\n                ? this.normalizeUser(msg.sender)\n                : {\n                    id: this.getCurrentUserId(),\n                    username: 'Unknown',\n                  },\n            };\n\n            this.logger.debug(\n              '🔧 FALLBACK: Created minimal message',\n              minimalMessage\n            );\n            return minimalMessage;\n          }\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Message subscription error:',\n            error\n          );\n          // Retourner un observable vide au lieu de null\n          return EMPTY;\n        }),\n        // Filtrer les valeurs null\n        filter((message) => !!message),\n        // Réessayer après un délai en cas d'erreur\n        retry(3)\n      );\n\n    console.log(`🔍 DEBUG: Setting up subscription observer...`);\n\n    const sub = sub$.subscribe({\n      next: (message) => {\n        console.log(`✅ DEBUG: Message received via subscription:`, message);\n        // Traitement supplémentaire pour s'assurer que le message est bien affiché\n        this.logger.debug('MessageService', 'New message received:', message);\n\n        // Mettre à jour la conversation avec le nouveau message\n        this.updateConversationWithNewMessage(conversationId, message);\n      },\n      error: (err) => {\n        console.error(`❌ DEBUG: Subscription error:`, err);\n        this.logger.error('Error in message subscription:', err);\n      },\n      complete: () => {\n        console.log(`🔚 DEBUG: Subscription completed`);\n      },\n    });\n\n    // Log pour confirmer que la subscription est créée\n    console.log(`🔗 DEBUG: Subscription object created:`, sub);\n    console.log(`🔗 DEBUG: Apollo client state:`, this.apollo);\n\n    this.subscriptions.push(sub);\n    console.log(\n      `✅ DEBUG: Subscription established and added to subscriptions list. Total subscriptions: ${this.subscriptions.length}`\n    );\n    return sub$;\n  }\n\n  /**\n   * Met à jour une conversation avec un nouveau message INSTANTANÉMENT\n   * @param conversationId ID de la conversation\n   * @param message Nouveau message\n   */\n  private updateConversationWithNewMessage(\n    conversationId: string,\n    message: Message\n  ): void {\n    this.logger.debug(\n      `⚡ INSTANT: Updating conversation ${conversationId} with new message ${message.id}`\n    );\n\n    // MISE À JOUR IMMÉDIATE sans attendre la requête\n    this.zone.run(() => {\n      // Émettre IMMÉDIATEMENT l'événement de conversation active\n      this.activeConversation.next(conversationId);\n\n      this.logger.debug('📡 INSTANT: Conversation event emitted immediately');\n    });\n\n    // Mise à jour en arrière-plan (non-bloquante)\n    setTimeout(() => {\n      this.getConversation(conversationId).subscribe({\n        next: (conversation) => {\n          this.logger.debug(\n            `✅ BACKGROUND: Conversation ${conversationId} refreshed with ${\n              conversation?.messages?.length || 0\n            } messages`\n          );\n        },\n        error: (error) => {\n          this.logger.error(\n            `⚠️ BACKGROUND: Error refreshing conversation ${conversationId}:`,\n            error\n          );\n        },\n      });\n    }, 0); // Exécution asynchrone immédiate\n  }\n\n  /**\n   * Rafraîchit les notifications du sender après envoi d'un message\n   */\n  private refreshSenderNotifications(): void {\n    console.log('🔄 SENDER: Refreshing notifications after message sent');\n\n    // Recharger les notifications en arrière-plan\n    this.getNotifications(true).subscribe({\n      next: (notifications) => {\n        console.log(\n          '🔄 SENDER: Notifications refreshed successfully',\n          notifications.length\n        );\n      },\n      error: (error) => {\n        console.error('🔄 SENDER: Error refreshing notifications:', error);\n      },\n    });\n  }\n\n  subscribeToUserStatus(): Observable<User> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement au statut utilisateur avec un token invalide ou expiré\"\n      );\n      return throwError(() => new Error('Invalid or expired token'));\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement au statut utilisateur\");\n\n    const sub$ = this.apollo\n      .subscribe<{ userStatusChanged: User }>({\n        query: USER_STATUS_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement au statut utilisateur:\",\n            result\n          )\n        ),\n        map((result) => {\n          const user = result.data?.userStatusChanged;\n          if (!user) {\n            this.logger.error('No status payload received');\n            throw new Error('No status payload received');\n          }\n          return this.normalizeUser(user);\n        }),\n        catchError((error) => {\n          this.logger.error('Status subscription error:', error as Error);\n          return throwError(() => new Error('Status subscription failed'));\n        }),\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToConversationUpdates(\n    conversationId: string\n  ): Observable<Conversation> {\n    const sub$ = this.apollo\n      .subscribe<{ conversationUpdated: Conversation }>({\n        query: CONVERSATION_UPDATED_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => {\n          const conv = result.data?.conversationUpdated;\n          if (!conv) throw new Error('No conversation payload received');\n\n          const normalizedConversation: Conversation = {\n            ...conv,\n            participants:\n              conv.participants?.map((p) => this.normalizeUser(p)) || [],\n            lastMessage: conv.lastMessage\n              ? {\n                  ...conv.lastMessage,\n                  sender: this.normalizeUser(conv.lastMessage.sender),\n                  timestamp: this.safeDate(conv.lastMessage.timestamp),\n                  readAt: conv.lastMessage.readAt\n                    ? this.safeDate(conv.lastMessage.readAt)\n                    : undefined,\n                  // Conservez toutes les autres propriétés du message\n                  id: conv.lastMessage.id,\n                  content: conv.lastMessage.content,\n                  type: conv.lastMessage.type,\n                  isRead: conv.lastMessage.isRead,\n                  // ... autres propriétés nécessaires\n                }\n              : null, // On conserve null comme dans votre version originale\n          };\n\n          return normalizedConversation as Conversation; // Assertion de type si nécessaire\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Conversation subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Conversation subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToTypingIndicator(\n    conversationId: string\n  ): Observable<TypingIndicatorEvent> {\n    const sub$ = this.apollo\n      .subscribe<TypingIndicatorEvents>({\n        query: TYPING_INDICATOR_SUBSCRIPTION,\n        variables: { conversationId },\n      })\n      .pipe(\n        map((result) => result.data?.typingIndicator),\n        filter(Boolean),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Typing indicator subscription error:',\n            error\n          );\n          return throwError(\n            () => new Error('Typing indicator subscription failed')\n          );\n        })\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  private isTokenValid(): boolean {\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn('Aucun token trouvé');\n      return false;\n    }\n\n    try {\n      // Décoder le token JWT (format: header.payload.signature)\n      const parts = token.split('.');\n      if (parts.length !== 3) {\n        this.logger.warn('Format de token invalide');\n        return false;\n      }\n\n      // Décoder le payload (deuxième partie du token)\n      const payload = JSON.parse(atob(parts[1]));\n\n      // Vérifier l'expiration\n      if (!payload.exp) {\n        this.logger.warn(\"Token sans date d'expiration\");\n        return false;\n      }\n\n      const expirationDate = new Date(payload.exp * 1000);\n      const now = new Date();\n\n      if (expirationDate < now) {\n        this.logger.warn('Token expiré', {\n          expiration: expirationDate.toISOString(),\n          now: now.toISOString(),\n        });\n        return false;\n      }\n\n      return true;\n    } catch (error) {\n      this.logger.error(\n        'Erreur lors de la vérification du token:',\n        error as Error\n      );\n      return false;\n    }\n  }\n\n  subscribeToNotificationsRead(): Observable<string[]> {\n    // Vérifier si l'utilisateur est connecté avec un token valide\n    if (!this.isTokenValid()) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications avec un token invalide ou expiré\"\n      );\n      return of([]);\n    }\n\n    this.logger.debug(\"Démarrage de l'abonnement aux notifications lues\");\n\n    const sub$ = this.apollo\n      .subscribe<NotificationsReadEvent>({\n        query: NOTIFICATIONS_READ_SUBSCRIPTION,\n      })\n      .pipe(\n        tap((result) =>\n          this.logger.debug(\n            \"Données reçues de l'abonnement aux notifications lues:\",\n            result\n          )\n        ),\n        map((result) => {\n          const notificationIds = result.data?.notificationsRead || [];\n          this.logger.debug(\n            'Notifications marquées comme lues:',\n            notificationIds\n          );\n          this.updateNotificationStatus(notificationIds, true);\n          return notificationIds;\n        }),\n        catchError((err) => {\n          this.logger.error(\n            'Notifications read subscription error:',\n            err as Error\n          );\n          // Retourner un tableau vide au lieu de propager l'erreur\n          return of([]);\n        }),\n        // Réessayer après un délai en cas d'erreur\n        retry(3) // Réessayer 3 fois en cas d'erreur\n      );\n\n    const sub = sub$.subscribe();\n    this.subscriptions.push(sub);\n    return sub$;\n  }\n  subscribeToNewNotifications(): Observable<Notification> {\n    // Vérifier si l'utilisateur est connecté\n    const token = localStorage.getItem('token');\n    if (!token) {\n      this.logger.warn(\n        \"Tentative d'abonnement aux notifications sans être connecté\"\n      );\n      return EMPTY;\n    }\n\n    this.logger.debug(\n      '🚀 INSTANT NOTIFICATION: Setting up real-time subscription'\n    );\n\n    const source$ = this.apollo.subscribe<NotificationReceivedEvent>({\n      query: NOTIFICATION_SUBSCRIPTION,\n    });\n\n    const processed$ = source$.pipe(\n      map((result) => {\n        const notification = result.data?.notificationReceived;\n        if (!notification) {\n          throw new Error('No notification payload received');\n        }\n\n        this.logger.debug(\n          '⚡ INSTANT: New notification received',\n          notification\n        );\n\n        const normalized = this.normalizeNotification(notification);\n\n        // Vérification rapide du cache\n        if (this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            `🔄 Notification ${normalized.id} already in cache, skipping`\n          );\n          throw new Error('Notification already exists in cache');\n        }\n\n        // TRAITEMENT INSTANTANÉ\n        this.logger.debug('📡 INSTANT: Processing notification immediately');\n\n        // Vérifier si la notification existe déjà pour éviter les doublons\n        const currentNotifications = this.notifications.value;\n        const existingNotification = currentNotifications.find(\n          (n) => n.id === normalized.id\n        );\n\n        if (existingNotification) {\n          this.logger.debug(\n            '🔄 DUPLICATE: Notification already exists, skipping:',\n            normalized.id\n          );\n          return normalized;\n        }\n\n        // Son de notification IMMÉDIAT\n        this.playNotificationSound();\n\n        // Mise à jour INSTANTANÉE du cache\n        this.updateNotificationCache(normalized);\n\n        // Émettre IMMÉDIATEMENT la notification EN PREMIER\n        this.zone.run(() => {\n          // 🚀 INSERTION EN PREMIER: Nouvelle notification en tête de liste\n          const updatedNotifications = [normalized, ...currentNotifications];\n\n          this.logger.debug(\n            `⚡ INSTANT: Nouvelle notification ajoutée en PREMIER (${updatedNotifications.length} total)`\n          );\n\n          this.notifications.next(updatedNotifications);\n          this.notificationCount.next(this.notificationCount.value + 1);\n        });\n\n        this.logger.debug(\n          '✅ INSTANT: Notification processed and emitted',\n          normalized\n        );\n\n        return normalized;\n      }),\n      // Gestion d'erreurs optimisée\n      catchError((err) => {\n        if (\n          err instanceof Error &&\n          err.message === 'Notification already exists in cache'\n        ) {\n          return EMPTY;\n        }\n\n        this.logger.error('❌ Notification subscription error:', err as Error);\n        return EMPTY;\n      }),\n      // Optimisation: traitement en temps réel\n      tap((notification) => {\n        this.logger.debug(\n          '⚡ INSTANT: Notification ready for UI update',\n          notification\n        );\n      })\n    );\n\n    const sub = processed$.subscribe({\n      next: (notification) => {\n        this.logger.debug(\n          '✅ INSTANT: Notification delivered to UI',\n          notification\n        );\n      },\n      error: (error) => {\n        this.logger.error(\n          '❌ CRITICAL: Notification subscription error',\n          error\n        );\n      },\n    });\n\n    this.subscriptions.push(sub);\n    this.logger.debug('🔗 INSTANT: Notification subscription established');\n    return processed$;\n  }\n  // --------------------------------------------------------------------------\n  // Helpers et Utilitaires\n  // --------------------------------------------------------------------------\n\n  private startCleanupInterval(): void {\n    this.cleanupInterval = setInterval(() => {\n      this.cleanupExpiredNotifications();\n    }, 3600000);\n  }\n  private cleanupExpiredNotifications(): void {\n    const now = new Date();\n    const thirtyDaysAgo = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);\n\n    let expiredCount = 0;\n\n    this.notificationCache.forEach((notification, id) => {\n      const notificationDate = new Date(notification.timestamp);\n      if (notificationDate < thirtyDaysAgo) {\n        this.notificationCache.delete(id);\n        expiredCount++;\n      }\n    });\n\n    if (expiredCount > 0) {\n      this.logger.debug(`Cleaned up ${expiredCount} expired notifications`);\n\n      // 🚀 TRI OPTIMISÉ: Maintenir l'ordre après nettoyage\n      const remainingNotifications = Array.from(\n        this.notificationCache.values()\n      );\n      const sortedNotifications = this.sortNotificationsByDate(\n        remainingNotifications\n      );\n\n      this.notifications.next(sortedNotifications);\n      this.updateUnreadCount();\n    }\n  }\n  /**\n   * Trie les notifications par date (plus récentes en premier)\n   * @param notifications Array de notifications à trier\n   * @returns Array de notifications triées\n   */\n  private sortNotificationsByDate(\n    notifications: Notification[]\n  ): Notification[] {\n    return notifications.sort((a, b) => {\n      // Utiliser timestamp ou une date par défaut si manquant\n      const dateA = new Date(a.timestamp || 0);\n      const dateB = new Date(b.timestamp || 0);\n      return dateB.getTime() - dateA.getTime(); // Ordre décroissant (plus récent en premier)\n    });\n  }\n\n  private getCurrentUserId(): string {\n    return localStorage.getItem('userId') || '';\n  }\n  private normalizeMessage(message: Message): Message {\n    if (!message) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined message'\n      );\n      throw new Error('Message object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!message.id && !message._id) {\n        this.logger.error(\n          '[MessageService] Message ID is missing',\n          undefined,\n          message\n        );\n        throw new Error('Message ID is required');\n      }\n\n      // Normaliser le sender avec gestion d'erreur\n      let normalizedSender;\n      try {\n        normalizedSender = message.sender\n          ? this.normalizeUser(message.sender)\n          : undefined;\n      } catch (error) {\n        this.logger.warn(\n          '[MessageService] Error normalizing message sender, using default values',\n          error\n        );\n        normalizedSender = {\n          _id: message.senderId || 'unknown',\n          id: message.senderId || 'unknown',\n          username: 'Unknown User',\n          email: '<EMAIL>',\n          role: 'user',\n          isActive: true,\n        };\n      }\n\n      // Normaliser le receiver si présent\n      let normalizedReceiver;\n      if (message.receiver) {\n        try {\n          normalizedReceiver = this.normalizeUser(message.receiver);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing message receiver, using default values',\n            error\n          );\n          normalizedReceiver = {\n            _id: message.receiverId || 'unknown',\n            id: message.receiverId || 'unknown',\n            username: 'Unknown User',\n            email: '<EMAIL>',\n            role: 'user',\n            isActive: true,\n          };\n        }\n      }\n\n      // Normaliser les pièces jointes si présentes\n      const normalizedAttachments =\n        message.attachments?.map((att) => ({\n          id: att.id || att._id || `attachment-${Date.now()}`,\n          url: att.url || '',\n          type: att.type || 'unknown',\n          name: att.name || 'attachment',\n          size: att.size || 0,\n          duration: att.duration || 0,\n        })) || [];\n\n      // Construire le message normalisé\n      const normalizedMessage = {\n        ...message,\n        _id: message.id || message._id,\n        id: message.id || message._id,\n        content: message.content || '',\n        sender: normalizedSender,\n        timestamp: this.normalizeDate(message.timestamp),\n        readAt: message.readAt ? this.normalizeDate(message.readAt) : undefined,\n        attachments: normalizedAttachments,\n        metadata: message.metadata || null,\n      };\n\n      // Ajouter le receiver seulement s'il existe\n      if (normalizedReceiver) {\n        normalizedMessage.receiver = normalizedReceiver;\n      }\n\n      this.logger.debug('[MessageService] Message normalized successfully', {\n        messageId: normalizedMessage.id,\n        senderId: normalizedMessage.sender?.id,\n      });\n\n      return normalizedMessage;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing message:',\n        error instanceof Error ? error : new Error(String(error)),\n        message\n      );\n      throw new Error(\n        `Failed to normalize message: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n\n  public normalizeUser(user: any): User {\n    if (!user) {\n      throw new Error('User object is required');\n    }\n\n    // Vérification des champs obligatoires avec valeurs par défaut\n    const userId = user.id || user._id;\n    if (!userId) {\n      throw new Error('User ID is required');\n    }\n\n    // Utiliser des valeurs par défaut pour les champs manquants\n    const username = user.username || 'Unknown User';\n    const email = user.email || `user-${userId}@example.com`;\n    const isActive =\n      user.isActive !== undefined && user.isActive !== null\n        ? user.isActive\n        : true;\n    const role = user.role || 'user';\n\n    // Construire l'objet utilisateur normalisé\n    return {\n      _id: userId,\n      id: userId,\n      username: username,\n      email: email,\n      role: role,\n      isActive: isActive,\n      // Champs optionnels\n      image: user.image ?? null,\n      bio: user.bio,\n      isOnline: user.isOnline || false,\n      lastActive: user.lastActive ? new Date(user.lastActive) : undefined,\n      createdAt: user.createdAt ? new Date(user.createdAt) : undefined,\n      updatedAt: user.updatedAt ? new Date(user.updatedAt) : undefined,\n      followingCount: user.followingCount,\n      followersCount: user.followersCount,\n      postCount: user.postCount,\n    };\n  }\n  private normalizeConversation(conv: Conversation): Conversation {\n    if (!conv) {\n      this.logger.error(\n        '[MessageService] Cannot normalize null or undefined conversation'\n      );\n      throw new Error('Conversation object is required');\n    }\n\n    try {\n      // Vérification des champs obligatoires\n      if (!conv.id && !conv._id) {\n        this.logger.error(\n          '[MessageService] Conversation ID is missing',\n          undefined,\n          conv\n        );\n        throw new Error('Conversation ID is required');\n      }\n\n      // Normaliser les participants avec gestion d'erreur\n      const normalizedParticipants = [];\n      if (conv.participants && Array.isArray(conv.participants)) {\n        for (const participant of conv.participants) {\n          try {\n            if (participant) {\n              normalizedParticipants.push(this.normalizeUser(participant));\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing participant, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.warn(\n          '[MessageService] Conversation has no participants or invalid participants array',\n          conv\n        );\n      }\n\n      // Normaliser les messages avec gestion d'erreur\n      const normalizedMessages = [];\n      if (conv.messages && Array.isArray(conv.messages)) {\n        this.logger.debug('[MessageService] Processing conversation messages', {\n          count: conv.messages.length,\n        });\n\n        for (const message of conv.messages) {\n          try {\n            if (message) {\n              const normalizedMessage = this.normalizeMessage(message);\n              this.logger.debug(\n                '[MessageService] Successfully normalized message',\n                {\n                  messageId: normalizedMessage.id,\n                  content: normalizedMessage.content?.substring(0, 20),\n                  sender: normalizedMessage.sender?.username,\n                }\n              );\n              normalizedMessages.push(normalizedMessage);\n            }\n          } catch (error) {\n            this.logger.warn(\n              '[MessageService] Error normalizing message in conversation, skipping',\n              error\n            );\n          }\n        }\n      } else {\n        this.logger.debug(\n          '[MessageService] No messages found in conversation or invalid messages array'\n        );\n      }\n\n      // Normaliser le dernier message avec gestion d'erreur\n      let normalizedLastMessage = null;\n      if (conv.lastMessage) {\n        try {\n          normalizedLastMessage = this.normalizeMessage(conv.lastMessage);\n        } catch (error) {\n          this.logger.warn(\n            '[MessageService] Error normalizing last message, using null',\n            error\n          );\n        }\n      }\n\n      // Construire la conversation normalisée\n      const normalizedConversation = {\n        ...conv,\n        _id: conv.id || conv._id,\n        id: conv.id || conv._id,\n        participants: normalizedParticipants,\n        messages: normalizedMessages,\n        lastMessage: normalizedLastMessage,\n        unreadCount: conv.unreadCount || 0,\n        isGroup: !!conv.isGroup,\n        createdAt: this.normalizeDate(conv.createdAt),\n        updatedAt: this.normalizeDate(conv.updatedAt),\n      };\n\n      this.logger.debug(\n        '[MessageService] Conversation normalized successfully',\n        {\n          conversationId: normalizedConversation.id,\n          participantCount: normalizedParticipants.length,\n          messageCount: normalizedMessages.length,\n        }\n      );\n\n      return normalizedConversation;\n    } catch (error) {\n      this.logger.error(\n        '[MessageService] Error normalizing conversation:',\n        error instanceof Error ? error : new Error(String(error)),\n        conv\n      );\n      throw new Error(\n        `Failed to normalize conversation: ${\n          error instanceof Error ? error.message : String(error)\n        }`\n      );\n    }\n  }\n  private normalizeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to parse date: ${date}`, error);\n      return new Date();\n    }\n  }\n\n  // Méthode sécurisée pour créer une date à partir d'une valeur potentiellement undefined\n  private safeDate(date: string | Date | undefined): Date {\n    if (!date) return new Date();\n    try {\n      return typeof date === 'string' ? new Date(date) : date;\n    } catch (error) {\n      this.logger.warn(`Failed to create safe date: ${date}`, error);\n      return new Date();\n    }\n  }\n  private toSafeISOString = (\n    date: Date | string | undefined\n  ): string | undefined => {\n    if (!date) return undefined;\n    return typeof date === 'string' ? date : date.toISOString();\n  };\n  private normalizeNotification(notification: Notification): Notification {\n    this.logger.debug(\n      'MessageService',\n      'Normalizing notification',\n      notification\n    );\n\n    if (!notification) {\n      this.logger.error('MessageService', 'Notification is null or undefined');\n      throw new Error('Notification is required');\n    }\n\n    // Vérifier et normaliser l'ID\n    const notificationId = notification.id || (notification as any)._id;\n    if (!notificationId) {\n      this.logger.error(\n        'MessageService',\n        'Notification ID is missing',\n        notification\n      );\n      throw new Error('Notification ID is required');\n    }\n\n    if (!notification.timestamp) {\n      this.logger.warn(\n        'MessageService',\n        'Notification timestamp is missing, using current time',\n        notification\n      );\n      notification.timestamp = new Date();\n    }\n\n    try {\n      const normalized = {\n        ...notification,\n        _id: notificationId, // Conserver l'ID MongoDB original\n        id: notificationId, // Utiliser le même ID pour les deux propriétés\n        timestamp: new Date(notification.timestamp),\n        ...(notification.senderId && {\n          senderId: this.normalizeSender(notification.senderId),\n        }),\n        ...(notification.message && {\n          message: this.normalizeNotMessage(notification.message),\n        }),\n      };\n\n      this.logger.debug(\n        'MessageService',\n        'Normalized notification result',\n        normalized\n      );\n      return normalized;\n    } catch (error) {\n      this.logger.error(\n        'MessageService',\n        'Error in normalizeNotification',\n        error\n      );\n      throw error;\n    }\n  }\n  private normalizeSender(sender: any) {\n    return {\n      id: sender.id,\n      username: sender.username,\n      ...(sender.image && { image: sender.image }),\n    };\n  }\n\n  /**\n   * Normalise un message de notification\n   * @param message Message à normaliser\n   * @returns Message normalisé\n   */\n  private normalizeNotMessage(message: any) {\n    if (!message) return null;\n\n    return {\n      id: message.id || message._id,\n      content: message.content || '',\n      type: message.type || 'TEXT',\n      timestamp: this.safeDate(message.timestamp),\n      attachments: message.attachments || [],\n      ...(message.sender && { sender: this.normalizeSender(message.sender) }),\n    };\n  }\n  /**\n   * Met à jour le cache de notifications avec une ou plusieurs notifications\n   * @param notifications Notification(s) à ajouter au cache\n   * @param skipDuplicates Si true, ignore les notifications déjà présentes dans le cache\n   */\n  private updateCache(\n    notifications: Notification | Notification[],\n    skipDuplicates: boolean = true\n  ) {\n    const notificationArray = Array.isArray(notifications)\n      ? notifications\n      : [notifications];\n\n    this.logger.debug(\n      'MessageService',\n      `Updating notification cache with ${notificationArray.length} notifications`\n    );\n\n    if (notificationArray.length === 0) {\n      this.logger.warn('MessageService', 'No notifications to update in cache');\n      return;\n    }\n\n    // Vérifier si les notifications ont des IDs valides\n    const validNotifications = notificationArray.filter(\n      (notif) => notif && (notif.id || (notif as any)._id)\n    );\n\n    if (validNotifications.length !== notificationArray.length) {\n      this.logger.warn(\n        'MessageService',\n        `Found ${\n          notificationArray.length - validNotifications.length\n        } notifications without valid IDs`\n      );\n    }\n\n    let addedCount = 0;\n    let skippedCount = 0;\n\n    // Traiter chaque notification\n    validNotifications.forEach((notif, index) => {\n      try {\n        // S'assurer que la notification a un ID\n        const notifId = notif.id || (notif as any)._id;\n        if (!notifId) {\n          this.logger.error(\n            'MessageService',\n            'Notification without ID:',\n            notif\n          );\n          return;\n        }\n\n        // Normaliser la notification\n        const normalized = this.normalizeNotification(notif);\n\n        // Vérifier si cette notification existe déjà dans le cache\n        if (skipDuplicates && this.notificationCache.has(normalized.id)) {\n          this.logger.debug(\n            'MessageService',\n            `Notification ${normalized.id} already exists in cache, skipping`\n          );\n          skippedCount++;\n          return;\n        }\n\n        // Ajouter au cache\n        this.notificationCache.set(normalized.id, normalized);\n        addedCount++;\n\n        this.logger.debug(\n          'MessageService',\n          `Added notification ${normalized.id} to cache`\n        );\n      } catch (error) {\n        this.logger.error(\n          'MessageService',\n          `Error processing notification ${index + 1}:`,\n          error\n        );\n      }\n    });\n\n    this.logger.debug(\n      'MessageService',\n      `Cache update complete: ${addedCount} added, ${skippedCount} skipped, total: ${this.notificationCache.size}`\n    );\n\n    // Mettre à jour les observables et sauvegarder\n    this.refreshNotificationObservables();\n  }\n  /**\n   * Met à jour les observables de notifications et sauvegarde dans le localStorage\n   * OPTIMISÉ: Trie les notifications par date (plus récentes en premier)\n   */\n  private refreshNotificationObservables(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n\n    // 🚀 TRI OPTIMISÉ: Les notifications les plus récentes en premier\n    const sortedNotifications = this.sortNotificationsByDate(allNotifications);\n\n    this.logger.debug(\n      `📊 SORTED: ${sortedNotifications.length} notifications triées par date (plus récentes en premier)`\n    );\n\n    this.notifications.next(sortedNotifications);\n    this.updateUnreadCount();\n    this.saveNotificationsToLocalStorage();\n  }\n\n  /**\n   * Met à jour le compteur de notifications non lues\n   */\n  private updateUnreadCount(): void {\n    const allNotifications = Array.from(this.notificationCache.values());\n    const unreadNotifications = allNotifications.filter((n) => !n.isRead);\n    const count = unreadNotifications.length;\n\n    // Forcer la mise à jour dans la zone Angular\n    this.zone.run(() => {\n      this.notificationCount.next(count);\n\n      // Émettre un événement global pour forcer la mise à jour du layout\n      window.dispatchEvent(\n        new CustomEvent('notificationCountChanged', {\n          detail: { count },\n        })\n      );\n    });\n  }\n\n  /**\n   * Met à jour le cache avec une seule notification (méthode simplifiée)\n   * @param notification Notification à ajouter\n   */\n  private updateNotificationCache(notification: Notification): void {\n    this.updateCache(notification, true);\n  }\n  /**\n   * Met à jour le statut de lecture des notifications\n   * @param ids IDs des notifications à mettre à jour\n   * @param isRead Nouveau statut de lecture\n   */\n  private updateNotificationStatus(ids: string[], isRead: boolean): void {\n    ids.forEach((id) => {\n      const notif = this.notificationCache.get(id);\n      if (notif) {\n        this.notificationCache.set(id, {\n          ...notif,\n          isRead,\n          readAt: isRead ? new Date().toISOString() : undefined,\n        });\n      }\n    });\n    this.refreshNotificationObservables();\n  }\n\n  /**\n   * Méthode générique pour supprimer des notifications du cache local\n   * @param notificationIds IDs des notifications à supprimer\n   * @returns Nombre de notifications supprimées\n   */\n  private removeNotificationsFromCache(notificationIds: string[]): number {\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Starting removal of',\n      notificationIds.length,\n      'notifications'\n    );\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size before:',\n      this.notificationCache.size\n    );\n\n    let removedCount = 0;\n    notificationIds.forEach((id) => {\n      if (this.notificationCache.has(id)) {\n        console.log('🗑️ REMOVE FROM CACHE: Removing notification:', id);\n        this.notificationCache.delete(id);\n        removedCount++;\n      } else {\n        console.log(\n          '🗑️ REMOVE FROM CACHE: Notification not found in cache:',\n          id\n        );\n      }\n    });\n\n    console.log('🗑️ REMOVE FROM CACHE: Removed', removedCount, 'notifications');\n    console.log(\n      '🗑️ REMOVE FROM CACHE: Cache size after:',\n      this.notificationCache.size\n    );\n\n    if (removedCount > 0) {\n      console.log('🗑️ REMOVE FROM CACHE: Refreshing observables...');\n      this.refreshNotificationObservables();\n    }\n\n    return removedCount;\n  }\n\n  /**\n   * Méthode générique pour gérer les erreurs de suppression\n   * @param error Erreur survenue\n   * @param operation Nom de l'opération\n   * @param fallbackResponse Réponse de fallback en cas d'erreur\n   */\n  private handleDeletionError(\n    error: any,\n    operation: string,\n    fallbackResponse: any\n  ) {\n    this.logger.error('MessageService', `Erreur lors de ${operation}:`, error);\n    return of(fallbackResponse);\n  }\n  // Typing indicators\n  startTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot start typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StartTupingResponse>({\n        mutation: START_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.startTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error starting typing indicator',\n            error\n          );\n          return throwError(\n            () => new Error('Failed to start typing indicator')\n          );\n        })\n      );\n  }\n\n  stopTyping(conversationId: string): Observable<boolean> {\n    const userId = this.getCurrentUserId();\n    if (!userId) {\n      this.logger.warn('MessageService', 'Cannot stop typing: no user ID');\n      return of(false);\n    }\n\n    return this.apollo\n      .mutate<StopTypingResponse>({\n        mutation: STOP_TYPING_MUTATION,\n        variables: {\n          input: {\n            conversationId,\n            userId,\n          },\n        },\n      })\n      .pipe(\n        map((result) => result.data?.stopTyping || false),\n        catchError((error) => {\n          this.logger.error(\n            'MessageService',\n            'Error stopping typing indicator',\n            error\n          );\n          return throwError(() => new Error('Failed to stop typing indicator'));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODE SENDMESSAGE MANQUANTE\n  // ========================================\n\n  /**\n   * Envoie un message (texte, fichier, audio, etc.)\n   * @param receiverId ID du destinataire\n   * @param content Contenu du message (texte)\n   * @param file Fichier à envoyer (optionnel)\n   * @param messageType Type de message (TEXT, AUDIO, IMAGE, etc.)\n   * @param conversationId ID de la conversation\n   * @returns Observable avec le message envoyé\n   */\n  sendMessage(\n    receiverId: string,\n    content: string,\n    file?: File,\n    messageType: any = 'TEXT',\n    conversationId?: string\n  ): Observable<Message> {\n    console.log('🚀 [MessageService] sendMessage called with:', {\n      receiverId,\n      content: content?.substring(0, 50),\n      hasFile: !!file,\n      fileName: file?.name,\n      fileType: file?.type,\n      fileSize: file?.size,\n      messageType,\n      conversationId,\n    });\n\n    if (!receiverId) {\n      const error = new Error('Receiver ID is required');\n      console.error('❌ [MessageService] sendMessage error:', error);\n      return throwError(() => error);\n    }\n\n    // Préparer les variables pour la mutation\n    const variables: any = {\n      receiverId,\n      content: content || '',\n      type: messageType,\n    };\n\n    // Ajouter l'ID de conversation si fourni\n    if (conversationId) {\n      variables.conversationId = conversationId;\n    }\n\n    // Si un fichier est fourni, l'ajouter aux variables\n    if (file) {\n      variables.file = file;\n      console.log('📁 [MessageService] Adding file to mutation:', {\n        name: file.name,\n        type: file.type,\n        size: file.size,\n      });\n    }\n\n    console.log(\n      '📤 [MessageService] Sending mutation with variables:',\n      variables\n    );\n\n    return this.apollo\n      .mutate<SendMessageResponse>({\n        mutation: SEND_MESSAGE_MUTATION,\n        variables,\n        context: {\n          useMultipart: !!file, // Utiliser multipart si un fichier est présent\n        },\n      })\n      .pipe(\n        map((result) => {\n          console.log(\n            '✅ [MessageService] sendMessage mutation result:',\n            result\n          );\n\n          if (!result.data?.sendMessage) {\n            throw new Error('No message data received from server');\n          }\n\n          const message = result.data.sendMessage;\n          console.log('📨 [MessageService] Message sent successfully:', {\n            id: message.id,\n            type: message.type,\n            content: message.content?.substring(0, 50),\n            hasAttachments: !!message.attachments?.length,\n          });\n\n          // Normaliser le message reçu\n          const normalizedMessage = this.normalizeMessage(message);\n          console.log(\n            '🔧 [MessageService] Message normalized:',\n            normalizedMessage\n          );\n\n          return normalizedMessage;\n        }),\n        catchError((error) => {\n          console.error('❌ [MessageService] sendMessage error:', error);\n          this.logger.error('Error sending message:', error);\n\n          // Fournir un message d'erreur plus spécifique\n          let errorMessage = \"Erreur lors de l'envoi du message\";\n          if (error.networkError) {\n            errorMessage = 'Erreur de connexion réseau';\n          } else if (error.graphQLErrors?.length > 0) {\n            errorMessage = error.graphQLErrors[0].message || errorMessage;\n          }\n\n          return throwError(() => new Error(errorMessage));\n        })\n      );\n  }\n\n  // ========================================\n  // MÉTHODES UTILITAIRES CONSOLIDÉES\n  // ========================================\n\n  /**\n   * Formate l'heure d'un message\n   */\n  formatMessageTime(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown time';\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      return date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n        hour12: false,\n      });\n    } catch (error) {\n      return 'Invalid time';\n    }\n  }\n\n  /**\n   * Formate la dernière activité d'un utilisateur\n   */\n  formatLastActive(lastActive: string | Date | undefined): string {\n    if (!lastActive) return 'Offline';\n    const lastActiveDate =\n      lastActive instanceof Date ? lastActive : new Date(lastActive);\n    const now = new Date();\n    const diffHours =\n      Math.abs(now.getTime() - lastActiveDate.getTime()) / (1000 * 60 * 60);\n\n    if (diffHours < 24) {\n      return `Active ${lastActiveDate.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    }\n    return `Active ${lastActiveDate.toLocaleDateString()}`;\n  }\n\n  /**\n   * Formate la date d'un message\n   */\n  formatMessageDate(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'Unknown date';\n\n    try {\n      const date = timestamp instanceof Date ? timestamp : new Date(timestamp);\n      const today = new Date();\n\n      if (date.toDateString() === today.toDateString()) {\n        return date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        });\n      }\n\n      const yesterday = new Date(today);\n      yesterday.setDate(yesterday.getDate() - 1);\n\n      if (date.toDateString() === yesterday.toDateString()) {\n        return `LUN., ${date.toLocaleTimeString([], {\n          hour: '2-digit',\n          minute: '2-digit',\n        })}`;\n      }\n\n      const day = date\n        .toLocaleDateString('fr-FR', { weekday: 'short' })\n        .toUpperCase();\n      return `${day}., ${date.toLocaleTimeString([], {\n        hour: '2-digit',\n        minute: '2-digit',\n      })}`;\n    } catch (error) {\n      return 'Invalid date';\n    }\n  }\n\n  /**\n   * Détermine si un en-tête de date doit être affiché\n   */\n  shouldShowDateHeader(messages: any[], index: number): boolean {\n    if (index === 0) return true;\n\n    try {\n      const currentMsg = messages[index];\n      const prevMsg = messages[index - 1];\n\n      if (!currentMsg?.timestamp || !prevMsg?.timestamp) return true;\n\n      const currentDate = this.getDateFromTimestamp(currentMsg.timestamp);\n      const prevDate = this.getDateFromTimestamp(prevMsg.timestamp);\n\n      return currentDate !== prevDate;\n    } catch (error) {\n      return false;\n    }\n  }\n\n  private getDateFromTimestamp(timestamp: string | Date | undefined): string {\n    if (!timestamp) return 'unknown-date';\n    try {\n      return (\n        timestamp instanceof Date ? timestamp : new Date(timestamp)\n      ).toDateString();\n    } catch (error) {\n      return 'invalid-date';\n    }\n  }\n\n  /**\n   * Obtient l'icône d'un fichier selon son type MIME\n   */\n  getFileIcon(mimeType?: string): string {\n    if (!mimeType) return 'fa-file';\n    if (mimeType.startsWith('image/')) return 'fa-image';\n    if (mimeType.includes('pdf')) return 'fa-file-pdf';\n    if (mimeType.includes('word') || mimeType.includes('msword'))\n      return 'fa-file-word';\n    if (mimeType.includes('excel')) return 'fa-file-excel';\n    if (mimeType.includes('powerpoint')) return 'fa-file-powerpoint';\n    if (mimeType.includes('audio')) return 'fa-file-audio';\n    if (mimeType.includes('video')) return 'fa-file-video';\n    if (mimeType.includes('zip') || mimeType.includes('compressed'))\n      return 'fa-file-archive';\n    return 'fa-file';\n  }\n\n  /**\n   * Obtient le type d'un fichier selon son type MIME\n   */\n  getFileType(mimeType?: string): string {\n    if (!mimeType) return 'File';\n\n    const typeMap: Record<string, string> = {\n      'image/': 'Image',\n      'application/pdf': 'PDF',\n      'application/msword': 'Word Doc',\n      'application/vnd.openxmlformats-officedocument.wordprocessingml.document':\n        'Word Doc',\n      'application/vnd.ms-excel': 'Excel',\n      'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet':\n        'Excel',\n      'application/vnd.ms-powerpoint': 'PowerPoint',\n      'application/vnd.openxmlformats-officedocument.presentationml.presentation':\n        'PowerPoint',\n      'audio/': 'Audio',\n      'video/': 'Video',\n      'application/zip': 'ZIP Archive',\n      'application/x-rar-compressed': 'RAR Archive',\n    };\n\n    for (const [key, value] of Object.entries(typeMap)) {\n      if (mimeType.includes(key)) return value;\n    }\n    return 'File';\n  }\n\n  /**\n   * Vérifie si un message contient une image\n   */\n  hasImage(message: any): boolean {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return false;\n    }\n\n    const attachment = message.attachments[0];\n    if (!attachment || !attachment.type) {\n      return false;\n    }\n\n    const type = attachment.type.toString();\n    return type === 'IMAGE' || type === 'image';\n  }\n\n  /**\n   * Vérifie si le message est un message vocal\n   */\n  isVoiceMessage(message: any): boolean {\n    if (!message) return false;\n\n    // Vérifier le type du message\n    if (\n      message.type === MessageType.VOICE_MESSAGE ||\n      message.type === MessageType.VOICE_MESSAGE\n    ) {\n      return true;\n    }\n\n    // Vérifier les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      return message.attachments.some((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          (message.metadata?.isVoiceMessage &&\n            (type === 'AUDIO' || type === 'audio'))\n        );\n      });\n    }\n\n    // Vérifier les métadonnées\n    return !!message.metadata?.isVoiceMessage;\n  }\n\n  /**\n   * Récupère l'URL du message vocal\n   */\n  getVoiceMessageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const voiceAttachment = message.attachments.find((att: any) => {\n      const type = att.type?.toString();\n      return (\n        type === 'VOICE_MESSAGE' ||\n        type === 'voice_message' ||\n        type === 'AUDIO' ||\n        type === 'audio'\n      );\n    });\n\n    return voiceAttachment?.url || '';\n  }\n\n  /**\n   * Récupère la durée du message vocal\n   */\n  getVoiceMessageDuration(message: any): number {\n    if (!message) return 0;\n\n    // Essayer d'abord de récupérer la durée depuis les métadonnées\n    if (message.metadata?.duration) {\n      return message.metadata.duration;\n    }\n\n    // Sinon, essayer de récupérer depuis les pièces jointes\n    if (message.attachments && message.attachments.length > 0) {\n      const voiceAttachment = message.attachments.find((att: any) => {\n        const type = att.type?.toString();\n        return (\n          type === 'VOICE_MESSAGE' ||\n          type === 'voice_message' ||\n          type === 'AUDIO' ||\n          type === 'audio'\n        );\n      });\n\n      if (voiceAttachment && voiceAttachment.duration) {\n        return voiceAttachment.duration;\n      }\n    }\n\n    return 0;\n  }\n\n  /**\n   * Génère la hauteur des barres de la forme d'onde moderne\n   */\n  getVoiceBarHeight(index: number): number {\n    const pattern = [\n      8, 12, 6, 15, 10, 18, 7, 14, 9, 16, 5, 13, 11, 17, 8, 12, 6, 15, 10, 18,\n    ];\n    return pattern[index % pattern.length];\n  }\n\n  /**\n   * Formate la durée du message vocal en format MM:SS\n   */\n  formatVoiceDuration(seconds: number): string {\n    if (!seconds || seconds === 0) {\n      return '0:00';\n    }\n\n    const minutes = Math.floor(seconds / 60);\n    const remainingSeconds = Math.floor(seconds % 60);\n    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;\n  }\n\n  /**\n   * Obtient l'URL de l'image en toute sécurité\n   */\n  getImageUrl(message: any): string {\n    if (!message || !message.attachments || message.attachments.length === 0) {\n      return '';\n    }\n\n    const attachment = message.attachments[0];\n    return attachment?.url || '';\n  }\n\n  /**\n   * Détermine le type d'un message\n   */\n  getMessageType(message: any): MessageType {\n    if (!message) return MessageType.TEXT;\n\n    try {\n      if (message.type) {\n        const msgType = message.type.toString();\n        if (msgType === 'text' || msgType === 'TEXT') {\n          return MessageType.TEXT;\n        } else if (msgType === 'image' || msgType === 'IMAGE') {\n          return MessageType.IMAGE;\n        } else if (msgType === 'file' || msgType === 'FILE') {\n          return MessageType.FILE;\n        } else if (msgType === 'audio' || msgType === 'AUDIO') {\n          return MessageType.AUDIO;\n        } else if (msgType === 'video' || msgType === 'VIDEO') {\n          return MessageType.VIDEO;\n        } else if (msgType === 'system' || msgType === 'SYSTEM') {\n          return MessageType.SYSTEM;\n        }\n      }\n\n      if (message.attachments?.length) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n\n          if (attachmentTypeStr === 'image' || attachmentTypeStr === 'IMAGE') {\n            return MessageType.IMAGE;\n          } else if (\n            attachmentTypeStr === 'file' ||\n            attachmentTypeStr === 'FILE'\n          ) {\n            return MessageType.FILE;\n          } else if (\n            attachmentTypeStr === 'audio' ||\n            attachmentTypeStr === 'AUDIO'\n          ) {\n            return MessageType.AUDIO;\n          } else if (\n            attachmentTypeStr === 'video' ||\n            attachmentTypeStr === 'VIDEO'\n          ) {\n            return MessageType.VIDEO;\n          }\n        }\n\n        return MessageType.FILE;\n      }\n\n      return MessageType.TEXT;\n    } catch (error) {\n      return MessageType.TEXT;\n    }\n  }\n\n  /**\n   * Retourne la liste des emojis communs\n   */\n  getCommonEmojis(): string[] {\n    return [\n      '😀',\n      '😃',\n      '😄',\n      '😁',\n      '😆',\n      '😅',\n      '😂',\n      '🤣',\n      '😊',\n      '😇',\n      '🙂',\n      '🙃',\n      '😉',\n      '😌',\n      '😍',\n      '🥰',\n      '😘',\n      '😗',\n      '😙',\n      '😚',\n      '😋',\n      '😛',\n      '😝',\n      '😜',\n      '🤪',\n      '🤨',\n      '🧐',\n      '🤓',\n      '😎',\n      '🤩',\n      '😏',\n      '😒',\n      '😞',\n      '😔',\n      '😟',\n      '😕',\n      '🙁',\n      '☹️',\n      '😣',\n      '😖',\n      '😫',\n      '😩',\n      '🥺',\n      '😢',\n      '😭',\n      '😤',\n      '😠',\n      '😡',\n      '🤬',\n      '🤯',\n      '😳',\n      '🥵',\n      '🥶',\n      '😱',\n      '😨',\n      '😰',\n      '😥',\n      '😓',\n      '🤗',\n      '🤔',\n      '👍',\n      '👎',\n      '👏',\n      '🙌',\n      '👐',\n      '🤲',\n      '🤝',\n      '🙏',\n      '✌️',\n      '🤞',\n      '❤️',\n      '🧡',\n      '💛',\n      '💚',\n      '💙',\n      '💜',\n      '🖤',\n      '💔',\n      '💯',\n      '💢',\n    ];\n  }\n\n  /**\n   * Obtient les classes CSS pour un message\n   */\n  getMessageTypeClass(message: any, currentUserId: string | null): string {\n    if (!message) {\n      return 'bg-gray-100 rounded-lg px-4 py-2';\n    }\n\n    try {\n      const isCurrentUser =\n        message.sender?.id === currentUserId ||\n        message.sender?._id === currentUserId ||\n        message.senderId === currentUserId;\n\n      const baseClass = isCurrentUser\n        ? 'bg-blue-500 text-white rounded-2xl rounded-br-sm'\n        : 'bg-gray-200 text-gray-800 rounded-2xl rounded-bl-sm';\n\n      const messageType = this.getMessageType(message);\n\n      if (message.attachments && message.attachments.length > 0) {\n        const attachment = message.attachments[0];\n        if (attachment && attachment.type) {\n          const attachmentTypeStr = attachment.type.toString();\n          if (attachmentTypeStr === 'IMAGE' || attachmentTypeStr === 'image') {\n            return `p-1 max-w-xs`;\n          } else if (\n            attachmentTypeStr === 'FILE' ||\n            attachmentTypeStr === 'file'\n          ) {\n            return `${baseClass} p-3`;\n          }\n        }\n      }\n\n      // Les vérifications de type sont déjà faites avec les attachments ci-dessus\n\n      return `${baseClass} px-4 py-3 whitespace-normal break-words min-w-[120px]`;\n    } catch (error) {\n      return 'bg-gray-100 rounded-lg px-4 py-2 whitespace-normal break-words';\n    }\n  }\n\n  // ========================================\n  // APPELS WEBRTC - DÉLÉGUÉS AU CALLSERVICE\n  // ========================================\n  // Note: Les méthodes d'appel ont été déplacées vers CallService\n  // pour éviter la duplication de code et centraliser la logique\n\n  // destroy\n  cleanupSubscriptions(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n    this.subscriptions = [];\n    if (this.cleanupInterval) {\n      clearInterval(this.cleanupInterval);\n    }\n    this.notificationCache.clear();\n    this.logger.debug('NotificationService destroyed');\n  }\n\n  ngOnDestroy() {\n    this.cleanupSubscriptions();\n  }\n}\n"], "mappings": "AAEA,SACEA,eAAe,EACfC,UAAU,EACVC,EAAE,EAEFC,UAAU,EACVC,KAAK,EACLC,KAAK,QACA,MAAM;AACb,SACEC,GAAG,EACHC,UAAU,EACVC,GAAG,EACHC,MAAM,QAID,gBAAgB;AAEvB,SACEC,WAAW,EAEXC,QAAQ,EACRC,UAAU,QAML,yBAAyB;AAChC,SACEC,uBAAuB,EACvBC,uBAAuB,EACvBC,yBAAyB,EACzBC,sBAAsB,EACtBC,qBAAqB,EACrBC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,cAAc,EACdC,kBAAkB,EAClBC,iCAAiC,EACjCC,qBAAqB,EACrBC,yBAAyB,EACzBC,wBAAwB,EACxBC,yBAAyB,EACzBC,qBAAqB,EACrBC,oBAAoB,EACpBC,6BAA6B,EAC7BC,sBAAsB,EACtBC,yBAAyB,EACzBC,wBAAwB,EACxBC,oBAAoB,EACpBC,qBAAqB,EACrBC,qBAAqB,EACrBC,qBAAqB,EAGrBC,oBAAoB,EACpBC,eAAe,EACfC,qBAAqB,EACrBC,qBAAqB,EACrBC,uBAAuB,EACvBC,kBAAkB,EAClBC,8BAA8B,EAC9BC,+BAA+B,EAC/BC,+BAA+B,EAC/BC,4BAA4B,EAC5BC,4BAA4B,EAC5BC,sCAAsC,EACtCC,iCAAiC;AACjC;AACAC,kBAAkB,EAClBC,kBAAkB,EAClBC,gBAAgB,EAEhBC,yBAAyB,EAKzBC,wBAAwB,EACxBC,0BAA0B,EAE1BC,wBAAwB,QACnB,4BAA4B;;;;AA0CnC,OAAM,MAAOC,cAAc;EA6DzBC,YACUC,MAAc,EACdC,MAAqB,EACrBC,IAAY;IAFZ,KAAAF,MAAM,GAANA,MAAM;IACN,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,IAAI,GAAJA,IAAI;IA/Dd;IACQ,KAAAC,kBAAkB,GAAG,IAAI/D,eAAe,CAAgB,IAAI,CAAC;IAC7D,KAAAgE,aAAa,GAAG,IAAIhE,eAAe,CAAiB,EAAE,CAAC;IACvD,KAAAiE,iBAAiB,GAAG,IAAIC,GAAG,EAAwB;IAEnD,KAAAC,iBAAiB,GAAG,IAAInE,eAAe,CAAS,CAAC,CAAC;IAClD,KAAAoE,WAAW,GAAG,IAAIF,GAAG,EAAgB;IACrC,KAAAG,aAAa,GAAmB,EAAE;IACzB,KAAAC,cAAc,GAAG,MAAM;IAChC,KAAAC,aAAa,GAAG,CAAC;IAEzB;IACQ,KAAAC,UAAU,GAAG,IAAIxE,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAyE,YAAY,GAAG,IAAIzE,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAA0E,WAAW,GAAG,IAAI1E,eAAe,CAAoB,IAAI,CAAC;IAC1D,KAAA2E,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,cAAc,GAA6B,IAAI;IAEvD;IACO,KAAAC,WAAW,GAAG,IAAI,CAACN,UAAU,CAACO,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACP,YAAY,CAACM,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACP,WAAW,CAACK,YAAY,EAAE;IAC9C,KAAAG,YAAY,GAAG,IAAIlF,eAAe,CAAqB,IAAI,CAAC;IAC5D,KAAAmF,aAAa,GAAG,IAAInF,eAAe,CAAqB,IAAI,CAAC;IAEpE;IACiB,KAAAoF,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IACO,KAAAC,UAAU,GAAW,EAAE;IAE/B;IACO,KAAAC,qBAAqB,GAMxB;MACFC,UAAU,EAAE,CAAC;MACbC,UAAU,EAAE,CAAC;MACbC,WAAW,EAAE,CAAC;MACdC,WAAW,EAAE,KAAK;MAClBC,eAAe,EAAE;KAClB;IAED;IACO,KAAAC,mBAAmB,GAAG,IAAI,CAAC/B,kBAAkB,CAACgB,YAAY,EAAE;IAC5D,KAAAgB,cAAc,GAAG,IAAI,CAAC/B,aAAa,CAACe,YAAY,EAAE;IAClD,KAAAiB,kBAAkB,GAAG,IAAI,CAAC7B,iBAAiB,CAACY,YAAY,EAAE;IAEjE;IACQ,KAAAkB,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAC1C,KAAAC,KAAK,GAAG,KAAK;IA01BrB;IACA;IACA;IACA;IACQ,KAAAC,sBAAsB,GAAG;MAC/BT,WAAW,EAAE,CAAC;MACdU,KAAK,EAAE,EAAE;MACTC,oBAAoB,EAAE;KACvB;IA6wEO,KAAAC,eAAe,GACrBC,IAA+B,IACT;MACtB,IAAI,CAACA,IAAI,EAAE,OAAOC,SAAS;MAC3B,OAAO,OAAOD,IAAI,KAAK,QAAQ,GAAGA,IAAI,GAAGA,IAAI,CAACE,WAAW,EAAE;IAC7D,CAAC;IA7mGC,IAAI,CAACC,iCAAiC,EAAE;IACxC,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;;EAIQH,iCAAiCA,CAAA;IACvC,IAAI;MACF,MAAMI,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAChE,IAAIF,kBAAkB,EAAE;QACtB,MAAM/C,aAAa,GAAGkD,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAmB;QAEtE,IAAI,CAAC9C,iBAAiB,CAACmD,KAAK,EAAE;QAE9BpD,aAAa,CAACqD,OAAO,CAAEC,YAAY,IAAI;UACrC,IAAIA,YAAY,IAAIA,YAAY,CAACC,EAAE,EAAE;YACnC,IAAI,CAACtD,iBAAiB,CAACuD,GAAG,CAACF,YAAY,CAACC,EAAE,EAAED,YAAY,CAAC;;QAE7D,CAAC,CAAC;QAEF,IAAI,CAACtD,aAAa,CAACyD,IAAI,CAACC,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,EAAE,CAAC,CAAC;QACpE,IAAI,CAACC,iBAAiB,EAAE;;KAE3B,CAAC,OAAOC,KAAK,EAAE;MACd;IAAA;EAEJ;EACQlB,iBAAiBA,CAAA;IACvB,IAAI,CAAC9C,IAAI,CAACiE,iBAAiB,CAAC,MAAK;MAC/B,IAAI,CAACC,2BAA2B,EAAE,CAACC,SAAS,EAAE;MAC9C,IAAI,CAACC,4BAA4B,EAAE,CAACD,SAAS,EAAE;MAC/C,IAAI,CAACE,wBAAwB,EAAE,CAACF,SAAS,EAAE;MAC3C;IACF,CAAC,CAAC;;IACF,IAAI,CAACG,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQD,wBAAwBA,CAAA;IAC9B,OAAO,IAAI,CAACvE,MAAM,CACfqE,SAAS,CAAiC;MACzCI,KAAK,EAAE7E;KACR,CAAC,CACD8E,IAAI,CACHhI,GAAG,CAAC,CAAC;MAAEiI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAE9D,YAAY,EAAE;QACvB,OAAO,IAAI;;MAGb;MACA,IAAI,CAAC+D,kBAAkB,CAACD,IAAI,CAAC9D,YAAY,CAAC;MAC1C,OAAO8D,IAAI,CAAC9D,YAAY;IAC1B,CAAC,CAAC,EACFlE,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,qCAAqC,EAAEA,KAAK,CAAC;MAC/D,OAAO5H,EAAE,CAAC,IAAI,CAAC;IACjB,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQsI,kBAAkBA,CAACC,IAAkB;IAC3C,IAAI,CAAChE,YAAY,CAACgD,IAAI,CAACgB,IAAI,CAAC;IAC5B,IAAI,CAACC,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;EACA;EACA;EAEA;;;EAGQ5B,aAAaA,CAAA;IACnB,IAAI,CAAC6B,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,UAAU,EAAE,4BAA4B,CAAC;IACxD,IAAI,CAACA,SAAS,CAAC,gBAAgB,EAAE,kCAAkC,CAAC;IACpE,IAAI,CAACA,SAAS,CAAC,cAAc,EAAE,gCAAgC,CAAC;EAClE;EAEA;;;;;EAKQA,SAASA,CAACC,IAAY,EAAEC,IAAY;IAC1C,IAAI;MACF,MAAMC,KAAK,GAAG,IAAIC,KAAK,CAACF,IAAI,CAAC;MAC7BC,KAAK,CAACE,IAAI,EAAE;MACZ,IAAI,CAAC/C,MAAM,CAAC2C,IAAI,CAAC,GAAGE,KAAK;MACzB,IAAI,CAAC5C,SAAS,CAAC0C,IAAI,CAAC,GAAG,KAAK;MAE5BE,KAAK,CAACG,gBAAgB,CAAC,OAAO,EAAE,MAAK;QACnC,IAAI,CAAC/C,SAAS,CAAC0C,IAAI,CAAC,GAAG,KAAK;MAC9B,CAAC,CAAC;KACH,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;;EAKAY,IAAIA,CAACE,IAAY,EAAEM,IAAA,GAAgB,KAAK;IACtC,IAAI,IAAI,CAAC/C,KAAK,EAAE;MACd;;IAGF,IAAI;MACF,MAAMgD,KAAK,GAAG,IAAI,CAAClD,MAAM,CAAC2C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGFA,KAAK,CAACD,IAAI,GAAGA,IAAI;MAEjB,IAAI,CAAC,IAAI,CAAChD,SAAS,CAAC0C,IAAI,CAAC,EAAE;QACzBO,KAAK,CAACC,WAAW,GAAG,CAAC;QACrBD,KAAK,CAACT,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;UAC3B;QAAA,CACD,CAAC;QACF,IAAI,CAAC5B,SAAS,CAAC0C,IAAI,CAAC,GAAG,IAAI;;KAE9B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;;EAIAwB,IAAIA,CAACV,IAAY;IACf,IAAI;MACF,MAAMO,KAAK,GAAG,IAAI,CAAClD,MAAM,CAAC2C,IAAI,CAAC;MAC/B,IAAI,CAACO,KAAK,EAAE;QACV;;MAGF,IAAI,IAAI,CAACjD,SAAS,CAAC0C,IAAI,CAAC,EAAE;QACxBO,KAAK,CAACI,KAAK,EAAE;QACbJ,KAAK,CAACC,WAAW,GAAG,CAAC;QACrB,IAAI,CAAClD,SAAS,CAAC0C,IAAI,CAAC,GAAG,KAAK;;KAE/B,CAAC,OAAOd,KAAK,EAAE;MACd;IAAA;EAEJ;EAEA;;;EAGA0B,aAAaA,CAAA;IACXC,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzD,MAAM,CAAC,CAACoB,OAAO,CAAEuB,IAAI,IAAI;MACxC,IAAI,CAACU,IAAI,CAACV,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;;EAIAe,QAAQA,CAACxD,KAAc;IACrB,IAAI,CAACA,KAAK,GAAGA,KAAK;IAElB,IAAIA,KAAK,EAAE;MACT,IAAI,CAACqD,aAAa,EAAE;;EAExB;EAEA;;;;EAIAI,OAAOA,CAAA;IACL,OAAO,IAAI,CAACzD,KAAK;EACnB;EAEA;;;EAGA0D,qBAAqBA,CAAA;IACnBC,OAAO,CAACC,GAAG,CAAC,6DAA6D,CAAC;IAE1E,IAAI,IAAI,CAAC5D,KAAK,EAAE;MACd2D,OAAO,CAACC,GAAG,CAAC,qDAAqD,CAAC;MAClE;;IAGF;IACA,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MAEA;MACA,IAAI,CAACC,uBAAuB,CAACJ,YAAY,CAAC;MAE1C;MACA;MAEA;MACA;MAEA;MACA;MAEA;MACA;MAEAF,OAAO,CAACC,GAAG,CACT,kEAAkE,CACnE;KACF,CAAC,OAAOjC,KAAK,EAAE;MACdgC,OAAO,CAAChC,KAAK,CACX,sDAAsD,EACtDA,KAAK,CACN;MAED;MACA,IAAI;QACF,MAAMgB,KAAK,GAAG,IAAIC,KAAK,CAAC,gCAAgC,CAAC;QACzDD,KAAK,CAACuB,MAAM,GAAG,GAAG,CAAC,CAAC;QACpBvB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEiB,GAAG,IAAI;UACzBR,OAAO,CAAChC,KAAK,CACX,2DAA2D,EAC3DwC,GAAG,CACJ;QACH,CAAC,CAAC;OACH,CAAC,OAAOC,UAAU,EAAE;QACnBT,OAAO,CAAChC,KAAK,CACX,8DAA8D,EAC9DyC,UAAU,CACX;;;EAGP;EAEA;EACQH,uBAAuBA,CAACJ,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQS,uBAAuBA,CAACT,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC/D;EAEA;EACQU,uBAAuBA,CAACV,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;IACtD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;IAC3D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,GAAG,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;EAC9D;EAEA;EACQW,uBAAuBA,CAACX,YAA0B;IACxD,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC1D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,MAAM,EAAE,IAAI,CAAC,CAAC,CAAC;IAC7D,IAAI,CAACQ,oBAAoB,CAACR,YAAY,EAAE,IAAI,EAAE,GAAG,EAAE,IAAI,CAAC,CAAC,CAAC;EAC5D;EAEA;EACQY,uBAAuBA,CAACZ,YAA0B;IACxD,IAAI,CAACa,YAAY,CAACb,YAAY,EAAE,CAAC,EAAE,MAAM,EAAE,GAAG,CAAC,CAAC,CAAC;EACnD;EAEA;;;EAGQQ,oBAAoBA,CAC1BR,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,MAAM;IACxBJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,GAAG,GAAG,CACtD;IACDG,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,CAAC,EACDxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EAEA;;;EAGQH,YAAYA,CAClBb,YAA0B,EAC1Bc,SAAiB,EACjBC,SAAiB,EACjBC,QAAgB;IAEhB,MAAMC,UAAU,GAAGjB,YAAY,CAACkB,gBAAgB,EAAE;IAClD,MAAMC,QAAQ,GAAGnB,YAAY,CAACoB,UAAU,EAAE;IAE1C;IACAH,UAAU,CAACI,IAAI,GAAG,UAAU,CAAC,CAAC;IAC9BJ,UAAU,CAACF,SAAS,CAACO,cAAc,CACjCP,SAAS,EACTf,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CACrC;IAED;IACAK,QAAQ,CAACI,IAAI,CAACD,cAAc,CAAC,CAAC,EAAEtB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACrEK,QAAQ,CAACI,IAAI,CAACC,uBAAuB,CACnC,GAAG,EACHxB,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAG,IAAI,CAC5C;IACDK,QAAQ,CAACI,IAAI,CAACK,4BAA4B,CACxC,IAAI,EACJ5B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAChD;IAED;IACAC,UAAU,CAACQ,OAAO,CAACN,QAAQ,CAAC;IAC5BA,QAAQ,CAACM,OAAO,CAACzB,YAAY,CAAC0B,WAAW,CAAC;IAE1C;IACAT,UAAU,CAACU,KAAK,CAAC3B,YAAY,CAACZ,WAAW,GAAG0B,SAAS,CAAC;IACtDG,UAAU,CAAC3B,IAAI,CAACU,YAAY,CAACZ,WAAW,GAAG0B,SAAS,GAAGE,QAAQ,CAAC;EAClE;EACA;EACA;EACA;EAEA;;;;;EAKAa,SAASA,CAACC,QAAgB;IACxB,OAAO,IAAIC,OAAO,CAAC,CAACC,OAAO,EAAEC,MAAM,KAAI;MACrC,MAAMnD,KAAK,GAAG,IAAIC,KAAK,CAAC+C,QAAQ,CAAC;MAEjChD,KAAK,CAACoD,OAAO,GAAG,MAAK;QACnBF,OAAO,EAAE;MACX,CAAC;MAEDlD,KAAK,CAACqD,OAAO,GAAIrE,KAAK,IAAI;QACxB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEmE,MAAM,CAACnE,KAAK,CAAC;MACf,CAAC;MAEDgB,KAAK,CAACJ,IAAI,EAAE,CAACW,KAAK,CAAEvB,KAAK,IAAI;QAC3B,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;QACjEmE,MAAM,CAACnE,KAAK,CAAC;MACf,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEA;;;;EAIAsE,gBAAgBA,CAAA;IACd,IAAI,CAACvI,MAAM,CAACwI,KAAK,CAAC,yCAAyC,CAAC;IAE5D,OAAO,IAAI,CAACzI,MAAM,CACf0I,UAAU,CAA+B;MACxCjE,KAAK,EAAE5E,wBAAwB;MAC/B8I,WAAW,EAAE,cAAc,CAAE;KAC9B,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMC,aAAa,GAAGD,MAAM,CAAClE,IAAI,EAAE6D,gBAAgB,IAAI,EAAE;MACzD,IAAI,CAACvI,MAAM,CAACwI,KAAK,CACf,8BAA8BK,aAAa,CAACC,MAAM,iBAAiB,CACpE;MACD,OAAOD,aAAa;IACtB,CAAC,CAAC,EACFnM,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,iDAAiD,EACjDA,KAAK,CACN;MACD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EACA;EACAC,WAAWA,CACTC,QAAgB,EAChBC,UAAkB,EAClBC,cAAsB,EACtBC,IAAA,GAAe,CAAC,EAChB5G,KAAA,GAAgB,EAAE;IAElB,OAAO,IAAI,CAACzC,MAAM,CACf0I,UAAU,CAA6B;MACtCjE,KAAK,EAAE1F,kBAAkB;MACzBuK,SAAS,EAAE;QAAEJ,QAAQ;QAAEC,UAAU;QAAEC,cAAc;QAAE3G,KAAK;QAAE4G;MAAI,CAAE;MAChEV,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMU,QAAQ,GAAGV,MAAM,CAAClE,IAAI,EAAEsE,WAAW,IAAI,EAAE;MAC/C,OAAOM,QAAQ,CAAC7M,GAAG,CAAE8M,GAAG,IAAK,IAAI,CAACC,gBAAgB,CAACD,GAAG,CAAC,CAAC;IAC1D,CAAC,CAAC,EACF7M,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,0BAA0B,EAAEA,KAAK,CAAC;MACpD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EACAU,WAAWA,CAACC,SAAiB,EAAEC,UAAkB;IAC/C,OAAO,IAAI,CAAC5J,MAAM,CACf6J,MAAM,CAA2B;MAChCC,QAAQ,EAAEjL,qBAAqB;MAC/ByK,SAAS,EAAE;QAAEK,SAAS;QAAEC;MAAU;KACnC,CAAC,CACDlF,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE+E,WAAW,EAAE;QAC7B,MAAM,IAAIV,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,OAAO,IAAI,CAACS,gBAAgB,CAACZ,MAAM,CAAClE,IAAI,CAAC+E,WAAW,CAAC;IACvD,CAAC,CAAC,EACF/M,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAClD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,wBAAwB,CAAC,CAAC;IAC9D,CAAC,CAAC,CACH;EACL;EAEAe,aAAaA,CAACJ,SAAiB;IAC7B,OAAO,IAAI,CAAC3J,MAAM,CACf6J,MAAM,CAA6B;MAClCC,QAAQ,EAAEhL,uBAAuB;MACjCwK,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDjF,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEoF,aAAa,EAAE;QAC/B,MAAM,IAAIf,KAAK,CAAC,0BAA0B,CAAC;;MAE7C,OAAO,IAAI,CAACS,gBAAgB,CAACZ,MAAM,CAAClE,IAAI,CAACoF,aAAa,CAAC;IACzD,CAAC,CAAC,EACFpN,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;MACnD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,0BAA0B,CAAC,CAAC;IAChE,CAAC,CAAC,CACH;EACL;EAEAgB,iBAAiBA,CAACL,SAAiB;IACjC,OAAO,IAAI,CAAC3J,MAAM,CACf6J,MAAM,CAAqB;MAC1BC,QAAQ,EAAExM,qBAAqB;MAC/BgM,SAAS,EAAE;QAAEK;MAAS;KACvB,CAAC,CACDjF,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEqF,iBAAiB,EACjC,MAAM,IAAIhB,KAAK,CAAC,gCAAgC,CAAC;MACnD,OAAO;QACL,GAAGH,MAAM,CAAClE,IAAI,CAACqF,iBAAiB;QAChCC,MAAM,EAAE,IAAIC,IAAI;OACjB;IACH,CAAC,CAAC,EACFvN,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEAmB,cAAcA,CAACR,SAAiB,EAAES,KAAa;IAC7C,OAAO,IAAI,CAACpK,MAAM,CACf6J,MAAM,CAAyB;MAC9BC,QAAQ,EAAE1L,yBAAyB;MACnCkL,SAAS,EAAE;QAAEK,SAAS;QAAES;MAAK;KAC9B,CAAC,CACD1F,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAEwF,cAAc,EAC9B,MAAM,IAAInB,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAOH,MAAM,CAAClE,IAAI,CAACwF,cAAc;IACnC,CAAC,CAAC,EACFxN,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MAClD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEAqB,cAAcA,CACZV,SAAiB,EACjBW,eAAyB;IAEzB,OAAO,IAAI,CAACtK,MAAM,CACf6J,MAAM,CAAyB;MAC9BC,QAAQ,EAAEzL,wBAAwB;MAClCiL,SAAS,EAAE;QAAEK,SAAS;QAAEW;MAAe;KACxC,CAAC,CACD5F,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE0F,cAAc,EAC9B,MAAM,IAAIrB,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAOH,MAAM,CAAClE,IAAI,CAAC0F,cAAc,CAAC3N,GAAG,CAAE8M,GAAG,KAAM;QAC9C,GAAGA,GAAG;QACNe,SAAS,EAAEf,GAAG,CAACe,SAAS,GACpB,IAAI,CAACC,aAAa,CAAChB,GAAG,CAACe,SAAS,CAAC,GACjC,IAAIL,IAAI;OACb,CAAC,CAAC;IACL,CAAC,CAAC,EACFvN,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAyB,UAAUA,CAACd,SAAiB,EAAEP,cAAsB;IAClD,OAAO,IAAI,CAACpJ,MAAM,CACf6J,MAAM,CAAqB;MAC1BC,QAAQ,EAAExL,oBAAoB;MAC9BgL,SAAS,EAAE;QAAEK,SAAS;QAAEP;MAAc;KACvC,CAAC,CACD1E,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE8F,UAAU,EAC1B,MAAM,IAAIzB,KAAK,CAAC,uBAAuB,CAAC;MAC1C,OAAO;QACL,GAAGH,MAAM,CAAClE,IAAI,CAAC8F,UAAU;QACzBC,QAAQ,EAAE,IAAIR,IAAI;OACnB;IACH,CAAC,CAAC,EACFvN,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAC9C,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA2B,cAAcA,CACZlG,KAAa,EACb2E,cAAuB,EACvBwB,OAAA,GAAyB,EAAE;IAE3B,OAAO,IAAI,CAAC5K,MAAM,CACf0I,UAAU,CAAyB;MAClCjE,KAAK,EAAE7G,qBAAqB;MAC5B0L,SAAS,EAAE;QACT7E,KAAK;QACL2E,cAAc;QACd,GAAGwB,OAAO;QACVC,QAAQ,EAAE,IAAI,CAAClI,eAAe,CAACiI,OAAO,CAACC,QAAQ,CAAC;QAChDC,MAAM,EAAE,IAAI,CAACnI,eAAe,CAACiI,OAAO,CAACE,MAAM;OAC5C;MACDnC,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CACAmM,MAAM,IACLA,MAAM,CAAClE,IAAI,EAAEgG,cAAc,EAAEjO,GAAG,CAAE8M,GAAG,KAAM;MACzC,GAAGA,GAAG;MACNe,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACvB,GAAG,CAACe,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACzB,GAAG,CAACwB,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDrO,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACjD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EAEAkC,iBAAiBA,CAACC,MAAc;IAC9B,OAAO,IAAI,CAACnL,MAAM,CACf0I,UAAU,CAA4B;MACrCjE,KAAK,EAAE5G,yBAAyB;MAChCyL,SAAS,EAAE;QAAE6B;MAAM,CAAE;MACrBxC,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CACAmM,MAAM,IACLA,MAAM,CAAClE,IAAI,EAAEuG,iBAAiB,EAAExO,GAAG,CAAE8M,GAAG,KAAM;MAC5C,GAAGA,GAAG;MACNe,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACvB,GAAG,CAACe,SAAS,CAAC;MACvCS,MAAM,EAAE,IAAI,CAACC,aAAa,CAACzB,GAAG,CAACwB,MAAM;KACtC,CAAC,CAAC,IAAI,EAAE,CACZ,EACDrO,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,iCAAiC,EAAEA,KAAK,CAAC;MACvD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEAoC,qBAAqBA,CAAChC,cAAsB;IAC1C,IAAI,CAACjJ,kBAAkB,CAAC0D,IAAI,CAACuF,cAAc,CAAC;EAC9C;EAEAiC,gBAAgBA,CAAA;IACd,OAAO,IAAI,CAACrL,MAAM,CACf0I,UAAU,CAA2B;MACpCjE,KAAK,EAAExH,uBAAuB;MAC9B0L,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMyC,aAAa,GAAGzC,MAAM,CAAClE,IAAI,EAAE0G,gBAAgB,IAAI,EAAE;MACzD,OAAOC,aAAa,CAAC5O,GAAG,CAAE6O,IAAI,IAAK,IAAI,CAACC,qBAAqB,CAACD,IAAI,CAAC,CAAC;IACtE,CAAC,CAAC,EACF5O,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;MACrD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEAyC,eAAeA,CACbrC,cAAsB,EACtB3G,KAAc,EACd4G,IAAa;IAEb,IAAI,CAACpJ,MAAM,CAACyL,IAAI,CACd,0CAA0CtC,cAAc,YAAY3G,KAAK,WAAW4G,IAAI,EAAE,CAC3F;IAED,MAAMC,SAAS,GAAQ;MAAEF;IAAc,CAAE;IAEzC;IACA,IAAI3G,KAAK,KAAKI,SAAS,EAAE;MACvByG,SAAS,CAAC7G,KAAK,GAAGA,KAAK;KACxB,MAAM;MACL6G,SAAS,CAAC7G,KAAK,GAAG,EAAE,CAAC,CAAC;;IAGxB;IACA,IAAI4G,IAAI,KAAKxG,SAAS,EAAE;MACtB;MACA,MAAM8I,MAAM,GAAG,CAACtC,IAAI,GAAG,CAAC,IAAIC,SAAS,CAAC7G,KAAK;MAC3C6G,SAAS,CAACqC,MAAM,GAAGA,MAAM;MACzB,IAAI,CAAC1L,MAAM,CAACwI,KAAK,CACf,uCAAuCkD,MAAM,eAAetC,IAAI,eAAeC,SAAS,CAAC7G,KAAK,EAAE,CACjG;KACF,MAAM;MACL6G,SAAS,CAACqC,MAAM,GAAG,CAAC,CAAC,CAAC;;;IAGxB,IAAI,CAAC1L,MAAM,CAACwI,KAAK,CACf,uDAAuDa,SAAS,CAAC7G,KAAK,YAAY6G,SAAS,CAACqC,MAAM,EAAE,CACrG;IAED,OAAO,IAAI,CAAC3L,MAAM,CACf0I,UAAU,CAA0B;MACnCjE,KAAK,EAAErH,sBAAsB;MAC7BkM,SAAS,EAAEA,SAAS;MACpBX,WAAW,EAAE,cAAc;MAC3BiD,WAAW,EAAE;KACd,CAAC,CACDhD,YAAY,CAAClE,IAAI,CAChBlI,KAAK,CAAC,CAAC,CAAC;IAAE;IACVE,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CACf,kDAAkD,EAClDI,MAAM,CACP;MAED,MAAM0C,IAAI,GAAG1C,MAAM,CAAClE,IAAI,EAAE8G,eAAe;MACzC,IAAI,CAACF,IAAI,EAAE;QACT,IAAI,CAACtL,MAAM,CAACiE,KAAK,CACf,4CAA4CkF,cAAc,EAAE,CAC7D;QACD,MAAM,IAAIJ,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,8CAA8CW,cAAc,EAAE,CAC/D;MACD,MAAMyC,sBAAsB,GAAG,IAAI,CAACL,qBAAqB,CAACD,IAAI,CAAC;MAE/D,IAAI,CAACtL,MAAM,CAACyL,IAAI,CACd,sDAAsDtC,cAAc,mBAClEyC,sBAAsB,CAACC,YAAY,EAAE/C,MAAM,IAAI,CACjD,eAAe8C,sBAAsB,CAACtC,QAAQ,EAAER,MAAM,IAAI,CAAC,EAAE,CAC9D;MACD,OAAO8C,sBAAsB;IAC/B,CAAC,CAAC,EACFlP,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,+CAA+C,EAC/CA,KAAK,CACN;MACD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EAEA+C,kBAAkBA,CAACZ,MAAc;IAC/B,IAAI,CAAClL,MAAM,CAACyL,IAAI,CACd,qDAAqDP,MAAM,EAAE,CAC9D;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAClL,MAAM,CAACiE,KAAK,CACf,kEAAkE,CACnE;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,8CAA8C,CAAC,CAChE;;IAGH,OAAO,IAAI,CAAChJ,MAAM,CACf6J,MAAM,CAAuC;MAC5CC,QAAQ,EAAE3K,4BAA4B;MACtCmK,SAAS,EAAE;QAAE6B;MAAM;KACpB,CAAC,CACDzG,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CACf,kDAAkD,EAClDI,MAAM,CACP;MAED,MAAMmD,YAAY,GAAGnD,MAAM,CAAClE,IAAI,EAAEoH,kBAAkB;MACpD,IAAI,CAACC,YAAY,EAAE;QACjB,IAAI,CAAC/L,MAAM,CAACiE,KAAK,CACf,6DAA6DiH,MAAM,EAAE,CACtE;QACD,MAAM,IAAInC,KAAK,CAAC,+BAA+B,CAAC;;MAGlD,IAAI;QACF,MAAM6C,sBAAsB,GAC1B,IAAI,CAACL,qBAAqB,CAACQ,YAAY,CAAC;QAC1C,IAAI,CAAC/L,MAAM,CAACyL,IAAI,CACd,uDAAuDG,sBAAsB,CAAClI,EAAE,EAAE,CACnF;QACD,OAAOkI,sBAAsB;OAC9B,CAAC,OAAO3H,KAAK,EAAE;QACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,0DAA0D,EAC1DA,KAAK,CACN;QACD,MAAM,IAAI8E,KAAK,CAAC,uCAAuC,CAAC;;IAE5D,CAAC,CAAC,EACFrM,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,0DAA0DiH,MAAM,GAAG,EACnEjH,KAAK,CACN;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,kCAAkC9E,KAAK,CAAC+H,OAAO,EAAE,CAAC,CACnE;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKAC,uBAAuBA,CAACf,MAAc;IACpC,IAAI,CAAClL,MAAM,CAACyL,IAAI,CACd,gEAAgEP,MAAM,EAAE,CACzE;IAED,IAAI,CAACA,MAAM,EAAE;MACX,IAAI,CAAClL,MAAM,CAACiE,KAAK,CACf,sEAAsE,CACvE;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,kDAAkD,CAAC,CACpE;;IAGH;IACA,OAAO,IAAI,CAACqC,gBAAgB,EAAE,CAAC3G,IAAI,CACjChI,GAAG,CAAE4O,aAAa,IAAI;MACpB;MACA,MAAMa,aAAa,GAAG,IAAI,CAACC,gBAAgB,EAAE;MAE7C;MACA,MAAMC,oBAAoB,GAAGf,aAAa,CAACgB,IAAI,CAAEf,IAAI,IAAI;QACvD,IAAIA,IAAI,CAACgB,OAAO,EAAE,OAAO,KAAK;QAE9B;QACA,MAAMC,cAAc,GAClBjB,IAAI,CAACO,YAAY,EAAEpP,GAAG,CAAE+P,CAAC,IAAKA,CAAC,CAAC9I,EAAE,IAAI8I,CAAC,CAACC,GAAG,CAAC,IAAI,EAAE;QACpD,OACEF,cAAc,CAACG,QAAQ,CAACxB,MAAM,CAAC,IAC/BqB,cAAc,CAACG,QAAQ,CAACR,aAAa,CAAC;MAE1C,CAAC,CAAC;MAEF,IAAIE,oBAAoB,EAAE;QACxB,IAAI,CAACpM,MAAM,CAACyL,IAAI,CACd,iDAAiDW,oBAAoB,CAAC1I,EAAE,EAAE,CAC3E;QACD,OAAO0I,oBAAoB;;MAG7B;MACA,MAAM,IAAIrD,KAAK,CAAC,gCAAgC,CAAC;IACnD,CAAC,CAAC,EACFrM,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACyL,IAAI,CACd,sEAAsExH,KAAK,CAAC+H,OAAO,EAAE,CACtF;MACD,OAAO,IAAI,CAACF,kBAAkB,CAACZ,MAAM,CAAC;IACxC,CAAC,CAAC,CACH;EACH;EAYAyB,gBAAgBA,CACdC,OAAO,GAAG,KAAK,EACfxD,IAAI,GAAG,CAAC,EACR5G,KAAK,GAAG,EAAE;IAEV,IAAI,CAACxC,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,oCAAoCmB,OAAO,WAAWxD,IAAI,YAAY5G,KAAK,EAAE,CAC9E;IACD,IAAI,CAACxC,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,aAAa,EAAE;MACjDhE,KAAK,EAAEvH;KACR,CAAC;IAEF;IACA;IACA,IAAI2P,OAAO,EAAE;MACX,IAAI,CAAC5M,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,qCAAqC,CACtC;MACD,IAAI,CAACjG,sBAAsB,CAACT,WAAW,GAAG,CAAC;MAC3C,IAAI,CAACS,sBAAsB,CAACE,oBAAoB,GAAG,IAAI;;IAGzD;IACA,IAAI,CAACF,sBAAsB,CAACT,WAAW,GAAGsH,IAAI;IAC9C,IAAI,CAAC7G,sBAAsB,CAACC,KAAK,GAAGA,KAAK;IAEzC;IACA,MAAMqK,sBAAsB,GAAG,IAAI,CAACC,yBAAyB,EAAE;IAC/D,IAAI,CAAC9M,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,SAASqE,sBAAsB,CAACE,IAAI,2CAA2C,CAChF;IAED,OAAO,IAAI,CAAChN,MAAM,CACf0I,UAAU,CAA+B;MACxCjE,KAAK,EAAEvH,uBAAuB;MAC9BoM,SAAS,EAAE;QACTD,IAAI,EAAEA,IAAI;QACV5G,KAAK,EAAEA;OACR;MACDkG,WAAW,EAAEkE,OAAO,GAAG,cAAc,GAAG;KACzC,CAAC,CACDjE,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,iCAAiC,CAClC;MAED,IAAII,MAAM,CAACoE,MAAM,EAAE;QACjB,IAAI,CAAChN,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjB2E,MAAM,CAACoE,MAAM,CACd;QACD,MAAM,IAAIjE,KAAK,CAACH,MAAM,CAACoE,MAAM,CAACvQ,GAAG,CAAEwQ,CAAC,IAAKA,CAAC,CAACjB,OAAO,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,MAAM/M,aAAa,GAAGyI,MAAM,CAAClE,IAAI,EAAEyI,oBAAoB,IAAI,EAAE;MAC7D,IAAI,CAACnN,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,YAAYrI,aAAa,CAAC2I,MAAM,uCAAuCM,IAAI,EAAE,CAC9E;MAED;MACA,IAAI,CAAC7G,sBAAsB,CAACE,oBAAoB,GAC9CtC,aAAa,CAAC2I,MAAM,IAAItG,KAAK;MAE/B,IAAIrC,aAAa,CAAC2I,MAAM,KAAK,CAAC,EAAE;QAC9B,IAAI,CAAC9I,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,uCAAuC,CACxC;QACD,IAAI,CAAClJ,sBAAsB,CAACE,oBAAoB,GAAG,KAAK;;MAG1D;MACA,MAAM2K,qBAAqB,GAAGjN,aAAa,CAACvD,MAAM,CAC/CyQ,KAAK,IAAK,CAACR,sBAAsB,CAACS,GAAG,CAACD,KAAK,CAAC3J,EAAE,CAAC,CACjD;MAED,IAAI,CAAC1D,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,gBACErI,aAAa,CAAC2I,MAAM,GAAGsE,qBAAqB,CAACtE,MAC/C,wBAAwB,CACzB;MAED;MACAsE,qBAAqB,CAAC5J,OAAO,CAAC,CAAC6J,KAAK,EAAEE,KAAK,KAAI;QAC7CtH,OAAO,CAACC,GAAG,CAAC,gBAAgBqH,KAAK,GAAG,CAAC,UAAUnE,IAAI,IAAI,EAAE;UACvD1F,EAAE,EAAE2J,KAAK,CAAC3J,EAAE,IAAK2J,KAAa,CAACZ,GAAG;UAClCjF,IAAI,EAAE6F,KAAK,CAAC7F,IAAI;UAChBgG,OAAO,EAAEH,KAAK,CAACG,OAAO;UACtBC,MAAM,EAAEJ,KAAK,CAACI;SACf,CAAC;MACJ,CAAC,CAAC;MAEF;MACA;MACA,IAAI,CAACC,WAAW,CAACN,qBAAqB,CAAC;MAEvC;MACA,MAAMO,mBAAmB,GAAG9J,KAAK,CAACC,IAAI,CACpC,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,EAAE,CAChC;MAED;MACA,MAAM6J,mBAAmB,GACvB,IAAI,CAACC,uBAAuB,CAACF,mBAAmB,CAAC;MAEnD1H,OAAO,CAACC,GAAG,CACT,cAAc0H,mBAAmB,CAAC9E,MAAM,kDAAkD,CAC3F;MAED;MACA,IAAI,CAAC3I,aAAa,CAACyD,IAAI,CAACgK,mBAAmB,CAAC;MAE5C;MACA,IAAI,CAAC5J,iBAAiB,EAAE;MAExB;MACA,IAAI,CAAC8J,+BAA+B,EAAE;MAEtC,OAAOH,mBAAmB;IAC5B,CAAC,CAAC,EACFjR,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MAED,IAAIA,KAAK,CAAC8J,aAAa,EAAE;QACvB,IAAI,CAAC/N,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAAC8J,aAAa,CACpB;;MAGH,IAAI9J,KAAK,CAAC+J,YAAY,EAAE;QACtB,IAAI,CAAChO,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAAC+J,YAAY,CACnB;;MAGH,OAAO1R,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKQ+D,yBAAyBA,CAAA;IAC/B,IAAI;MACF,MAAMmB,UAAU,GAAG,IAAIC,GAAG,EAAU;MACpC,MAAMhL,kBAAkB,GAAGC,YAAY,CAACC,OAAO,CAAC,eAAe,CAAC;MAEhE;MACA,IAAI,CAACF,kBAAkB,EAAE;QACvB,OAAO+K,UAAU;;MAGnB;MACA,MAAME,oBAAoB,GAAG,IAAID,GAAG,CAClC7K,IAAI,CAACC,KAAK,CAACJ,kBAAkB,CAAC,CAACzG,GAAG,CAAE2R,CAAe,IAAKA,CAAC,CAAC1K,EAAE,CAAC,CAC9D;MAED;MACA,MAAM2K,mBAAmB,GACvB,IAAI,CAACtO,MAAM,CAACuO,MAAM,CAACC,SAAS,CAA+B;QACzD/J,KAAK,EAAEvH;OACR,CAAC,EAAEkQ,oBAAoB,IAAI,EAAE;MAEhC;MACAkB,mBAAmB,CAAC7K,OAAO,CAAEC,YAAY,IAAI;QAC3C,IAAI,CAAC0K,oBAAoB,CAACb,GAAG,CAAC7J,YAAY,CAACC,EAAE,CAAC,EAAE;UAC9CuK,UAAU,CAACO,GAAG,CAAC/K,YAAY,CAACC,EAAE,CAAC;;MAEnC,CAAC,CAAC;MAEF,OAAOuK,UAAU;KAClB,CAAC,OAAOhK,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,qEAAqE,EACrEA,KAAK,CACN;MACD,OAAO,IAAIiK,GAAG,EAAU;;EAE5B;EAEA;EACAzL,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACF,sBAAsB,CAACE,oBAAoB;EACzD;EAEA;EACAgM,qBAAqBA,CAAA;IACnB,MAAMC,QAAQ,GAAG,IAAI,CAACnM,sBAAsB,CAACT,WAAW,GAAG,CAAC;IAC5D,OAAO,IAAI,CAAC6K,gBAAgB,CAC1B,KAAK,EACL+B,QAAQ,EACR,IAAI,CAACnM,sBAAsB,CAACC,KAAK,CAClC;EACH;EACAmM,mBAAmBA,CAACjL,EAAU;IAC5B,OAAO,IAAI,CAACxB,cAAc,CAACuC,IAAI,CAC7BhI,GAAG,CAAE0D,aAAa,IAAKA,aAAa,CAACkM,IAAI,CAAE+B,CAAC,IAAKA,CAAC,CAAC1K,EAAE,KAAKA,EAAE,CAAC,CAAC,EAC9DhH,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;MACvD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACH;EACA6F,oBAAoBA,CAAA;IAClB,OAAO,IAAI,CAACzO,aAAa,CAAC0O,KAAK,EAAE/F,MAAM,IAAI,CAAC;EAC9C;EACAgG,0BAA0BA,CAACC,cAAsB;IAC/C,OAAO,IAAI,CAAChP,MAAM,CACfyE,KAAK,CAAkC;MACtCA,KAAK,EAAEzF,8BAA8B;MACrCsK,SAAS,EAAE;QAAE3F,EAAE,EAAEqL;MAAc,CAAE;MACjCrG,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAEoK,0BAA0B,IAAI,EAAE,CAAC,EAC9DpS,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,0CAA0C,EAAEA,KAAK,CAAC;MACpE,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,6BAA6B,CAAC,CAAC;IACnE,CAAC,CAAC,CACH;EACL;EACAiG,sBAAsBA,CAAA;IACpB,OAAO,IAAI,CAAC9M,cAAc,CAACuC,IAAI,CAC7BhI,GAAG,CAAE0D,aAAa,IAAKA,aAAa,CAACvD,MAAM,CAAEwR,CAAC,IAAK,CAACA,CAAC,CAACX,MAAM,CAAC,CAAC,CAC/D;EACH;EAEA;;;;;EAKAwB,kBAAkBA,CAChBF,cAAsB;IAEtB,IAAI,CAAC/O,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,kCAAkCuG,cAAc,EAAE,CACnD;IAED,IAAI,CAACA,cAAc,EAAE;MACnB,IAAI,CAAC/O,MAAM,CAACkP,IAAI,CAAC,gBAAgB,EAAE,6BAA6B,CAAC;MACjE,OAAO5S,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,6BAA6B,CAAC,CAAC;;IAGnE;IACA,MAAMoG,YAAY,GAAG,IAAI,CAACC,4BAA4B,CAAC,CAACL,cAAc,CAAC,CAAC;IAExE;IACA,OAAO,IAAI,CAAChP,MAAM,CACf6J,MAAM,CAAgE;MACrEC,QAAQ,EAAE1K,4BAA4B;MACtCkK,SAAS,EAAE;QAAE0F;MAAc;KAC5B,CAAC,CACDtK,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMyG,QAAQ,GAAGzG,MAAM,CAAClE,IAAI,EAAEuK,kBAAkB;MAChD,IAAI,CAACI,QAAQ,EAAE;QACb,MAAM,IAAItG,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7B6G,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF3S,UAAU,CAAEuH,KAAK,IACf,IAAI,CAACqL,mBAAmB,CAACrL,KAAK,EAAE,mCAAmC,EAAE;MACnEsL,OAAO,EAAE,IAAI;MACbvD,OAAO,EAAE;KACV,CAAC,CACH,CACF;EACL;EAEA;;;;EAIQ8B,+BAA+BA,CAAA;IACrC,IAAI;MACF,MAAM3N,aAAa,GAAG0D,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,EAAE,CAAC;MACjEZ,YAAY,CAACqM,OAAO,CAAC,eAAe,EAAEnM,IAAI,CAACoM,SAAS,CAACtP,aAAa,CAAC,CAAC;MACpE,IAAI,CAACH,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,uCAAuC,CACxC;KACF,CAAC,OAAOvE,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iDAAiD,EACjDA,KAAK,CACN;;EAEL;EAEA;;;;EAIAyL,sBAAsBA,CAAA;IAKpB,IAAI,CAAC1P,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,yCAAyC,CAC1C;IAED;IACA,MAAMmH,KAAK,GAAG,IAAI,CAACvP,iBAAiB,CAAC2M,IAAI;IACzC,MAAM6C,kBAAkB,GAAG/L,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAACyF,IAAI,EAAE,CAAC;IACpE,IAAI,CAACuJ,4BAA4B,CAACQ,kBAAkB,CAAC;IAErD;IACA,OAAO,IAAI,CAAC7P,MAAM,CACf6J,MAAM,CAMJ;MACDC,QAAQ,EAAExK;KACX,CAAC,CACDoF,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMyG,QAAQ,GAAGzG,MAAM,CAAClE,IAAI,EAAEgL,sBAAsB;MACpD,IAAI,CAACL,QAAQ,EAAE;QACb,MAAM,IAAItG,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,yDAAyD,EACzD6G,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF3S,UAAU,CAAEuH,KAAK,IACf,IAAI,CAACqL,mBAAmB,CACtBrL,KAAK,EACL,4CAA4C,EAC5C;MACEsL,OAAO,EAAE,IAAI;MACbI,KAAK;MACL3D,OAAO,EAAE,GAAG2D,KAAK;KAClB,CACF,CACF,CACF;EACL;EAEA;;;;;EAKAE,2BAA2BA,CACzBC,eAAyB;IAEzB,IAAI,CAAC9P,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,kBAAkBsH,eAAe,CAAChH,MAAM,gBAAgB,CACzD;IAED,IAAI,CAACgH,eAAe,IAAIA,eAAe,CAAChH,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAC9I,MAAM,CAACkP,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAO5S,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,iCAAiC,CAAC,CAAC;;IAGvE;IACA,MAAM4G,KAAK,GAAG,IAAI,CAACP,4BAA4B,CAACU,eAAe,CAAC;IAEhE;IACA,OAAO,IAAI,CAAC/P,MAAM,CACf6J,MAAM,CAMJ;MACDC,QAAQ,EAAEzK,sCAAsC;MAChDiK,SAAS,EAAE;QAAEyG;MAAe;KAC7B,CAAC,CACDrL,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMyG,QAAQ,GAAGzG,MAAM,CAAClE,IAAI,EAAEmL,2BAA2B;MACzD,IAAI,CAACR,QAAQ,EAAE;QACb,MAAM,IAAItG,KAAK,CAAC,iCAAiC,CAAC;;MAGpD,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtC6G,QAAQ,CACT;MAED,OAAOA,QAAQ;IACjB,CAAC,CAAC,EACF3S,UAAU,CAAEuH,KAAK,IACf,IAAI,CAACqL,mBAAmB,CACtBrL,KAAK,EACL,0CAA0C,EAC1C;MACEsL,OAAO,EAAEI,KAAK,GAAG,CAAC;MAClBA,KAAK;MACL3D,OAAO,EAAE,GAAG2D,KAAK;KAClB,CACF,CACF,CACF;EACL;EACAI,wBAAwBA,CAAA;IAGtB,OAAO,IAAI,CAAC7N,cAAc,CAACuC,IAAI,CAC7BhI,GAAG,CAAE0D,aAAa,IAAI;MACpB,MAAM6P,MAAM,GAAG,IAAI3P,GAAG,EAAoC;MAC1DF,aAAa,CAACqD,OAAO,CAAE6J,KAAK,IAAI;QAC9B,IAAI,CAAC2C,MAAM,CAAC1C,GAAG,CAACD,KAAK,CAAC7F,IAAI,CAAC,EAAE;UAC3BwI,MAAM,CAACrM,GAAG,CAAC0J,KAAK,CAAC7F,IAAI,EAAE,EAAE,CAAC;;QAE5BwI,MAAM,CAACC,GAAG,CAAC5C,KAAK,CAAC7F,IAAI,CAAC,EAAE0I,IAAI,CAAC7C,KAAK,CAAC;MACrC,CAAC,CAAC;MACF,OAAO2C,MAAM;IACf,CAAC,CAAC,CACH;EACH;EACAG,UAAUA,CAACL,eAAyB;IAKlC,IAAI,CAAC9P,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,kCAAkCsH,eAAe,EAAE5C,IAAI,CAAC,IAAI,CAAC,IAAI,MAAM,EAAE,CAC1E;IAED,IAAI,CAAC4C,eAAe,IAAIA,eAAe,CAAChH,MAAM,KAAK,CAAC,EAAE;MACpD,IAAI,CAAC9I,MAAM,CAACkP,IAAI,CAAC,gBAAgB,EAAE,8BAA8B,CAAC;MAClE,OAAO7S,EAAE,CAAC;QACRkT,OAAO,EAAE,KAAK;QACda,SAAS,EAAE,CAAC;QACZC,cAAc,EAAE,IAAI,CAAC/P,iBAAiB,CAACuO;OACxC,CAAC;;IAGJ;IACA,MAAMyB,QAAQ,GAAGR,eAAe,CAAClT,MAAM,CACpC8G,EAAE,IAAKA,EAAE,IAAI,OAAOA,EAAE,KAAK,QAAQ,IAAIA,EAAE,CAAC6M,IAAI,EAAE,KAAK,EAAE,CACzD;IAED,IAAID,QAAQ,CAACxH,MAAM,KAAKgH,eAAe,CAAChH,MAAM,EAAE;MAC9C,IAAI,CAAC9I,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,EAAE;QACvEuM,QAAQ,EAAEV,eAAe;QACzBW,KAAK,EAAEH;OACR,CAAC;MACF,OAAOhU,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,mCAAmC,CAAC,CAAC;;IAGzE,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,gDAAgD,EAChD8H,QAAQ,CACT;IAED;IACA,IAAI,CAACI,wBAAwB,CAACJ,QAAQ,EAAE,IAAI,CAAC;IAE7C;IACA,MAAMK,kBAAkB,GAAG;MACzBC,uBAAuB,EAAE;QACvBrB,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAACxH,MAAM;QAC1BuH,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACxQ,iBAAiB,CAACuO,KAAK,GAAGyB,QAAQ,CAACxH,MAAM;;KAGnD;IAED;IACA7C,OAAO,CAACC,GAAG,CAAC,0DAA0D,EAAE;MACtE4J,eAAe,EAAEQ;KAClB,CAAC;IACFrK,OAAO,CAACC,GAAG,CAAC,iBAAiB,EAAElH,+BAA+B,CAAC;IAE/D,OAAO,IAAI,CAACe,MAAM,CACf6J,MAAM,CAAkC;MACvCC,QAAQ,EAAE7K,+BAA+B;MACzCqK,SAAS,EAAE;QAAEyG,eAAe,EAAEQ;MAAQ,CAAE;MACxCK,kBAAkB,EAAEA,kBAAkB;MACtChF,WAAW,EAAE,KAAK;MAClBjD,WAAW,EAAE,UAAU,CAAE;KAC1B,CAAC,CACDjE,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,iBAAiB,EAAEI,MAAM,CAAC;MAC9D3C,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE0C,MAAM,CAAC;MAEvC;MACA,IAAIA,MAAM,CAACoE,MAAM,EAAE;QACjB,IAAI,CAAChN,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjB2E,MAAM,CAACoE,MAAM,CACd;QACD/G,OAAO,CAAChC,KAAK,CAAC,iBAAiB,EAAE2E,MAAM,CAACoE,MAAM,CAAC;;MAGjD;MACA,MAAMqC,QAAQ,GACZzG,MAAM,CAAClE,IAAI,EAAEkM,uBAAuB,IACpCD,kBAAkB,CAACC,uBAAuB;MAE5C,OAAOvB,QAAQ;IACjB,CAAC,CAAC,EACF3S,UAAU,CAAEuH,KAAY,IAAI;MAC1B,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACDgC,OAAO,CAAChC,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;MAE5C;MACA;MACA,OAAO5H,EAAE,CAAC;QACRkT,OAAO,EAAE,IAAI;QACba,SAAS,EAAEE,QAAQ,CAACxH,MAAM;QAC1BuH,cAAc,EAAEQ,IAAI,CAACC,GAAG,CACtB,CAAC,EACD,IAAI,CAACxQ,iBAAiB,CAACuO,KAAK,GAAGyB,QAAQ,CAACxH,MAAM;OAEjD,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EACA;EACA;EACA;EAEA;;;;;EAKAiI,sBAAsBA,CAACC,MAAc;IACnC,OAAO,IAAI,CAACjR,MAAM,CACfqE,SAAS,CAA6B;MACrCI,KAAK,EAAE9E,wBAAwB;MAC/B2J,SAAS,EAAE;QAAE2H;MAAM;KACpB,CAAC,CACDvM,IAAI,CACHhI,GAAG,CAAC,CAAC;MAAEiI;IAAI,CAAE,KAAI;MACf,IAAI,CAACA,IAAI,EAAEuM,UAAU,EAAE;QACrB,MAAM,IAAIlI,KAAK,CAAC,yBAAyB,CAAC;;MAE5C,OAAOrE,IAAI,CAACuM,UAAU;IACxB,CAAC,CAAC,EACFtU,GAAG,CAAEuU,MAAM,IAAI;MACb,IAAI,CAACrQ,WAAW,CAAC+C,IAAI,CAACsN,MAAM,CAAC;MAC7B,IAAI,CAACC,gBAAgB,CAACD,MAAM,CAAC;IAC/B,CAAC,CAAC,EACFxU,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,mCAAmC,EAAEA,KAAK,CAAC;MAC7D,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;EAOAqI,cAAcA,CACZJ,MAAc,EACdK,UAAkB,EAClBC,UAAkB;IAElB,OAAO,IAAI,CAACvR,MAAM,CACf6J,MAAM,CAAkC;MACvCC,QAAQ,EAAEpK,yBAAyB;MACnC4J,SAAS,EAAE;QACT2H,MAAM;QACNK,UAAU;QACVC;;KAEH,CAAC,CACD7M,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAM2G,OAAO,GAAG3G,MAAM,CAAClE,IAAI,EAAE0M,cAAc;MAC3C,IAAI,CAAC7B,OAAO,EAAE;QACZ,MAAM,IAAIxG,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAOwG,OAAO;IAChB,CAAC,CAAC,EACF7S,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;MACrD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;;;;;;;EAUAwI,cAAcA,CACZ/O,KAAA,GAAgB,EAAE,EAClBkJ,MAAA,GAAiB,CAAC,EAClB8F,MAAiB,EACjBhK,IAAe,EACfiK,SAAyB,EACzBC,OAAuB;IAEvB,OAAO,IAAI,CAAC3R,MAAM,CACf0I,UAAU,CAA0B;MACnCjE,KAAK,EAAElF,kBAAkB;MACzB+J,SAAS,EAAE;QACT7G,KAAK;QACLkJ,MAAM;QACN8F,MAAM;QACNhK,IAAI;QACJiK,SAAS;QACTC;OACD;MACDhJ,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAM+I,OAAO,GAAG/I,MAAM,CAAClE,IAAI,EAAEkN,WAAW,IAAI,EAAE;MAC9C,IAAI,CAAC5R,MAAM,CAACwI,KAAK,CAAC,aAAamJ,OAAO,CAAC7I,MAAM,qBAAqB,CAAC;MACnE,OAAO6I,OAAO;IAChB,CAAC,CAAC,EACFjV,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;;EAKA8I,cAAcA,CAACb,MAAc;IAC3B,OAAO,IAAI,CAACjR,MAAM,CACf0I,UAAU,CAAwB;MACjCjE,KAAK,EAAEjF,kBAAkB;MACzB8J,SAAS,EAAE;QAAE2H;MAAM,CAAE;MACrBtI,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMkJ,OAAO,GAAGlJ,MAAM,CAAClE,IAAI,EAAEqN,WAAW;MACxC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAI/I,KAAK,CAAC,wBAAwB,CAAC;;MAE3C,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CAAC,+BAA+BwI,MAAM,EAAE,CAAC;MAC1D,OAAOc,OAAO;IAChB,CAAC,CAAC,EACFpV,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,8BAA8B,EAAEA,KAAK,CAAC;MACxD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIAiJ,YAAYA,CAAA;IACV,OAAO,IAAI,CAACjS,MAAM,CACf0I,UAAU,CAAqB;MAC9BjE,KAAK,EAAEhF,gBAAgB;MACvBkJ,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMqJ,KAAK,GAAGrJ,MAAM,CAAClE,IAAI,EAAEwN,SAAS;MACpC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAIlJ,KAAK,CAAC,sBAAsB,CAAC;;MAEzC,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CAAC,uBAAuB,EAAEyJ,KAAK,CAAC;MACjD,OAAOA,KAAK;IACd,CAAC,CAAC,EACFvV,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;MACtD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;;;;EAIQoI,gBAAgBA,CAACD,MAAkB;IACzC,QAAQA,MAAM,CAAC1J,IAAI;MACjB,KAAK,eAAe;QAClB,IAAI,CAAC2K,kBAAkB,CAACjB,MAAM,CAAC;QAC/B;MACF,KAAK,QAAQ;QACX,IAAI,CAACkB,YAAY,CAAClB,MAAM,CAAC;QACzB;MACF,KAAK,UAAU;QACb,IAAI,CAACmB,aAAa,CAACnB,MAAM,CAAC;QAC1B;MACF,KAAK,QAAQ;QACX,IAAI,CAACoB,gBAAgB,CAACpB,MAAM,CAAC;QAC7B;MACF;QACE,IAAI,CAAClR,MAAM,CAACwI,KAAK,CAAC,0BAA0B0I,MAAM,CAAC1J,IAAI,EAAE,EAAE0J,MAAM,CAAC;;EAExE;EAEA;;;;EAIQiB,kBAAkBA,CAACjB,MAAkB;IAC3C,IAAI,CAAC,IAAI,CAAClQ,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACiE,KAAK,CAAC,gDAAgD,CAAC;MACnE;;IAGF,IAAI;MACF,MAAMsO,SAAS,GAAGlP,IAAI,CAACC,KAAK,CAAC4N,MAAM,CAACxM,IAAI,CAAC;MACzC,IAAI,CAAC1D,cAAc,CAChBwR,eAAe,CAAC,IAAIC,eAAe,CAACF,SAAS,CAAC,CAAC,CAC/C/M,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MACjE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,6BAA6B,EAAEA,KAAc,CAAC;;EAEpE;EAEA;;;;EAIQmO,YAAYA,CAAClB,MAAkB;IACrC,IAAI,CAAC,IAAI,CAAClQ,cAAc,EAAE;MACxB,IAAI,CAAChB,MAAM,CAACiE,KAAK,CAAC,yCAAyC,CAAC;MAC5D;;IAGF,IAAI;MACF,MAAMyO,MAAM,GAAGrP,IAAI,CAACC,KAAK,CAAC4N,MAAM,CAACxM,IAAI,CAAC;MACtC,IAAI,CAAC1D,cAAc,CAChB2R,oBAAoB,CAAC,IAAIC,qBAAqB,CAACF,MAAM,CAAC,CAAC,CACvDlN,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,kCAAkC,EAAEA,KAAc,CAAC;MACvE,CAAC,CAAC;KACL,CAAC,OAAOA,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,sBAAsB,EAAEA,KAAc,CAAC;;EAE7D;EAEA;;;;EAIQoO,aAAaA,CAACnB,MAAkB;IACtC,IAAI,CAACzL,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACoN,WAAW,EAAE;IAElB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACnS,UAAU,CAACkO,KAAK;IACzC,IAAIiE,WAAW,IAAIA,WAAW,CAACpP,EAAE,KAAKwN,MAAM,CAACF,MAAM,EAAE;MACnD,IAAI,CAACrQ,UAAU,CAACiD,IAAI,CAAC;QACnB,GAAGkP,WAAW;QACdtB,MAAM,EAAEzU,UAAU,CAACgW,KAAK;QACxBC,OAAO,EAAE,IAAI/I,IAAI,EAAE,CAACpH,WAAW;OAChC,CAAC;;EAEN;EAEA;;;;EAIQyP,gBAAgBA,CAACpB,MAAkB;IACzC,IAAI,CAACzL,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACoN,WAAW,EAAE;IAElB;IACA,MAAMC,WAAW,GAAG,IAAI,CAACnS,UAAU,CAACkO,KAAK;IACzC,IAAIiE,WAAW,IAAIA,WAAW,CAACpP,EAAE,KAAKwN,MAAM,CAACF,MAAM,EAAE;MACnD,IAAI,CAACrQ,UAAU,CAACiD,IAAI,CAAC;QACnB,GAAGkP,WAAW;QACdtB,MAAM,EAAEzU,UAAU,CAACkW,QAAQ;QAC3BD,OAAO,EAAE,IAAI/I,IAAI,EAAE,CAACpH,WAAW;OAChC,CAAC;;EAEN;EAEA;;;EAGQgQ,WAAWA,CAAA;IACjB,IAAI,IAAI,CAAC/R,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACoS,SAAS,EAAE,CAAC1P,OAAO,CAAE2P,KAAK,IAAKA,KAAK,CAAC1N,IAAI,EAAE,CAAC;MAC7D,IAAI,CAAC3E,WAAW,GAAG,IAAI;MACvB,IAAI,CAACO,YAAY,CAACuC,IAAI,CAAC,IAAI,CAAC;;IAG9B,IAAI,IAAI,CAAC5C,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAACoS,KAAK,EAAE;MAC3B,IAAI,CAACpS,cAAc,GAAG,IAAI;;IAG5B,IAAI,CAACD,YAAY,GAAG,IAAI;IACxB,IAAI,CAACO,aAAa,CAACsC,IAAI,CAAC,IAAI,CAAC;EAC/B;EAEA;;;;;EAKQyP,iBAAiBA,CAACC,QAAkB;IAC1C,MAAMC,WAAW,GAA2B;MAC1CtO,KAAK,EAAE,IAAI;MACXuO,KAAK,EACHF,QAAQ,KAAKxW,QAAQ,CAAC2W,KAAK,GACvB;QACEC,KAAK,EAAE;UAAEC,KAAK,EAAE;QAAI,CAAE;QACtBC,MAAM,EAAE;UAAED,KAAK,EAAE;QAAG;OACrB,GACD;KACP;IAED,OAAO,IAAIvX,UAAU,CAAeyX,QAAQ,IAAI;MAC9CC,SAAS,CAACC,YAAY,CACnBC,YAAY,CAACT,WAAW,CAAC,CACzBU,IAAI,CAAEC,MAAM,IAAI;QACfL,QAAQ,CAACjQ,IAAI,CAACsQ,MAAM,CAAC;QACrBL,QAAQ,CAACM,QAAQ,EAAE;MACrB,CAAC,CAAC,CACD3O,KAAK,CAAEvB,KAAK,IAAI;QACf,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,+BAA+B,EAAEA,KAAK,CAAC;QACzD4P,QAAQ,CAAC5P,KAAK,CAAC,IAAI8E,KAAK,CAAC,gCAAgC,CAAC,CAAC;MAC7D,CAAC,CAAC;IACN,CAAC,CAAC;EACJ;EAEA;;;;EAIQqL,cAAcA,CAAA;IACpB,OAAOnK,IAAI,CAACoK,GAAG,EAAE,CAACC,QAAQ,EAAE,GAAGzD,IAAI,CAAC0D,MAAM,EAAE,CAACD,QAAQ,CAAC,EAAE,CAAC,CAACE,SAAS,CAAC,CAAC,EAAE,CAAC,CAAC;EAC3E;EAEA;EACA;EACA;EACA;EACAC,WAAWA,CACTC,YAAY,GAAG,KAAK,EACpBC,MAAe,EACfvL,IAAA,GAAe,CAAC,EAChB5G,KAAA,GAAgB,EAAE,EAClBoS,MAAA,GAAiB,UAAU,EAC3BC,SAAA,GAAoB,KAAK,EACzBC,QAAkB;IAElB,IAAI,CAAC9U,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,2CAA2CiJ,YAAY,YACrDC,MAAM,IAAI,SACZ,UAAUvL,IAAI,WAAW5G,KAAK,YAAYoS,MAAM,eAAeC,SAAS,cAAcC,QAAQ,EAAE,CACjG;IAED,MAAMT,GAAG,GAAGpK,IAAI,CAACoK,GAAG,EAAE;IACtB,MAAMU,UAAU,GACd,CAACL,YAAY,IACb,IAAI,CAAChT,UAAU,CAACoH,MAAM,GAAG,CAAC,IAC1BuL,GAAG,GAAG,IAAI,CAAC3T,aAAa,IAAI,IAAI,CAACD,cAAc,IAC/C,CAACkU,MAAM,IACPvL,IAAI,KAAK,CAAC,IACV5G,KAAK,IAAI,IAAI,CAACd,UAAU,CAACoH,MAAM;IAEjC;IACA,IAAIiM,UAAU,EAAE;MACd,IAAI,CAAC/U,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,uBAAuB,IAAI,CAAC9G,UAAU,CAACoH,MAAM,SAAS,CACvD;MACD,OAAOzM,EAAE,CAAC,CAAC,GAAG,IAAI,CAACqF,UAAU,CAAC,CAAC;;IAGjC,IAAI,CAAC1B,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,2DACEkM,YAAY,GAAG,cAAc,GAAG,aAClC,EAAE,CACH;IAED,OAAO,IAAI,CAAC3U,MAAM,CACf0I,UAAU,CAAM;MACfjE,KAAK,EAAE/G,kBAAkB;MACzB4L,SAAS,EAAE;QACTsL,MAAM;QACNvL,IAAI;QACJ5G,KAAK;QACLoS,MAAM;QACNC,SAAS;QACTC,QAAQ,EAAEA,QAAQ,KAAKlS,SAAS,GAAGkS,QAAQ,GAAG;OAC/C;MACDpM,WAAW,EAAEgM,YAAY,GAAG,cAAc,GAAG;KAC9C,CAAC,CACD/L,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,yBAAyB,EACzBI,MAAM,CACP;MAED,IAAIA,MAAM,CAACoE,MAAM,EAAE;QACjB,IAAI,CAAChN,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChC2E,MAAM,CAACoE,MAAM,CACd;QACD,MAAM,IAAIjE,KAAK,CAACH,MAAM,CAACoE,MAAM,CAACvQ,GAAG,CAAEwQ,CAAC,IAAKA,CAAC,CAACjB,OAAO,CAAC,CAACkB,IAAI,CAAC,IAAI,CAAC,CAAC;;MAGjE,IAAI,CAACtE,MAAM,CAAClE,IAAI,EAAE+P,WAAW,EAAE;QAC7B,IAAI,CAACzU,MAAM,CAACkP,IAAI,CACd,gBAAgB,EAChB,oCAAoC,CACrC;QACD,OAAO,EAAE;;MAGX,MAAM8F,iBAAiB,GAAGpM,MAAM,CAAClE,IAAI,CAAC+P,WAAW;MAEjD;MACA,IAAI,CAACzU,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAE;QAC1D5G,UAAU,EAAEoT,iBAAiB,CAACpT,UAAU;QACxCC,UAAU,EAAEmT,iBAAiB,CAACnT,UAAU;QACxCC,WAAW,EAAEkT,iBAAiB,CAAClT,WAAW;QAC1CC,WAAW,EAAEiT,iBAAiB,CAACjT,WAAW;QAC1CC,eAAe,EAAEgT,iBAAiB,CAAChT;OACpC,CAAC;MAEF;MACA,MAAMiT,KAAK,GAAW,EAAE;MACxB,KAAK,MAAMC,IAAI,IAAIF,iBAAiB,CAACC,KAAK,EAAE;QAC1C,IAAI;UACF,IAAIC,IAAI,EAAE;YACRD,KAAK,CAAC/E,IAAI,CAAC,IAAI,CAAClF,aAAa,CAACkK,IAAI,CAAC,CAAC;;SAEvC,CAAC,OAAOjR,KAAK,EAAE;UACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,gBAAgB,EAChB,mCAAmC,EACnCjL,KAAK,CACN;;;MAIL,IAAI,CAACjE,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,YAAYwJ,KAAK,CAACnM,MAAM,4BAA4BkM,iBAAiB,CAAClT,WAAW,OAAOkT,iBAAiB,CAACnT,UAAU,GAAG,CACxH;MAED;MACA,IAAI,CAAC8S,MAAM,IAAIvL,IAAI,KAAK,CAAC,IAAI,CAAC0L,QAAQ,EAAE;QACtC,IAAI,CAACpT,UAAU,GAAG,CAAC,GAAGuT,KAAK,CAAC;QAC5B,IAAI,CAACvU,aAAa,GAAGuJ,IAAI,CAACoK,GAAG,EAAE;QAC/B,IAAI,CAACrU,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,2BAA2ByM,KAAK,CAACnM,MAAM,QAAQ,CAChD;;MAGH;MACA,IAAI,CAACnH,qBAAqB,GAAG;QAC3BC,UAAU,EAAEoT,iBAAiB,CAACpT,UAAU;QACxCC,UAAU,EAAEmT,iBAAiB,CAACnT,UAAU;QACxCC,WAAW,EAAEkT,iBAAiB,CAAClT,WAAW;QAC1CC,WAAW,EAAEiT,iBAAiB,CAACjT,WAAW;QAC1CC,eAAe,EAAEgT,iBAAiB,CAAChT;OACpC;MAED,OAAOiT,KAAK;IACd,CAAC,CAAC,EACFvY,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAEnE,IAAIA,KAAK,CAAC8J,aAAa,EAAE;QACvB,IAAI,CAAC/N,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iBAAiB,EACjBA,KAAK,CAAC8J,aAAa,CACpB;;MAGH,IAAI9J,KAAK,CAAC+J,YAAY,EAAE;QACtB,IAAI,CAAChO,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,gBAAgB,EAChBA,KAAK,CAAC+J,YAAY,CACnB;;MAGH;MACA,IACE,IAAI,CAACtM,UAAU,CAACoH,MAAM,GAAG,CAAC,IAC1BM,IAAI,KAAK,CAAC,IACV,CAACuL,MAAM,IACP,CAACG,QAAQ,EACT;QACA,IAAI,CAAC9U,MAAM,CAACkP,IAAI,CACd,gBAAgB,EAChB,aAAa,IAAI,CAACxN,UAAU,CAACoH,MAAM,kCAAkC,CACtE;QACD,OAAOzM,EAAE,CAAC,CAAC,GAAG,IAAI,CAACqF,UAAU,CAAC,CAAC;;MAGjC,OAAOpF,UAAU,CACf,MACE,IAAIyM,KAAK,CACP,0BAA0B9E,KAAK,CAAC+H,OAAO,IAAI,eAAe,EAAE,CAC7D,CACJ;IACH,CAAC,CAAC,CACH;EACL;EACAmJ,UAAUA,CAACjK,MAAc;IACvB,OAAO,IAAI,CAACnL,MAAM,CACf0I,UAAU,CAAqB;MAC9BjE,KAAK,EAAEhH,cAAc;MACrB6L,SAAS,EAAE;QAAE3F,EAAE,EAAEwH;MAAM,CAAE;MACzBxC,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAK,IAAI,CAACoC,aAAa,CAACpC,MAAM,CAAClE,IAAI,EAAEyQ,UAAU,CAAC,CAAC,EAC5DzY,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,sBAAsB,CAAC,CAAC;IAC5D,CAAC,CAAC,CACH;EACL;EACAqM,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACrV,MAAM,CACf0I,UAAU,CAAyB;MAClCjE,KAAK,EAAEtG,sBAAsB;MAC7BwK,WAAW,EAAE;KACd,CAAC,CACDC,YAAY,CAAClE,IAAI,CAChBhI,GAAG,CAAEmM,MAAM,IAAK,IAAI,CAACoC,aAAa,CAACpC,MAAM,CAAClE,IAAI,EAAE0Q,cAAc,CAAC,CAAC,EAChE1Y,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,8BAA8B,EAC9BA,KAAK,CACN;MACD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EACAsM,aAAaA,CAACnK,MAAc;IAC1B,OAAO,IAAI,CAACnL,MAAM,CACf6J,MAAM,CAAwB;MAC7BC,QAAQ,EAAEhM,wBAAwB;MAClCwL,SAAS,EAAE;QAAE6B;MAAM;KACpB,CAAC,CACDzG,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE2Q,aAAa,EAC7B,MAAM,IAAItM,KAAK,CAAC,2BAA2B,CAAC;MAC9C,OAAO,IAAI,CAACiC,aAAa,CAACpC,MAAM,CAAClE,IAAI,CAAC2Q,aAAa,CAAC;IACtD,CAAC,CAAC,EACF3Y,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,2BAA2B,CAAC,CAAC;IACjE,CAAC,CAAC,CACH;EACL;EACAuM,cAAcA,CAACpK,MAAc;IAC3B,OAAO,IAAI,CAACnL,MAAM,CACf6J,MAAM,CAAyB;MAC9BC,QAAQ,EAAE/L,yBAAyB;MACnCuL,SAAS,EAAE;QAAE6B;MAAM;KACpB,CAAC,CACDzG,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE4Q,cAAc,EAC9B,MAAM,IAAIvM,KAAK,CAAC,4BAA4B,CAAC;MAC/C,OAAO,IAAI,CAACiC,aAAa,CAACpC,MAAM,CAAClE,IAAI,CAAC4Q,cAAc,CAAC;IACvD,CAAC,CAAC,EACF5Y,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAwM,WAAWA,CACTxQ,IAAY,EACZwH,cAAwB,EACxBiJ,KAAY,EACZC,WAAoB;IAEpB,IAAI,CAACzV,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,mBAAmBzD,IAAI,SAASwH,cAAc,CAACzD,MAAM,eAAe,CACrE;IAED,IAAI,CAAC/D,IAAI,IAAI,CAACwH,cAAc,IAAIA,cAAc,CAACzD,MAAM,KAAK,CAAC,EAAE;MAC3D,OAAOxM,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,sCAAsC,CAAC,CACxD;;IAGH,OAAO,IAAI,CAAChJ,MAAM,CACf6J,MAAM,CAAC;MACNC,QAAQ,EAAEvL,qBAAqB;MAC/B+K,SAAS,EAAE;QAAEtE,IAAI;QAAEwH,cAAc;QAAEiJ,KAAK;QAAEC;MAAW;KACtD,CAAC,CACDhR,IAAI,CACHhI,GAAG,CAAEmM,MAAW,IAAI;MAClB,MAAM8M,KAAK,GAAG9M,MAAM,CAAClE,IAAI,EAAE6Q,WAAW;MACtC,IAAI,CAACG,KAAK,EAAE;QACV,MAAM,IAAI3M,KAAK,CAAC,gCAAgC,CAAC;;MAEnD,IAAI,CAAC/I,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,+BAA+BiK,KAAK,CAAChS,EAAE,EAAE,CAC1C;MACD,OAAOgS,KAAK;IACd,CAAC,CAAC,EACFhZ,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,gCAAgC,CAAC,CAAC;IACtE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA4M,WAAWA,CAACC,OAAe,EAAEC,KAAU;IACrC,IAAI,CAAC7V,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,mBAAmBoN,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtZ,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChJ,MAAM,CACf6J,MAAM,CAAC;MACNC,QAAQ,EAAEtL,qBAAqB;MAC/B8K,SAAS,EAAE;QAAE3F,EAAE,EAAEkS,OAAO;QAAEC;MAAK;KAChC,CAAC,CACDpR,IAAI,CACHhI,GAAG,CAAEmM,MAAW,IAAI;MAClB,MAAM8M,KAAK,GAAG9M,MAAM,CAAClE,IAAI,EAAEiR,WAAW;MACtC,IAAI,CAACD,KAAK,EAAE;QACV,MAAM,IAAI3M,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC/I,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,+BAA+BiK,KAAK,CAAChS,EAAE,EAAE,CAC1C;MACD,OAAOgS,KAAK;IACd,CAAC,CAAC,EACFhZ,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGA+M,WAAWA,CACTF,OAAe;IAEf,IAAI,CAAC5V,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,mBAAmBoN,OAAO,EAAE,CAAC;IAEjE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtZ,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChJ,MAAM,CACf6J,MAAM,CAAC;MACNC,QAAQ,EAAErL,qBAAqB;MAC/B6K,SAAS,EAAE;QAAE3F,EAAE,EAAEkS;MAAO;KACzB,CAAC,CACDnR,IAAI,CACHhI,GAAG,CAAEmM,MAAW,IAAI;MAClB,MAAMyG,QAAQ,GAAGzG,MAAM,CAAClE,IAAI,EAAEoR,WAAW;MACzC,IAAI,CAACzG,QAAQ,EAAE;QACb,MAAM,IAAItG,KAAK,CAAC,mCAAmC,CAAC;;MAEtD,IAAI,CAAC/I,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,+BAA+BmK,OAAO,EAAE,CACzC;MACD,OAAOvG,QAAQ;IACjB,CAAC,CAAC,EACF3S,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MACnE,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,mCAAmC,CAAC,CACrD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAgN,UAAUA,CACRH,OAAe;IAEf,IAAI,CAAC5V,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,kBAAkBoN,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtZ,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChJ,MAAM,CACf6J,MAAM,CAAC;MACNC,QAAQ,EAAEpL,oBAAoB;MAC9B4K,SAAS,EAAE;QAAEuM;MAAO;KACrB,CAAC,CACDnR,IAAI,CACHhI,GAAG,CAAEmM,MAAW,IAAI;MAClB,MAAMyG,QAAQ,GAAGzG,MAAM,CAAClE,IAAI,EAAEqR,UAAU;MACxC,IAAI,CAAC1G,QAAQ,EAAE;QACb,MAAM,IAAItG,KAAK,CAAC,8BAA8B,CAAC;;MAEjD,IAAI,CAAC/I,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,4BAA4BmK,OAAO,EAAE,CACtC;MACD,OAAOvG,QAAQ;IACjB,CAAC,CAAC,EACF3S,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,8BAA8B,CAAC,CAAC;IACpE,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAiN,QAAQA,CAACJ,OAAe;IACtB,IAAI,CAAC5V,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,kBAAkBoN,OAAO,EAAE,CAAC;IAEhE,IAAI,CAACA,OAAO,EAAE;MACZ,OAAOtZ,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,qBAAqB,CAAC,CAAC;;IAG3D,OAAO,IAAI,CAAChJ,MAAM,CACfyE,KAAK,CAAC;MACLA,KAAK,EAAE9F,eAAe;MACtB2K,SAAS,EAAE;QAAE3F,EAAE,EAAEkS;MAAO,CAAE;MAC1BlN,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHhI,GAAG,CAAEmM,MAAW,IAAI;MAClB,MAAM8M,KAAK,GAAG9M,MAAM,CAAClE,IAAI,EAAEsR,QAAQ;MACnC,IAAI,CAACN,KAAK,EAAE;QACV,MAAM,IAAI3M,KAAK,CAAC,mBAAmB,CAAC;;MAEtC,IAAI,CAAC/I,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,iCAAiCmK,OAAO,EAAE,CAC3C;MACD,OAAOF,KAAK;IACd,CAAC,CAAC,EACFhZ,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,sBAAsB,EAAEA,KAAK,CAAC;MAClE,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,oCAAoC,CAAC,CACtD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAkN,aAAaA,CAAC/K,MAAc;IAC1B,IAAI,CAAClL,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,4BAA4B0C,MAAM,EAAE,CAAC;IAEzE,IAAI,CAACA,MAAM,EAAE;MACX,OAAO5O,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,4BAA4B,CAAC,CAAC;;IAGlE,OAAO,IAAI,CAAChJ,MAAM,CACfyE,KAAK,CAAC;MACLA,KAAK,EAAE7F,qBAAqB;MAC5B0K,SAAS,EAAE;QAAE6B;MAAM,CAAE;MACrBxC,WAAW,EAAE;KACd,CAAC,CACDjE,IAAI,CACHhI,GAAG,CAAEmM,MAAW,IAAI;MAClB,MAAMoH,MAAM,GAAGpH,MAAM,CAAClE,IAAI,EAAEuR,aAAa,IAAI,EAAE;MAC/C,IAAI,CAACjW,MAAM,CAACyL,IAAI,CACd,gBAAgB,EAChB,aAAauE,MAAM,CAAClH,MAAM,qBAAqBoC,MAAM,EAAE,CACxD;MACD,OAAO8E,MAAM;IACf,CAAC,CAAC,EACFtT,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BA,KAAK,CACN;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EACAmN,sBAAsBA,CAAC/M,cAAsB;IAC3ClD,OAAO,CAACC,GAAG,CACT,gEAAgEiD,cAAc,EAAE,CACjF;IAED;IACA,MAAMgN,UAAU,GAAG,IAAI,CAACC,YAAY,EAAE;IACtCnQ,OAAO,CAACC,GAAG,CAAC,sCAAsCiQ,UAAU,EAAE,CAAC;IAE/D,IAAI,CAACA,UAAU,EAAE;MACflQ,OAAO,CAACiJ,IAAI,CACV,+DAA+D,CAChE;MACD,IAAI,CAAClP,MAAM,CAACkP,IAAI,CACd,sEAAsE,CACvE;MACD,OAAO7S,EAAE,CAAC,IAA0B,CAAC;;IAGvC4J,OAAO,CAACC,GAAG,CACT,+EAA+EiD,cAAc,EAAE,CAChG;IACD,IAAI,CAACnJ,MAAM,CAACwI,KAAK,CACf,2EAA2EW,cAAc,EAAE,CAC5F;IAEDlD,OAAO,CAACC,GAAG,CAAC,wDAAwD,EAAE;MACpEiD;KACD,CAAC;IACFlD,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5C5I,yBAAyB,CAC1B;IACD2I,OAAO,CAACC,GAAG,CAAC,mCAAmC,EAAE;MAAEiD;IAAc,CAAE,CAAC;IAEpE,MAAMkN,IAAI,GAAG,IAAI,CAACtW,MAAM,CACrBqE,SAAS,CAA2B;MACnCI,KAAK,EAAElH,yBAAyB;MAChC+L,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACH9H,GAAG,CAAEiM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE0C,MAAM,CAAC;MAClE3C,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAE0C,MAAM,CAAClE,IAAI,CAAC;MAClDuB,OAAO,CAACC,GAAG,CACT,qCAAqC,EACrC0C,MAAM,CAAClE,IAAI,EAAE4R,WAAW,CACzB;IACH,CAAC,CAAC,EACF7Z,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMW,GAAG,GAAGX,MAAM,CAAClE,IAAI,EAAE4R,WAAW;MACpC,IAAI,CAAC/M,GAAG,EAAE;QACRtD,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;QACD,IAAI,CAAC5I,MAAM,CAACkP,IAAI,CAAC,gCAAgC,CAAC;QAClD,MAAM,IAAInG,KAAK,CAAC,6BAA6B,CAAC;;MAGhD,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,+CAA+C,EAC/Ce,GAAG,CACJ;MAED;MACA,IAAI,CAACA,GAAG,CAAC7F,EAAE,IAAI,CAAC6F,GAAG,CAACkD,GAAG,EAAE;QACvB,IAAI,CAACzM,MAAM,CAACkP,IAAI,CACd,oDAAoD,CACrD;QACD3F,GAAG,CAAC7F,EAAE,GAAG,QAAQuG,IAAI,CAACoK,GAAG,EAAE,EAAE;;MAG/B,IAAI;QACF;QACA,MAAMkC,iBAAiB,GAAG,IAAI,CAAC/M,gBAAgB,CAACD,GAAG,CAAC;QAEpD,IAAI,CAACvJ,MAAM,CAACwI,KAAK,CACf,4CAA4C,EAC5C+N,iBAAiB,CAClB;QAED;QACA,IACEA,iBAAiB,CAAC/O,IAAI,KAAK3K,WAAW,CAAC4W,KAAK,IAC5C8C,iBAAiB,CAAC/O,IAAI,KAAK3K,WAAW,CAAC2Z,aAAa,IACnDD,iBAAiB,CAACE,WAAW,IAC5BF,iBAAiB,CAACE,WAAW,CAACC,IAAI,CAC/BC,GAAG,IAAKA,GAAG,CAACnP,IAAI,KAAK,OAAO,CAC7B,EACJ;UACA,IAAI,CAACxH,MAAM,CAACwI,KAAK,CACf,iDAAiD,CAClD;;QAGH;QACA,IAAI,CAACvI,IAAI,CAAC2W,GAAG,CAAC,MAAK;UACjB,IAAI,CAAC5W,MAAM,CAACwI,KAAK,CACf,kDAAkD,CACnD;UACD,IAAI,CAACqO,gCAAgC,CACnC1N,cAAc,EACdoN,iBAAiB,CAClB;QACH,CAAC,CAAC;QAEF,OAAOA,iBAAiB;OACzB,CAAC,OAAO9P,GAAG,EAAE;QACZ,IAAI,CAACzG,MAAM,CAACiE,KAAK,CAAC,8BAA8B,EAAEwC,GAAG,CAAC;QAEtD;QACA,MAAMqQ,cAAc,GAAY;UAC9BpT,EAAE,EAAE6F,GAAG,CAAC7F,EAAE,IAAI6F,GAAG,CAACkD,GAAG,IAAI,QAAQxC,IAAI,CAACoK,GAAG,EAAE,EAAE;UAC7C7G,OAAO,EAAEjE,GAAG,CAACiE,OAAO,IAAI,EAAE;UAC1BhG,IAAI,EAAE+B,GAAG,CAAC/B,IAAI,IAAI3K,WAAW,CAACka,IAAI;UAClCzM,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACvB,GAAG,CAACe,SAAS,CAAC;UACvCmD,MAAM,EAAE,KAAK;UACb1C,MAAM,EAAExB,GAAG,CAACwB,MAAM,GACd,IAAI,CAACC,aAAa,CAACzB,GAAG,CAACwB,MAAM,CAAC,GAC9B;YACErH,EAAE,EAAE,IAAI,CAACyI,gBAAgB,EAAE;YAC3B6K,QAAQ,EAAE;;SAEjB;QAED,IAAI,CAAChX,MAAM,CAACwI,KAAK,CACf,sCAAsC,EACtCsO,cAAc,CACf;QACD,OAAOA,cAAc;;IAEzB,CAAC,CAAC,EACFpa,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,6BAA6B,EAC7BA,KAAK,CACN;MACD;MACA,OAAOzH,KAAK;IACd,CAAC,CAAC;IACF;IACAI,MAAM,CAAEoP,OAAO,IAAK,CAAC,CAACA,OAAO,CAAC;IAC9B;IACAzP,KAAK,CAAC,CAAC,CAAC,CACT;IAEH0J,OAAO,CAACC,GAAG,CAAC,+CAA+C,CAAC;IAE5D,MAAM+Q,GAAG,GAAGZ,IAAI,CAACjS,SAAS,CAAC;MACzBR,IAAI,EAAGoI,OAAO,IAAI;QAChB/F,OAAO,CAACC,GAAG,CAAC,6CAA6C,EAAE8F,OAAO,CAAC;QACnE;QACA,IAAI,CAAChM,MAAM,CAACwI,KAAK,CAAC,gBAAgB,EAAE,uBAAuB,EAAEwD,OAAO,CAAC;QAErE;QACA,IAAI,CAAC6K,gCAAgC,CAAC1N,cAAc,EAAE6C,OAAO,CAAC;MAChE,CAAC;MACD/H,KAAK,EAAGwC,GAAG,IAAI;QACbR,OAAO,CAAChC,KAAK,CAAC,8BAA8B,EAAEwC,GAAG,CAAC;QAClD,IAAI,CAACzG,MAAM,CAACiE,KAAK,CAAC,gCAAgC,EAAEwC,GAAG,CAAC;MAC1D,CAAC;MACD0N,QAAQ,EAAEA,CAAA,KAAK;QACblO,OAAO,CAACC,GAAG,CAAC,kCAAkC,CAAC;MACjD;KACD,CAAC;IAEF;IACAD,OAAO,CAACC,GAAG,CAAC,wCAAwC,EAAE+Q,GAAG,CAAC;IAC1DhR,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAE,IAAI,CAACnG,MAAM,CAAC;IAE1D,IAAI,CAACS,aAAa,CAAC0P,IAAI,CAAC+G,GAAG,CAAC;IAC5BhR,OAAO,CAACC,GAAG,CACT,2FAA2F,IAAI,CAAC1F,aAAa,CAACsI,MAAM,EAAE,CACvH;IACD,OAAOuN,IAAI;EACb;EAEA;;;;;EAKQQ,gCAAgCA,CACtC1N,cAAsB,EACtB6C,OAAgB;IAEhB,IAAI,CAAChM,MAAM,CAACwI,KAAK,CACf,oCAAoCW,cAAc,qBAAqB6C,OAAO,CAACtI,EAAE,EAAE,CACpF;IAED;IACA,IAAI,CAACzD,IAAI,CAAC2W,GAAG,CAAC,MAAK;MACjB;MACA,IAAI,CAAC1W,kBAAkB,CAAC0D,IAAI,CAACuF,cAAc,CAAC;MAE5C,IAAI,CAACnJ,MAAM,CAACwI,KAAK,CAAC,oDAAoD,CAAC;IACzE,CAAC,CAAC;IAEF;IACA0O,UAAU,CAAC,MAAK;MACd,IAAI,CAAC1L,eAAe,CAACrC,cAAc,CAAC,CAAC/E,SAAS,CAAC;QAC7CR,IAAI,EAAGmI,YAAY,IAAI;UACrB,IAAI,CAAC/L,MAAM,CAACwI,KAAK,CACf,8BAA8BW,cAAc,mBAC1C4C,YAAY,EAAEzC,QAAQ,EAAER,MAAM,IAAI,CACpC,WAAW,CACZ;QACH,CAAC;QACD7E,KAAK,EAAGA,KAAK,IAAI;UACf,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gDAAgDkF,cAAc,GAAG,EACjElF,KAAK,CACN;QACH;OACD,CAAC;IACJ,CAAC,EAAE,CAAC,CAAC,CAAC,CAAC;EACT;EAEA;;;EAGQkT,0BAA0BA,CAAA;IAChClR,OAAO,CAACC,GAAG,CAAC,wDAAwD,CAAC;IAErE;IACA,IAAI,CAACyG,gBAAgB,CAAC,IAAI,CAAC,CAACvI,SAAS,CAAC;MACpCR,IAAI,EAAGzD,aAAa,IAAI;QACtB8F,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD/F,aAAa,CAAC2I,MAAM,CACrB;MACH,CAAC;MACD7E,KAAK,EAAGA,KAAK,IAAI;QACfgC,OAAO,CAAChC,KAAK,CAAC,4CAA4C,EAAEA,KAAK,CAAC;MACpE;KACD,CAAC;EACJ;EAEAM,qBAAqBA,CAAA;IACnB;IACA,IAAI,CAAC,IAAI,CAAC6R,YAAY,EAAE,EAAE;MACxB,IAAI,CAACpW,MAAM,CAACkP,IAAI,CACd,+EAA+E,CAChF;MACD,OAAO5S,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,0BAA0B,CAAC,CAAC;;IAGhE,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CAAC,iDAAiD,CAAC;IAEpE,MAAM6N,IAAI,GAAG,IAAI,CAACtW,MAAM,CACrBqE,SAAS,CAA8B;MACtCI,KAAK,EAAEjH;KACR,CAAC,CACDkH,IAAI,CACH9H,GAAG,CAAEiM,MAAM,IACT,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CACf,uDAAuD,EACvDI,MAAM,CACP,CACF,EACDnM,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMsM,IAAI,GAAGtM,MAAM,CAAClE,IAAI,EAAE0S,iBAAiB;MAC3C,IAAI,CAAClC,IAAI,EAAE;QACT,IAAI,CAAClV,MAAM,CAACiE,KAAK,CAAC,4BAA4B,CAAC;QAC/C,MAAM,IAAI8E,KAAK,CAAC,4BAA4B,CAAC;;MAE/C,OAAO,IAAI,CAACiC,aAAa,CAACkK,IAAI,CAAC;IACjC,CAAC,CAAC,EACFxY,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,4BAA4B,EAAEA,KAAc,CAAC;MAC/D,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,4BAA4B,CAAC,CAAC;IAClE,CAAC,CAAC,EACFxM,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAM0a,GAAG,GAAGZ,IAAI,CAACjS,SAAS,EAAE;IAC5B,IAAI,CAAC5D,aAAa,CAAC0P,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAgB,8BAA8BA,CAC5BlO,cAAsB;IAEtB,MAAMkN,IAAI,GAAG,IAAI,CAACtW,MAAM,CACrBqE,SAAS,CAAwC;MAChDI,KAAK,EAAE9G,iCAAiC;MACxC2L,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAM0C,IAAI,GAAG1C,MAAM,CAAClE,IAAI,EAAE4S,mBAAmB;MAC7C,IAAI,CAAChM,IAAI,EAAE,MAAM,IAAIvC,KAAK,CAAC,kCAAkC,CAAC;MAE9D,MAAM6C,sBAAsB,GAAiB;QAC3C,GAAGN,IAAI;QACPO,YAAY,EACVP,IAAI,CAACO,YAAY,EAAEpP,GAAG,CAAE+P,CAAC,IAAK,IAAI,CAACxB,aAAa,CAACwB,CAAC,CAAC,CAAC,IAAI,EAAE;QAC5D+K,WAAW,EAAEjM,IAAI,CAACiM,WAAW,GACzB;UACE,GAAGjM,IAAI,CAACiM,WAAW;UACnBxM,MAAM,EAAE,IAAI,CAACC,aAAa,CAACM,IAAI,CAACiM,WAAW,CAACxM,MAAM,CAAC;UACnDT,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACQ,IAAI,CAACiM,WAAW,CAACjN,SAAS,CAAC;UACpDN,MAAM,EAAEsB,IAAI,CAACiM,WAAW,CAACvN,MAAM,GAC3B,IAAI,CAACc,QAAQ,CAACQ,IAAI,CAACiM,WAAW,CAACvN,MAAM,CAAC,GACtCpH,SAAS;UACb;UACAc,EAAE,EAAE4H,IAAI,CAACiM,WAAW,CAAC7T,EAAE;UACvB8J,OAAO,EAAElC,IAAI,CAACiM,WAAW,CAAC/J,OAAO;UACjChG,IAAI,EAAE8D,IAAI,CAACiM,WAAW,CAAC/P,IAAI;UAC3BiG,MAAM,EAAEnC,IAAI,CAACiM,WAAW,CAAC9J;UACzB;SACD,GACD,IAAI,CAAE;OACX;;MAED,OAAO7B,sBAAsC,CAAC,CAAC;IACjD,CAAC,CAAC,EACFlP,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,kCAAkC,EAClCA,KAAK,CACN;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;IAEH,MAAMkO,GAAG,GAAGZ,IAAI,CAACjS,SAAS,EAAE;IAC5B,IAAI,CAAC5D,aAAa,CAAC0P,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAmB,0BAA0BA,CACxBrO,cAAsB;IAEtB,MAAMkN,IAAI,GAAG,IAAI,CAACtW,MAAM,CACrBqE,SAAS,CAAwB;MAChCI,KAAK,EAAEvG,6BAA6B;MACpCoL,SAAS,EAAE;QAAEF;MAAc;KAC5B,CAAC,CACD1E,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAE+S,eAAe,CAAC,EAC7C7a,MAAM,CAAC8a,OAAO,CAAC,EACfhb,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,sCAAsC,EACtCA,KAAK,CACN;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;IAEH,MAAMkO,GAAG,GAAGZ,IAAI,CAACjS,SAAS,EAAE;IAC5B,IAAI,CAAC5D,aAAa,CAAC0P,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACQD,YAAYA,CAAA;IAClB,MAAMuB,KAAK,GAAGxU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACuU,KAAK,EAAE;MACV,IAAI,CAAC3X,MAAM,CAACkP,IAAI,CAAC,oBAAoB,CAAC;MACtC,OAAO,KAAK;;IAGd,IAAI;MACF;MACA,MAAM0I,KAAK,GAAGD,KAAK,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9B,IAAID,KAAK,CAAC9O,MAAM,KAAK,CAAC,EAAE;QACtB,IAAI,CAAC9I,MAAM,CAACkP,IAAI,CAAC,0BAA0B,CAAC;QAC5C,OAAO,KAAK;;MAGd;MACA,MAAM4I,OAAO,GAAGzU,IAAI,CAACC,KAAK,CAACyU,IAAI,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC;MAE1C;MACA,IAAI,CAACE,OAAO,CAACE,GAAG,EAAE;QAChB,IAAI,CAAChY,MAAM,CAACkP,IAAI,CAAC,8BAA8B,CAAC;QAChD,OAAO,KAAK;;MAGd,MAAM+I,cAAc,GAAG,IAAIhO,IAAI,CAAC6N,OAAO,CAACE,GAAG,GAAG,IAAI,CAAC;MACnD,MAAM3D,GAAG,GAAG,IAAIpK,IAAI,EAAE;MAEtB,IAAIgO,cAAc,GAAG5D,GAAG,EAAE;QACxB,IAAI,CAACrU,MAAM,CAACkP,IAAI,CAAC,cAAc,EAAE;UAC/BgJ,UAAU,EAAED,cAAc,CAACpV,WAAW,EAAE;UACxCwR,GAAG,EAAEA,GAAG,CAACxR,WAAW;SACrB,CAAC;QACF,OAAO,KAAK;;MAGd,OAAO,IAAI;KACZ,CAAC,OAAOoB,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,0CAA0C,EAC1CA,KAAc,CACf;MACD,OAAO,KAAK;;EAEhB;EAEAI,4BAA4BA,CAAA;IAC1B;IACA,IAAI,CAAC,IAAI,CAAC+R,YAAY,EAAE,EAAE;MACxB,IAAI,CAACpW,MAAM,CAACkP,IAAI,CACd,2EAA2E,CAC5E;MACD,OAAO7S,EAAE,CAAC,EAAE,CAAC;;IAGf,IAAI,CAAC2D,MAAM,CAACwI,KAAK,CAAC,kDAAkD,CAAC;IAErE,MAAM6N,IAAI,GAAG,IAAI,CAACtW,MAAM,CACrBqE,SAAS,CAAyB;MACjCI,KAAK,EAAEvF;KACR,CAAC,CACDwF,IAAI,CACH9H,GAAG,CAAEiM,MAAM,IACT,IAAI,CAAC5I,MAAM,CAACwI,KAAK,CACf,wDAAwD,EACxDI,MAAM,CACP,CACF,EACDnM,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMkH,eAAe,GAAGlH,MAAM,CAAClE,IAAI,EAAEyT,iBAAiB,IAAI,EAAE;MAC5D,IAAI,CAACnY,MAAM,CAACwI,KAAK,CACf,oCAAoC,EACpCsH,eAAe,CAChB;MACD,IAAI,CAACY,wBAAwB,CAACZ,eAAe,EAAE,IAAI,CAAC;MACpD,OAAOA,eAAe;IACxB,CAAC,CAAC,EACFpT,UAAU,CAAE+J,GAAG,IAAI;MACjB,IAAI,CAACzG,MAAM,CAACiE,KAAK,CACf,wCAAwC,EACxCwC,GAAY,CACb;MACD;MACA,OAAOpK,EAAE,CAAC,EAAE,CAAC;IACf,CAAC,CAAC;IACF;IACAE,KAAK,CAAC,CAAC,CAAC,CAAC;KACV;;IAEH,MAAM0a,GAAG,GAAGZ,IAAI,CAACjS,SAAS,EAAE;IAC5B,IAAI,CAAC5D,aAAa,CAAC0P,IAAI,CAAC+G,GAAG,CAAC;IAC5B,OAAOZ,IAAI;EACb;EACAlS,2BAA2BA,CAAA;IACzB;IACA,MAAMwT,KAAK,GAAGxU,YAAY,CAACC,OAAO,CAAC,OAAO,CAAC;IAC3C,IAAI,CAACuU,KAAK,EAAE;MACV,IAAI,CAAC3X,MAAM,CAACkP,IAAI,CACd,6DAA6D,CAC9D;MACD,OAAO1S,KAAK;;IAGd,IAAI,CAACwD,MAAM,CAACwI,KAAK,CACf,4DAA4D,CAC7D;IAED,MAAM4P,OAAO,GAAG,IAAI,CAACrY,MAAM,CAACqE,SAAS,CAA4B;MAC/DI,KAAK,EAAEtH;KACR,CAAC;IAEF,MAAMmb,UAAU,GAAGD,OAAO,CAAC3T,IAAI,CAC7BhI,GAAG,CAAEmM,MAAM,IAAI;MACb,MAAMnF,YAAY,GAAGmF,MAAM,CAAClE,IAAI,EAAE4T,oBAAoB;MACtD,IAAI,CAAC7U,YAAY,EAAE;QACjB,MAAM,IAAIsF,KAAK,CAAC,kCAAkC,CAAC;;MAGrD,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CACf,sCAAsC,EACtC/E,YAAY,CACb;MAED,MAAM8U,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAAC/U,YAAY,CAAC;MAE3D;MACA,IAAI,IAAI,CAACrD,iBAAiB,CAACkN,GAAG,CAACiL,UAAU,CAAC7U,EAAE,CAAC,EAAE;QAC7C,IAAI,CAAC1D,MAAM,CAACwI,KAAK,CACf,mBAAmB+P,UAAU,CAAC7U,EAAE,6BAA6B,CAC9D;QACD,MAAM,IAAIqF,KAAK,CAAC,sCAAsC,CAAC;;MAGzD;MACA,IAAI,CAAC/I,MAAM,CAACwI,KAAK,CAAC,iDAAiD,CAAC;MAEpE;MACA,MAAMiQ,oBAAoB,GAAG,IAAI,CAACtY,aAAa,CAAC0O,KAAK;MACrD,MAAM6J,oBAAoB,GAAGD,oBAAoB,CAACpM,IAAI,CACnD+B,CAAC,IAAKA,CAAC,CAAC1K,EAAE,KAAK6U,UAAU,CAAC7U,EAAE,CAC9B;MAED,IAAIgV,oBAAoB,EAAE;QACxB,IAAI,CAAC1Y,MAAM,CAACwI,KAAK,CACf,sDAAsD,EACtD+P,UAAU,CAAC7U,EAAE,CACd;QACD,OAAO6U,UAAU;;MAGnB;MACA,IAAI,CAACvS,qBAAqB,EAAE;MAE5B;MACA,IAAI,CAAC2S,uBAAuB,CAACJ,UAAU,CAAC;MAExC;MACA,IAAI,CAACtY,IAAI,CAAC2W,GAAG,CAAC,MAAK;QACjB;QACA,MAAMgC,oBAAoB,GAAG,CAACL,UAAU,EAAE,GAAGE,oBAAoB,CAAC;QAElE,IAAI,CAACzY,MAAM,CAACwI,KAAK,CACf,wDAAwDoQ,oBAAoB,CAAC9P,MAAM,SAAS,CAC7F;QAED,IAAI,CAAC3I,aAAa,CAACyD,IAAI,CAACgV,oBAAoB,CAAC;QAC7C,IAAI,CAACtY,iBAAiB,CAACsD,IAAI,CAAC,IAAI,CAACtD,iBAAiB,CAACuO,KAAK,GAAG,CAAC,CAAC;MAC/D,CAAC,CAAC;MAEF,IAAI,CAAC7O,MAAM,CAACwI,KAAK,CACf,+CAA+C,EAC/C+P,UAAU,CACX;MAED,OAAOA,UAAU;IACnB,CAAC,CAAC;IACF;IACA7b,UAAU,CAAE+J,GAAG,IAAI;MACjB,IACEA,GAAG,YAAYsC,KAAK,IACpBtC,GAAG,CAACuF,OAAO,KAAK,sCAAsC,EACtD;QACA,OAAOxP,KAAK;;MAGd,IAAI,CAACwD,MAAM,CAACiE,KAAK,CAAC,oCAAoC,EAAEwC,GAAY,CAAC;MACrE,OAAOjK,KAAK;IACd,CAAC,CAAC;IACF;IACAG,GAAG,CAAE8G,YAAY,IAAI;MACnB,IAAI,CAACzD,MAAM,CAACwI,KAAK,CACf,6CAA6C,EAC7C/E,YAAY,CACb;IACH,CAAC,CAAC,CACH;IAED,MAAMwT,GAAG,GAAGoB,UAAU,CAACjU,SAAS,CAAC;MAC/BR,IAAI,EAAGH,YAAY,IAAI;QACrB,IAAI,CAACzD,MAAM,CAACwI,KAAK,CACf,yCAAyC,EACzC/E,YAAY,CACb;MACH,CAAC;MACDQ,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,CACN;MACH;KACD,CAAC;IAEF,IAAI,CAACzD,aAAa,CAAC0P,IAAI,CAAC+G,GAAG,CAAC;IAC5B,IAAI,CAACjX,MAAM,CAACwI,KAAK,CAAC,mDAAmD,CAAC;IACtE,OAAO6P,UAAU;EACnB;EACA;EACA;EACA;EAEQrV,oBAAoBA,CAAA;IAC1B,IAAI,CAAC6V,eAAe,GAAGC,WAAW,CAAC,MAAK;MACtC,IAAI,CAACC,2BAA2B,EAAE;IACpC,CAAC,EAAE,OAAO,CAAC;EACb;EACQA,2BAA2BA,CAAA;IACjC,MAAM1E,GAAG,GAAG,IAAIpK,IAAI,EAAE;IACtB,MAAM+O,aAAa,GAAG,IAAI/O,IAAI,CAACoK,GAAG,CAAC4E,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;IAExE,IAAIC,YAAY,GAAG,CAAC;IAEpB,IAAI,CAAC9Y,iBAAiB,CAACoD,OAAO,CAAC,CAACC,YAAY,EAAEC,EAAE,KAAI;MAClD,MAAMyV,gBAAgB,GAAG,IAAIlP,IAAI,CAACxG,YAAY,CAAC6G,SAAS,CAAC;MACzD,IAAI6O,gBAAgB,GAAGH,aAAa,EAAE;QACpC,IAAI,CAAC5Y,iBAAiB,CAACgZ,MAAM,CAAC1V,EAAE,CAAC;QACjCwV,YAAY,EAAE;;IAElB,CAAC,CAAC;IAEF,IAAIA,YAAY,GAAG,CAAC,EAAE;MACpB,IAAI,CAAClZ,MAAM,CAACwI,KAAK,CAAC,cAAc0Q,YAAY,wBAAwB,CAAC;MAErE;MACA,MAAMG,sBAAsB,GAAGxV,KAAK,CAACC,IAAI,CACvC,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,EAAE,CAChC;MACD,MAAM6J,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CACtDwL,sBAAsB,CACvB;MAED,IAAI,CAAClZ,aAAa,CAACyD,IAAI,CAACgK,mBAAmB,CAAC;MAC5C,IAAI,CAAC5J,iBAAiB,EAAE;;EAE5B;EACA;;;;;EAKQ6J,uBAAuBA,CAC7B1N,aAA6B;IAE7B,OAAOA,aAAa,CAACmZ,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAI;MACjC;MACA,MAAMC,KAAK,GAAG,IAAIxP,IAAI,CAACsP,CAAC,CAACjP,SAAS,IAAI,CAAC,CAAC;MACxC,MAAMoP,KAAK,GAAG,IAAIzP,IAAI,CAACuP,CAAC,CAAClP,SAAS,IAAI,CAAC,CAAC;MACxC,OAAOoP,KAAK,CAACT,OAAO,EAAE,GAAGQ,KAAK,CAACR,OAAO,EAAE,CAAC,CAAC;IAC5C,CAAC,CAAC;EACJ;;EAEQ9M,gBAAgBA,CAAA;IACtB,OAAOhJ,YAAY,CAACC,OAAO,CAAC,QAAQ,CAAC,IAAI,EAAE;EAC7C;EACQoG,gBAAgBA,CAACwC,OAAgB;IACvC,IAAI,CAACA,OAAO,EAAE;MACZ,IAAI,CAAChM,MAAM,CAACiE,KAAK,CACf,6DAA6D,CAC9D;MACD,MAAM,IAAI8E,KAAK,CAAC,4BAA4B,CAAC;;IAG/C,IAAI;MACF;MACA,IAAI,CAACiD,OAAO,CAACtI,EAAE,IAAI,CAACsI,OAAO,CAACS,GAAG,EAAE;QAC/B,IAAI,CAACzM,MAAM,CAACiE,KAAK,CACf,wCAAwC,EACxCrB,SAAS,EACToJ,OAAO,CACR;QACD,MAAM,IAAIjD,KAAK,CAAC,wBAAwB,CAAC;;MAG3C;MACA,IAAI4Q,gBAAgB;MACpB,IAAI;QACFA,gBAAgB,GAAG3N,OAAO,CAACjB,MAAM,GAC7B,IAAI,CAACC,aAAa,CAACgB,OAAO,CAACjB,MAAM,CAAC,GAClCnI,SAAS;OACd,CAAC,OAAOqB,KAAK,EAAE;QACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,yEAAyE,EACzEjL,KAAK,CACN;QACD0V,gBAAgB,GAAG;UACjBlN,GAAG,EAAET,OAAO,CAAC/C,QAAQ,IAAI,SAAS;UAClCvF,EAAE,EAAEsI,OAAO,CAAC/C,QAAQ,IAAI,SAAS;UACjC+N,QAAQ,EAAE,cAAc;UACxB4C,KAAK,EAAE,qBAAqB;UAC5BC,IAAI,EAAE,MAAM;UACZC,QAAQ,EAAE;SACX;;MAGH;MACA,IAAIC,kBAAkB;MACtB,IAAI/N,OAAO,CAACgO,QAAQ,EAAE;QACpB,IAAI;UACFD,kBAAkB,GAAG,IAAI,CAAC/O,aAAa,CAACgB,OAAO,CAACgO,QAAQ,CAAC;SAC1D,CAAC,OAAO/V,KAAK,EAAE;UACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,2EAA2E,EAC3EjL,KAAK,CACN;UACD8V,kBAAkB,GAAG;YACnBtN,GAAG,EAAET,OAAO,CAAC9C,UAAU,IAAI,SAAS;YACpCxF,EAAE,EAAEsI,OAAO,CAAC9C,UAAU,IAAI,SAAS;YACnC8N,QAAQ,EAAE,cAAc;YACxB4C,KAAK,EAAE,qBAAqB;YAC5BC,IAAI,EAAE,MAAM;YACZC,QAAQ,EAAE;WACX;;;MAIL;MACA,MAAMG,qBAAqB,GACzBjO,OAAO,CAACyK,WAAW,EAAEha,GAAG,CAAEka,GAAG,KAAM;QACjCjT,EAAE,EAAEiT,GAAG,CAACjT,EAAE,IAAIiT,GAAG,CAAClK,GAAG,IAAI,cAAcxC,IAAI,CAACoK,GAAG,EAAE,EAAE;QACnD6F,GAAG,EAAEvD,GAAG,CAACuD,GAAG,IAAI,EAAE;QAClB1S,IAAI,EAAEmP,GAAG,CAACnP,IAAI,IAAI,SAAS;QAC3BzC,IAAI,EAAE4R,GAAG,CAAC5R,IAAI,IAAI,YAAY;QAC9BgI,IAAI,EAAE4J,GAAG,CAAC5J,IAAI,IAAI,CAAC;QACnB5F,QAAQ,EAAEwP,GAAG,CAACxP,QAAQ,IAAI;OAC3B,CAAC,CAAC,IAAI,EAAE;MAEX;MACA,MAAMoP,iBAAiB,GAAG;QACxB,GAAGvK,OAAO;QACVS,GAAG,EAAET,OAAO,CAACtI,EAAE,IAAIsI,OAAO,CAACS,GAAG;QAC9B/I,EAAE,EAAEsI,OAAO,CAACtI,EAAE,IAAIsI,OAAO,CAACS,GAAG;QAC7Be,OAAO,EAAExB,OAAO,CAACwB,OAAO,IAAI,EAAE;QAC9BzC,MAAM,EAAE4O,gBAAgB;QACxBrP,SAAS,EAAE,IAAI,CAACC,aAAa,CAACyB,OAAO,CAAC1B,SAAS,CAAC;QAChDN,MAAM,EAAEgC,OAAO,CAAChC,MAAM,GAAG,IAAI,CAACO,aAAa,CAACyB,OAAO,CAAChC,MAAM,CAAC,GAAGpH,SAAS;QACvE6T,WAAW,EAAEwD,qBAAqB;QAClCE,QAAQ,EAAEnO,OAAO,CAACmO,QAAQ,IAAI;OAC/B;MAED;MACA,IAAIJ,kBAAkB,EAAE;QACtBxD,iBAAiB,CAACyD,QAAQ,GAAGD,kBAAkB;;MAGjD,IAAI,CAAC/Z,MAAM,CAACwI,KAAK,CAAC,kDAAkD,EAAE;QACpEkB,SAAS,EAAE6M,iBAAiB,CAAC7S,EAAE;QAC/BuF,QAAQ,EAAEsN,iBAAiB,CAACxL,MAAM,EAAErH;OACrC,CAAC;MAEF,OAAO6S,iBAAiB;KACzB,CAAC,OAAOtS,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,6CAA6C,EAC7CA,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,GAAG,IAAI8E,KAAK,CAACqR,MAAM,CAACnW,KAAK,CAAC,CAAC,EACzD+H,OAAO,CACR;MACD,MAAM,IAAIjD,KAAK,CACb,gCACE9E,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,CAAC+H,OAAO,GAAGoO,MAAM,CAACnW,KAAK,CACvD,EAAE,CACH;;EAEL;EAEO+G,aAAaA,CAACkK,IAAS;IAC5B,IAAI,CAACA,IAAI,EAAE;MACT,MAAM,IAAInM,KAAK,CAAC,yBAAyB,CAAC;;IAG5C;IACA,MAAMmC,MAAM,GAAGgK,IAAI,CAACxR,EAAE,IAAIwR,IAAI,CAACzI,GAAG;IAClC,IAAI,CAACvB,MAAM,EAAE;MACX,MAAM,IAAInC,KAAK,CAAC,qBAAqB,CAAC;;IAGxC;IACA,MAAMiO,QAAQ,GAAG9B,IAAI,CAAC8B,QAAQ,IAAI,cAAc;IAChD,MAAM4C,KAAK,GAAG1E,IAAI,CAAC0E,KAAK,IAAI,QAAQ1O,MAAM,cAAc;IACxD,MAAM4O,QAAQ,GACZ5E,IAAI,CAAC4E,QAAQ,KAAKlX,SAAS,IAAIsS,IAAI,CAAC4E,QAAQ,KAAK,IAAI,GACjD5E,IAAI,CAAC4E,QAAQ,GACb,IAAI;IACV,MAAMD,IAAI,GAAG3E,IAAI,CAAC2E,IAAI,IAAI,MAAM;IAEhC;IACA,OAAO;MACLpN,GAAG,EAAEvB,MAAM;MACXxH,EAAE,EAAEwH,MAAM;MACV8L,QAAQ,EAAEA,QAAQ;MAClB4C,KAAK,EAAEA,KAAK;MACZC,IAAI,EAAEA,IAAI;MACVC,QAAQ,EAAEA,QAAQ;MAClB;MACAO,KAAK,EAAEnF,IAAI,CAACmF,KAAK,IAAI,IAAI;MACzBC,GAAG,EAAEpF,IAAI,CAACoF,GAAG;MACbxF,QAAQ,EAAEI,IAAI,CAACJ,QAAQ,IAAI,KAAK;MAChCyF,UAAU,EAAErF,IAAI,CAACqF,UAAU,GAAG,IAAItQ,IAAI,CAACiL,IAAI,CAACqF,UAAU,CAAC,GAAG3X,SAAS;MACnE4X,SAAS,EAAEtF,IAAI,CAACsF,SAAS,GAAG,IAAIvQ,IAAI,CAACiL,IAAI,CAACsF,SAAS,CAAC,GAAG5X,SAAS;MAChE6X,SAAS,EAAEvF,IAAI,CAACuF,SAAS,GAAG,IAAIxQ,IAAI,CAACiL,IAAI,CAACuF,SAAS,CAAC,GAAG7X,SAAS;MAChE8X,cAAc,EAAExF,IAAI,CAACwF,cAAc;MACnCC,cAAc,EAAEzF,IAAI,CAACyF,cAAc;MACnCC,SAAS,EAAE1F,IAAI,CAAC0F;KACjB;EACH;EACQrP,qBAAqBA,CAACD,IAAkB;IAC9C,IAAI,CAACA,IAAI,EAAE;MACT,IAAI,CAACtL,MAAM,CAACiE,KAAK,CACf,kEAAkE,CACnE;MACD,MAAM,IAAI8E,KAAK,CAAC,iCAAiC,CAAC;;IAGpD,IAAI;MACF;MACA,IAAI,CAACuC,IAAI,CAAC5H,EAAE,IAAI,CAAC4H,IAAI,CAACmB,GAAG,EAAE;QACzB,IAAI,CAACzM,MAAM,CAACiE,KAAK,CACf,6CAA6C,EAC7CrB,SAAS,EACT0I,IAAI,CACL;QACD,MAAM,IAAIvC,KAAK,CAAC,6BAA6B,CAAC;;MAGhD;MACA,MAAM8R,sBAAsB,GAAG,EAAE;MACjC,IAAIvP,IAAI,CAACO,YAAY,IAAIhI,KAAK,CAACiX,OAAO,CAACxP,IAAI,CAACO,YAAY,CAAC,EAAE;QACzD,KAAK,MAAMkP,WAAW,IAAIzP,IAAI,CAACO,YAAY,EAAE;UAC3C,IAAI;YACF,IAAIkP,WAAW,EAAE;cACfF,sBAAsB,CAAC3K,IAAI,CAAC,IAAI,CAAClF,aAAa,CAAC+P,WAAW,CAAC,CAAC;;WAE/D,CAAC,OAAO9W,KAAK,EAAE;YACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,0DAA0D,EAC1DjL,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,iFAAiF,EACjF5D,IAAI,CACL;;MAGH;MACA,MAAM0P,kBAAkB,GAAG,EAAE;MAC7B,IAAI1P,IAAI,CAAChC,QAAQ,IAAIzF,KAAK,CAACiX,OAAO,CAACxP,IAAI,CAAChC,QAAQ,CAAC,EAAE;QACjD,IAAI,CAACtJ,MAAM,CAACwI,KAAK,CAAC,mDAAmD,EAAE;UACrEmH,KAAK,EAAErE,IAAI,CAAChC,QAAQ,CAACR;SACtB,CAAC;QAEF,KAAK,MAAMkD,OAAO,IAAIV,IAAI,CAAChC,QAAQ,EAAE;UACnC,IAAI;YACF,IAAI0C,OAAO,EAAE;cACX,MAAMuK,iBAAiB,GAAG,IAAI,CAAC/M,gBAAgB,CAACwC,OAAO,CAAC;cACxD,IAAI,CAAChM,MAAM,CAACwI,KAAK,CACf,kDAAkD,EAClD;gBACEkB,SAAS,EAAE6M,iBAAiB,CAAC7S,EAAE;gBAC/B8J,OAAO,EAAE+I,iBAAiB,CAAC/I,OAAO,EAAEgH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;gBACpDzJ,MAAM,EAAEwL,iBAAiB,CAACxL,MAAM,EAAEiM;eACnC,CACF;cACDgE,kBAAkB,CAAC9K,IAAI,CAACqG,iBAAiB,CAAC;;WAE7C,CAAC,OAAOtS,KAAK,EAAE;YACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,sEAAsE,EACtEjL,KAAK,CACN;;;OAGN,MAAM;QACL,IAAI,CAACjE,MAAM,CAACwI,KAAK,CACf,8EAA8E,CAC/E;;MAGH;MACA,IAAIyS,qBAAqB,GAAG,IAAI;MAChC,IAAI3P,IAAI,CAACiM,WAAW,EAAE;QACpB,IAAI;UACF0D,qBAAqB,GAAG,IAAI,CAACzR,gBAAgB,CAAC8B,IAAI,CAACiM,WAAW,CAAC;SAChE,CAAC,OAAOtT,KAAK,EAAE;UACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CACd,6DAA6D,EAC7DjL,KAAK,CACN;;;MAIL;MACA,MAAM2H,sBAAsB,GAAG;QAC7B,GAAGN,IAAI;QACPmB,GAAG,EAAEnB,IAAI,CAAC5H,EAAE,IAAI4H,IAAI,CAACmB,GAAG;QACxB/I,EAAE,EAAE4H,IAAI,CAAC5H,EAAE,IAAI4H,IAAI,CAACmB,GAAG;QACvBZ,YAAY,EAAEgP,sBAAsB;QACpCvR,QAAQ,EAAE0R,kBAAkB;QAC5BzD,WAAW,EAAE0D,qBAAqB;QAClCC,WAAW,EAAE5P,IAAI,CAAC4P,WAAW,IAAI,CAAC;QAClC5O,OAAO,EAAE,CAAC,CAAChB,IAAI,CAACgB,OAAO;QACvBkO,SAAS,EAAE,IAAI,CAACjQ,aAAa,CAACe,IAAI,CAACkP,SAAS,CAAC;QAC7CC,SAAS,EAAE,IAAI,CAAClQ,aAAa,CAACe,IAAI,CAACmP,SAAS;OAC7C;MAED,IAAI,CAACza,MAAM,CAACwI,KAAK,CACf,uDAAuD,EACvD;QACEW,cAAc,EAAEyC,sBAAsB,CAAClI,EAAE;QACzCyX,gBAAgB,EAAEN,sBAAsB,CAAC/R,MAAM;QAC/CsS,YAAY,EAAEJ,kBAAkB,CAAClS;OAClC,CACF;MAED,OAAO8C,sBAAsB;KAC9B,CAAC,OAAO3H,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,kDAAkD,EAClDA,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,GAAG,IAAI8E,KAAK,CAACqR,MAAM,CAACnW,KAAK,CAAC,CAAC,EACzDqH,IAAI,CACL;MACD,MAAM,IAAIvC,KAAK,CACb,qCACE9E,KAAK,YAAY8E,KAAK,GAAG9E,KAAK,CAAC+H,OAAO,GAAGoO,MAAM,CAACnW,KAAK,CACvD,EAAE,CACH;;EAEL;EACQsG,aAAaA,CAAC5H,IAA+B;IACnD,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIsH,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOtH,IAAI,KAAK,QAAQ,GAAG,IAAIsH,IAAI,CAACtH,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CAAC,yBAAyBvM,IAAI,EAAE,EAAEsB,KAAK,CAAC;MACxD,OAAO,IAAIgG,IAAI,EAAE;;EAErB;EAEA;EACQa,QAAQA,CAACnI,IAA+B;IAC9C,IAAI,CAACA,IAAI,EAAE,OAAO,IAAIsH,IAAI,EAAE;IAC5B,IAAI;MACF,OAAO,OAAOtH,IAAI,KAAK,QAAQ,GAAG,IAAIsH,IAAI,CAACtH,IAAI,CAAC,GAAGA,IAAI;KACxD,CAAC,OAAOsB,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACkP,IAAI,CAAC,+BAA+BvM,IAAI,EAAE,EAAEsB,KAAK,CAAC;MAC9D,OAAO,IAAIgG,IAAI,EAAE;;EAErB;EAOQuO,qBAAqBA,CAAC/U,YAA0B;IACtD,IAAI,CAACzD,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1B/E,YAAY,CACb;IAED,IAAI,CAACA,YAAY,EAAE;MACjB,IAAI,CAACzD,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,mCAAmC,CAAC;MACxE,MAAM,IAAI8E,KAAK,CAAC,0BAA0B,CAAC;;IAG7C;IACA,MAAMgG,cAAc,GAAGtL,YAAY,CAACC,EAAE,IAAKD,YAAoB,CAACgJ,GAAG;IACnE,IAAI,CAACsC,cAAc,EAAE;MACnB,IAAI,CAAC/O,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,4BAA4B,EAC5BR,YAAY,CACb;MACD,MAAM,IAAIsF,KAAK,CAAC,6BAA6B,CAAC;;IAGhD,IAAI,CAACtF,YAAY,CAAC6G,SAAS,EAAE;MAC3B,IAAI,CAACtK,MAAM,CAACkP,IAAI,CACd,gBAAgB,EAChB,uDAAuD,EACvDzL,YAAY,CACb;MACDA,YAAY,CAAC6G,SAAS,GAAG,IAAIL,IAAI,EAAE;;IAGrC,IAAI;MACF,MAAMsO,UAAU,GAAG;QACjB,GAAG9U,YAAY;QACfgJ,GAAG,EAAEsC,cAAc;QACnBrL,EAAE,EAAEqL,cAAc;QAClBzE,SAAS,EAAE,IAAIL,IAAI,CAACxG,YAAY,CAAC6G,SAAS,CAAC;QAC3C,IAAI7G,YAAY,CAACwF,QAAQ,IAAI;UAC3BA,QAAQ,EAAE,IAAI,CAACoS,eAAe,CAAC5X,YAAY,CAACwF,QAAQ;SACrD,CAAC;QACF,IAAIxF,YAAY,CAACuI,OAAO,IAAI;UAC1BA,OAAO,EAAE,IAAI,CAACsP,mBAAmB,CAAC7X,YAAY,CAACuI,OAAO;SACvD;OACF;MAED,IAAI,CAAChM,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChC+P,UAAU,CACX;MACD,OAAOA,UAAU;KAClB,CAAC,OAAOtU,KAAK,EAAE;MACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,gCAAgC,EAChCA,KAAK,CACN;MACD,MAAMA,KAAK;;EAEf;EACQoX,eAAeA,CAACtQ,MAAW;IACjC,OAAO;MACLrH,EAAE,EAAEqH,MAAM,CAACrH,EAAE;MACbsT,QAAQ,EAAEjM,MAAM,CAACiM,QAAQ;MACzB,IAAIjM,MAAM,CAACsP,KAAK,IAAI;QAAEA,KAAK,EAAEtP,MAAM,CAACsP;MAAK,CAAE;KAC5C;EACH;EAEA;;;;;EAKQiB,mBAAmBA,CAACtP,OAAY;IACtC,IAAI,CAACA,OAAO,EAAE,OAAO,IAAI;IAEzB,OAAO;MACLtI,EAAE,EAAEsI,OAAO,CAACtI,EAAE,IAAIsI,OAAO,CAACS,GAAG;MAC7Be,OAAO,EAAExB,OAAO,CAACwB,OAAO,IAAI,EAAE;MAC9BhG,IAAI,EAAEwE,OAAO,CAACxE,IAAI,IAAI,MAAM;MAC5B8C,SAAS,EAAE,IAAI,CAACQ,QAAQ,CAACkB,OAAO,CAAC1B,SAAS,CAAC;MAC3CmM,WAAW,EAAEzK,OAAO,CAACyK,WAAW,IAAI,EAAE;MACtC,IAAIzK,OAAO,CAACjB,MAAM,IAAI;QAAEA,MAAM,EAAE,IAAI,CAACsQ,eAAe,CAACrP,OAAO,CAACjB,MAAM;MAAC,CAAE;KACvE;EACH;EACA;;;;;EAKQ2C,WAAWA,CACjBvN,aAA4C,EAC5Cob,cAAA,GAA0B,IAAI;IAE9B,MAAMC,iBAAiB,GAAG3X,KAAK,CAACiX,OAAO,CAAC3a,aAAa,CAAC,GAClDA,aAAa,GACb,CAACA,aAAa,CAAC;IAEnB,IAAI,CAACH,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,oCAAoCgT,iBAAiB,CAAC1S,MAAM,gBAAgB,CAC7E;IAED,IAAI0S,iBAAiB,CAAC1S,MAAM,KAAK,CAAC,EAAE;MAClC,IAAI,CAAC9I,MAAM,CAACkP,IAAI,CAAC,gBAAgB,EAAE,qCAAqC,CAAC;MACzE;;IAGF;IACA,MAAMuM,kBAAkB,GAAGD,iBAAiB,CAAC5e,MAAM,CAChDyQ,KAAK,IAAKA,KAAK,KAAKA,KAAK,CAAC3J,EAAE,IAAK2J,KAAa,CAACZ,GAAG,CAAC,CACrD;IAED,IAAIgP,kBAAkB,CAAC3S,MAAM,KAAK0S,iBAAiB,CAAC1S,MAAM,EAAE;MAC1D,IAAI,CAAC9I,MAAM,CAACkP,IAAI,CACd,gBAAgB,EAChB,SACEsM,iBAAiB,CAAC1S,MAAM,GAAG2S,kBAAkB,CAAC3S,MAChD,kCAAkC,CACnC;;IAGH,IAAI4S,UAAU,GAAG,CAAC;IAClB,IAAIC,YAAY,GAAG,CAAC;IAEpB;IACAF,kBAAkB,CAACjY,OAAO,CAAC,CAAC6J,KAAK,EAAEE,KAAK,KAAI;MAC1C,IAAI;QACF;QACA,MAAMqO,OAAO,GAAGvO,KAAK,CAAC3J,EAAE,IAAK2J,KAAa,CAACZ,GAAG;QAC9C,IAAI,CAACmP,OAAO,EAAE;UACZ,IAAI,CAAC5b,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,0BAA0B,EAC1BoJ,KAAK,CACN;UACD;;QAGF;QACA,MAAMkL,UAAU,GAAG,IAAI,CAACC,qBAAqB,CAACnL,KAAK,CAAC;QAEpD;QACA,IAAIkO,cAAc,IAAI,IAAI,CAACnb,iBAAiB,CAACkN,GAAG,CAACiL,UAAU,CAAC7U,EAAE,CAAC,EAAE;UAC/D,IAAI,CAAC1D,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,gBAAgB+P,UAAU,CAAC7U,EAAE,oCAAoC,CAClE;UACDiY,YAAY,EAAE;UACd;;QAGF;QACA,IAAI,CAACvb,iBAAiB,CAACuD,GAAG,CAAC4U,UAAU,CAAC7U,EAAE,EAAE6U,UAAU,CAAC;QACrDmD,UAAU,EAAE;QAEZ,IAAI,CAAC1b,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,sBAAsB+P,UAAU,CAAC7U,EAAE,WAAW,CAC/C;OACF,CAAC,OAAOO,KAAK,EAAE;QACd,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iCAAiCsJ,KAAK,GAAG,CAAC,GAAG,EAC7CtJ,KAAK,CACN;;IAEL,CAAC,CAAC;IAEF,IAAI,CAACjE,MAAM,CAACwI,KAAK,CACf,gBAAgB,EAChB,0BAA0BkT,UAAU,WAAWC,YAAY,oBAAoB,IAAI,CAACvb,iBAAiB,CAAC2M,IAAI,EAAE,CAC7G;IAED;IACA,IAAI,CAAC8O,8BAA8B,EAAE;EACvC;EACA;;;;EAIQA,8BAA8BA,CAAA;IACpC,MAAMC,gBAAgB,GAAGjY,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,EAAE,CAAC;IAEpE;IACA,MAAM6J,mBAAmB,GAAG,IAAI,CAACC,uBAAuB,CAACiO,gBAAgB,CAAC;IAE1E,IAAI,CAAC9b,MAAM,CAACwI,KAAK,CACf,cAAcoF,mBAAmB,CAAC9E,MAAM,2DAA2D,CACpG;IAED,IAAI,CAAC3I,aAAa,CAACyD,IAAI,CAACgK,mBAAmB,CAAC;IAC5C,IAAI,CAAC5J,iBAAiB,EAAE;IACxB,IAAI,CAAC8J,+BAA+B,EAAE;EACxC;EAEA;;;EAGQ9J,iBAAiBA,CAAA;IACvB,MAAM8X,gBAAgB,GAAGjY,KAAK,CAACC,IAAI,CAAC,IAAI,CAAC1D,iBAAiB,CAAC2D,MAAM,EAAE,CAAC;IACpE,MAAMgY,mBAAmB,GAAGD,gBAAgB,CAAClf,MAAM,CAAEwR,CAAC,IAAK,CAACA,CAAC,CAACX,MAAM,CAAC;IACrE,MAAMkC,KAAK,GAAGoM,mBAAmB,CAACjT,MAAM;IAExC;IACA,IAAI,CAAC7I,IAAI,CAAC2W,GAAG,CAAC,MAAK;MACjB,IAAI,CAACtW,iBAAiB,CAACsD,IAAI,CAAC+L,KAAK,CAAC;MAElC;MACAvJ,MAAM,CAAC4V,aAAa,CAClB,IAAIC,WAAW,CAAC,0BAA0B,EAAE;QAC1CC,MAAM,EAAE;UAAEvM;QAAK;OAChB,CAAC,CACH;IACH,CAAC,CAAC;EACJ;EAEA;;;;EAIQgJ,uBAAuBA,CAAClV,YAA0B;IACxD,IAAI,CAACiK,WAAW,CAACjK,YAAY,EAAE,IAAI,CAAC;EACtC;EACA;;;;;EAKQiN,wBAAwBA,CAACyL,GAAa,EAAE1O,MAAe;IAC7D0O,GAAG,CAAC3Y,OAAO,CAAEE,EAAE,IAAI;MACjB,MAAM2J,KAAK,GAAG,IAAI,CAACjN,iBAAiB,CAAC6P,GAAG,CAACvM,EAAE,CAAC;MAC5C,IAAI2J,KAAK,EAAE;QACT,IAAI,CAACjN,iBAAiB,CAACuD,GAAG,CAACD,EAAE,EAAE;UAC7B,GAAG2J,KAAK;UACRI,MAAM;UACNzD,MAAM,EAAEyD,MAAM,GAAG,IAAIxD,IAAI,EAAE,CAACpH,WAAW,EAAE,GAAGD;SAC7C,CAAC;;IAEN,CAAC,CAAC;IACF,IAAI,CAACiZ,8BAA8B,EAAE;EACvC;EAEA;;;;;EAKQzM,4BAA4BA,CAACU,eAAyB;IAC5D7J,OAAO,CAACC,GAAG,CACT,4CAA4C,EAC5C4J,eAAe,CAAChH,MAAM,EACtB,eAAe,CAChB;IACD7C,OAAO,CAACC,GAAG,CACT,2CAA2C,EAC3C,IAAI,CAAC9F,iBAAiB,CAAC2M,IAAI,CAC5B;IAED,IAAIoC,YAAY,GAAG,CAAC;IACpBW,eAAe,CAACtM,OAAO,CAAEE,EAAE,IAAI;MAC7B,IAAI,IAAI,CAACtD,iBAAiB,CAACkN,GAAG,CAAC5J,EAAE,CAAC,EAAE;QAClCuC,OAAO,CAACC,GAAG,CAAC,+CAA+C,EAAExC,EAAE,CAAC;QAChE,IAAI,CAACtD,iBAAiB,CAACgZ,MAAM,CAAC1V,EAAE,CAAC;QACjCyL,YAAY,EAAE;OACf,MAAM;QACLlJ,OAAO,CAACC,GAAG,CACT,yDAAyD,EACzDxC,EAAE,CACH;;IAEL,CAAC,CAAC;IAEFuC,OAAO,CAACC,GAAG,CAAC,gCAAgC,EAAEiJ,YAAY,EAAE,eAAe,CAAC;IAC5ElJ,OAAO,CAACC,GAAG,CACT,0CAA0C,EAC1C,IAAI,CAAC9F,iBAAiB,CAAC2M,IAAI,CAC5B;IAED,IAAIoC,YAAY,GAAG,CAAC,EAAE;MACpBlJ,OAAO,CAACC,GAAG,CAAC,kDAAkD,CAAC;MAC/D,IAAI,CAAC2V,8BAA8B,EAAE;;IAGvC,OAAO1M,YAAY;EACrB;EAEA;;;;;;EAMQG,mBAAmBA,CACzBrL,KAAU,EACVmY,SAAiB,EACjBC,gBAAqB;IAErB,IAAI,CAACrc,MAAM,CAACiE,KAAK,CAAC,gBAAgB,EAAE,kBAAkBmY,SAAS,GAAG,EAAEnY,KAAK,CAAC;IAC1E,OAAO5H,EAAE,CAACggB,gBAAgB,CAAC;EAC7B;EACA;EACAC,WAAWA,CAACnT,cAAsB;IAChC,MAAM+B,MAAM,GAAG,IAAI,CAACiB,gBAAgB,EAAE;IACtC,IAAI,CAACjB,MAAM,EAAE;MACX,IAAI,CAAClL,MAAM,CAACkP,IAAI,CAAC,gBAAgB,EAAE,iCAAiC,CAAC;MACrE,OAAO7S,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC0D,MAAM,CACf6J,MAAM,CAAsB;MAC3BC,QAAQ,EAAE9L,qBAAqB;MAC/BsL,SAAS,EAAE;QACTwM,KAAK,EAAE;UACL1M,cAAc;UACd+B;;;KAGL,CAAC,CACDzG,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAE4X,WAAW,IAAI,KAAK,CAAC,EAClD5f,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAO3H,UAAU,CACf,MAAM,IAAIyM,KAAK,CAAC,kCAAkC,CAAC,CACpD;IACH,CAAC,CAAC,CACH;EACL;EAEAwT,UAAUA,CAACpT,cAAsB;IAC/B,MAAM+B,MAAM,GAAG,IAAI,CAACiB,gBAAgB,EAAE;IACtC,IAAI,CAACjB,MAAM,EAAE;MACX,IAAI,CAAClL,MAAM,CAACkP,IAAI,CAAC,gBAAgB,EAAE,gCAAgC,CAAC;MACpE,OAAO7S,EAAE,CAAC,KAAK,CAAC;;IAGlB,OAAO,IAAI,CAAC0D,MAAM,CACf6J,MAAM,CAAqB;MAC1BC,QAAQ,EAAE7L,oBAAoB;MAC9BqL,SAAS,EAAE;QACTwM,KAAK,EAAE;UACL1M,cAAc;UACd+B;;;KAGL,CAAC,CACDzG,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAKA,MAAM,CAAClE,IAAI,EAAE6X,UAAU,IAAI,KAAK,CAAC,EACjD7f,UAAU,CAAEuH,KAAK,IAAI;MACnB,IAAI,CAACjE,MAAM,CAACiE,KAAK,CACf,gBAAgB,EAChB,iCAAiC,EACjCA,KAAK,CACN;MACD,OAAO3H,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAAC,iCAAiC,CAAC,CAAC;IACvE,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;;;;;;;EASAyT,WAAWA,CACTtT,UAAkB,EAClBsE,OAAe,EACfiP,IAAW,EACXC,WAAA,GAAmB,MAAM,EACzBvT,cAAuB;IAEvBlD,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;MAC1DgD,UAAU;MACVsE,OAAO,EAAEA,OAAO,EAAEgH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;MAClCmI,OAAO,EAAE,CAAC,CAACF,IAAI;MACfG,QAAQ,EAAEH,IAAI,EAAE1X,IAAI;MACpB8X,QAAQ,EAAEJ,IAAI,EAAEjV,IAAI;MACpBsV,QAAQ,EAAEL,IAAI,EAAE1P,IAAI;MACpB2P,WAAW;MACXvT;KACD,CAAC;IAEF,IAAI,CAACD,UAAU,EAAE;MACf,MAAMjF,KAAK,GAAG,IAAI8E,KAAK,CAAC,yBAAyB,CAAC;MAClD9C,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,OAAO3H,UAAU,CAAC,MAAM2H,KAAK,CAAC;;IAGhC;IACA,MAAMoF,SAAS,GAAQ;MACrBH,UAAU;MACVsE,OAAO,EAAEA,OAAO,IAAI,EAAE;MACtBhG,IAAI,EAAEkV;KACP;IAED;IACA,IAAIvT,cAAc,EAAE;MAClBE,SAAS,CAACF,cAAc,GAAGA,cAAc;;IAG3C;IACA,IAAIsT,IAAI,EAAE;MACRpT,SAAS,CAACoT,IAAI,GAAGA,IAAI;MACrBxW,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAE;QAC1DnB,IAAI,EAAE0X,IAAI,CAAC1X,IAAI;QACfyC,IAAI,EAAEiV,IAAI,CAACjV,IAAI;QACfuF,IAAI,EAAE0P,IAAI,CAAC1P;OACZ,CAAC;;IAGJ9G,OAAO,CAACC,GAAG,CACT,sDAAsD,EACtDmD,SAAS,CACV;IAED,OAAO,IAAI,CAACtJ,MAAM,CACf6J,MAAM,CAAsB;MAC3BC,QAAQ,EAAEzM,qBAAqB;MAC/BiM,SAAS;MACT0T,OAAO,EAAE;QACPC,YAAY,EAAE,CAAC,CAACP,IAAI,CAAE;;KAEzB,CAAC,CACDhY,IAAI,CACHhI,GAAG,CAAEmM,MAAM,IAAI;MACb3C,OAAO,CAACC,GAAG,CACT,iDAAiD,EACjD0C,MAAM,CACP;MAED,IAAI,CAACA,MAAM,CAAClE,IAAI,EAAE8X,WAAW,EAAE;QAC7B,MAAM,IAAIzT,KAAK,CAAC,sCAAsC,CAAC;;MAGzD,MAAMiD,OAAO,GAAGpD,MAAM,CAAClE,IAAI,CAAC8X,WAAW;MACvCvW,OAAO,CAACC,GAAG,CAAC,gDAAgD,EAAE;QAC5DxC,EAAE,EAAEsI,OAAO,CAACtI,EAAE;QACd8D,IAAI,EAAEwE,OAAO,CAACxE,IAAI;QAClBgG,OAAO,EAAExB,OAAO,CAACwB,OAAO,EAAEgH,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;QAC1CyI,cAAc,EAAE,CAAC,CAACjR,OAAO,CAACyK,WAAW,EAAE3N;OACxC,CAAC;MAEF;MACA,MAAMyN,iBAAiB,GAAG,IAAI,CAAC/M,gBAAgB,CAACwC,OAAO,CAAC;MACxD/F,OAAO,CAACC,GAAG,CACT,yCAAyC,EACzCqQ,iBAAiB,CAClB;MAED,OAAOA,iBAAiB;IAC1B,CAAC,CAAC,EACF7Z,UAAU,CAAEuH,KAAK,IAAI;MACnBgC,OAAO,CAAChC,KAAK,CAAC,uCAAuC,EAAEA,KAAK,CAAC;MAC7D,IAAI,CAACjE,MAAM,CAACiE,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;MAElD;MACA,IAAIiZ,YAAY,GAAG,mCAAmC;MACtD,IAAIjZ,KAAK,CAAC+J,YAAY,EAAE;QACtBkP,YAAY,GAAG,4BAA4B;OAC5C,MAAM,IAAIjZ,KAAK,CAAC8J,aAAa,EAAEjF,MAAM,GAAG,CAAC,EAAE;QAC1CoU,YAAY,GAAGjZ,KAAK,CAAC8J,aAAa,CAAC,CAAC,CAAC,CAAC/B,OAAO,IAAIkR,YAAY;;MAG/D,OAAO5gB,UAAU,CAAC,MAAM,IAAIyM,KAAK,CAACmU,YAAY,CAAC,CAAC;IAClD,CAAC,CAAC,CACH;EACL;EAEA;EACA;EACA;EAEA;;;EAGAC,iBAAiBA,CAAC7S,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,MAAM3H,IAAI,GAAG2H,SAAS,YAAYL,IAAI,GAAGK,SAAS,GAAG,IAAIL,IAAI,CAACK,SAAS,CAAC;MACxE,OAAO3H,IAAI,CAACya,kBAAkB,CAAC,EAAE,EAAE;QACjCC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE,SAAS;QACjBC,MAAM,EAAE;OACT,CAAC;KACH,CAAC,OAAOtZ,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAuZ,gBAAgBA,CAACjD,UAAqC;IACpD,IAAI,CAACA,UAAU,EAAE,OAAO,SAAS;IACjC,MAAMkD,cAAc,GAClBlD,UAAU,YAAYtQ,IAAI,GAAGsQ,UAAU,GAAG,IAAItQ,IAAI,CAACsQ,UAAU,CAAC;IAChE,MAAMlG,GAAG,GAAG,IAAIpK,IAAI,EAAE;IACtB,MAAMyT,SAAS,GACb7M,IAAI,CAAC8M,GAAG,CAACtJ,GAAG,CAAC4E,OAAO,EAAE,GAAGwE,cAAc,CAACxE,OAAO,EAAE,CAAC,IAAI,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;IAEvE,IAAIyE,SAAS,GAAG,EAAE,EAAE;MAClB,OAAO,UAAUD,cAAc,CAACL,kBAAkB,CAAC,EAAE,EAAE;QACrDC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;;IAEN,OAAO,UAAUG,cAAc,CAACG,kBAAkB,EAAE,EAAE;EACxD;EAEA;;;EAGAC,iBAAiBA,CAACvT,SAAoC;IACpD,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IAErC,IAAI;MACF,MAAM3H,IAAI,GAAG2H,SAAS,YAAYL,IAAI,GAAGK,SAAS,GAAG,IAAIL,IAAI,CAACK,SAAS,CAAC;MACxE,MAAMwT,KAAK,GAAG,IAAI7T,IAAI,EAAE;MAExB,IAAItH,IAAI,CAACob,YAAY,EAAE,KAAKD,KAAK,CAACC,YAAY,EAAE,EAAE;QAChD,OAAOpb,IAAI,CAACya,kBAAkB,CAAC,EAAE,EAAE;UACjCC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC;;MAGJ,MAAMU,SAAS,GAAG,IAAI/T,IAAI,CAAC6T,KAAK,CAAC;MACjCE,SAAS,CAACC,OAAO,CAACD,SAAS,CAACE,OAAO,EAAE,GAAG,CAAC,CAAC;MAE1C,IAAIvb,IAAI,CAACob,YAAY,EAAE,KAAKC,SAAS,CAACD,YAAY,EAAE,EAAE;QACpD,OAAO,SAASpb,IAAI,CAACya,kBAAkB,CAAC,EAAE,EAAE;UAC1CC,IAAI,EAAE,SAAS;UACfC,MAAM,EAAE;SACT,CAAC,EAAE;;MAGN,MAAMa,GAAG,GAAGxb,IAAI,CACbib,kBAAkB,CAAC,OAAO,EAAE;QAAEQ,OAAO,EAAE;MAAO,CAAE,CAAC,CACjDC,WAAW,EAAE;MAChB,OAAO,GAAGF,GAAG,MAAMxb,IAAI,CAACya,kBAAkB,CAAC,EAAE,EAAE;QAC7CC,IAAI,EAAE,SAAS;QACfC,MAAM,EAAE;OACT,CAAC,EAAE;KACL,CAAC,OAAOrZ,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGAqa,oBAAoBA,CAAChV,QAAe,EAAEiE,KAAa;IACjD,IAAIA,KAAK,KAAK,CAAC,EAAE,OAAO,IAAI;IAE5B,IAAI;MACF,MAAMgR,UAAU,GAAGjV,QAAQ,CAACiE,KAAK,CAAC;MAClC,MAAMiR,OAAO,GAAGlV,QAAQ,CAACiE,KAAK,GAAG,CAAC,CAAC;MAEnC,IAAI,CAACgR,UAAU,EAAEjU,SAAS,IAAI,CAACkU,OAAO,EAAElU,SAAS,EAAE,OAAO,IAAI;MAE9D,MAAMmU,WAAW,GAAG,IAAI,CAACC,oBAAoB,CAACH,UAAU,CAACjU,SAAS,CAAC;MACnE,MAAMqU,QAAQ,GAAG,IAAI,CAACD,oBAAoB,CAACF,OAAO,CAAClU,SAAS,CAAC;MAE7D,OAAOmU,WAAW,KAAKE,QAAQ;KAChC,CAAC,OAAO1a,KAAK,EAAE;MACd,OAAO,KAAK;;EAEhB;EAEQya,oBAAoBA,CAACpU,SAAoC;IAC/D,IAAI,CAACA,SAAS,EAAE,OAAO,cAAc;IACrC,IAAI;MACF,OAAO,CACLA,SAAS,YAAYL,IAAI,GAAGK,SAAS,GAAG,IAAIL,IAAI,CAACK,SAAS,CAAC,EAC3DyT,YAAY,EAAE;KACjB,CAAC,OAAO9Z,KAAK,EAAE;MACd,OAAO,cAAc;;EAEzB;EAEA;;;EAGA2a,WAAWA,CAACC,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,SAAS;IAC/B,IAAIA,QAAQ,CAACC,UAAU,CAAC,QAAQ,CAAC,EAAE,OAAO,UAAU;IACpD,IAAID,QAAQ,CAACnS,QAAQ,CAAC,KAAK,CAAC,EAAE,OAAO,aAAa;IAClD,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,MAAM,CAAC,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,QAAQ,CAAC,EAC1D,OAAO,cAAc;IACvB,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,oBAAoB;IAChE,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,eAAe;IACtD,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,KAAK,CAAC,IAAImS,QAAQ,CAACnS,QAAQ,CAAC,YAAY,CAAC,EAC7D,OAAO,iBAAiB;IAC1B,OAAO,SAAS;EAClB;EAEA;;;EAGAqS,WAAWA,CAACF,QAAiB;IAC3B,IAAI,CAACA,QAAQ,EAAE,OAAO,MAAM;IAE5B,MAAMG,OAAO,GAA2B;MACtC,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,KAAK;MACxB,oBAAoB,EAAE,UAAU;MAChC,yEAAyE,EACvE,UAAU;MACZ,0BAA0B,EAAE,OAAO;MACnC,mEAAmE,EACjE,OAAO;MACT,+BAA+B,EAAE,YAAY;MAC7C,2EAA2E,EACzE,YAAY;MACd,QAAQ,EAAE,OAAO;MACjB,QAAQ,EAAE,OAAO;MACjB,iBAAiB,EAAE,aAAa;MAChC,8BAA8B,EAAE;KACjC;IAED,KAAK,MAAM,CAACC,GAAG,EAAEpQ,KAAK,CAAC,IAAIjJ,MAAM,CAACsZ,OAAO,CAACF,OAAO,CAAC,EAAE;MAClD,IAAIH,QAAQ,CAACnS,QAAQ,CAACuS,GAAG,CAAC,EAAE,OAAOpQ,KAAK;;IAE1C,OAAO,MAAM;EACf;EAEA;;;EAGAsQ,QAAQA,CAACnT,OAAY;IACnB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACyK,WAAW,IAAIzK,OAAO,CAACyK,WAAW,CAAC3N,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,KAAK;;IAGd,MAAMsW,UAAU,GAAGpT,OAAO,CAACyK,WAAW,CAAC,CAAC,CAAC;IACzC,IAAI,CAAC2I,UAAU,IAAI,CAACA,UAAU,CAAC5X,IAAI,EAAE;MACnC,OAAO,KAAK;;IAGd,MAAMA,IAAI,GAAG4X,UAAU,CAAC5X,IAAI,CAAC8M,QAAQ,EAAE;IACvC,OAAO9M,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO;EAC7C;EAEA;;;EAGA6X,cAAcA,CAACrT,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAO,KAAK;IAE1B;IACA,IACEA,OAAO,CAACxE,IAAI,KAAK3K,WAAW,CAAC2Z,aAAa,IAC1CxK,OAAO,CAACxE,IAAI,KAAK3K,WAAW,CAAC2Z,aAAa,EAC1C;MACA,OAAO,IAAI;;IAGb;IACA,IAAIxK,OAAO,CAACyK,WAAW,IAAIzK,OAAO,CAACyK,WAAW,CAAC3N,MAAM,GAAG,CAAC,EAAE;MACzD,OAAOkD,OAAO,CAACyK,WAAW,CAACC,IAAI,CAAEC,GAAQ,IAAI;QAC3C,MAAMnP,IAAI,GAAGmP,GAAG,CAACnP,IAAI,EAAE8M,QAAQ,EAAE;QACjC,OACE9M,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACvBwE,OAAO,CAACmO,QAAQ,EAAEkF,cAAc,KAC9B7X,IAAI,KAAK,OAAO,IAAIA,IAAI,KAAK,OAAO,CAAE;MAE7C,CAAC,CAAC;;IAGJ;IACA,OAAO,CAAC,CAACwE,OAAO,CAACmO,QAAQ,EAAEkF,cAAc;EAC3C;EAEA;;;EAGAC,kBAAkBA,CAACtT,OAAY;IAC7B,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACyK,WAAW,IAAIzK,OAAO,CAACyK,WAAW,CAAC3N,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMyW,eAAe,GAAGvT,OAAO,CAACyK,WAAW,CAACpK,IAAI,CAAEsK,GAAQ,IAAI;MAC5D,MAAMnP,IAAI,GAAGmP,GAAG,CAACnP,IAAI,EAAE8M,QAAQ,EAAE;MACjC,OACE9M,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;IAEpB,CAAC,CAAC;IAEF,OAAO+X,eAAe,EAAErF,GAAG,IAAI,EAAE;EACnC;EAEA;;;EAGAsF,uBAAuBA,CAACxT,OAAY;IAClC,IAAI,CAACA,OAAO,EAAE,OAAO,CAAC;IAEtB;IACA,IAAIA,OAAO,CAACmO,QAAQ,EAAEhT,QAAQ,EAAE;MAC9B,OAAO6E,OAAO,CAACmO,QAAQ,CAAChT,QAAQ;;IAGlC;IACA,IAAI6E,OAAO,CAACyK,WAAW,IAAIzK,OAAO,CAACyK,WAAW,CAAC3N,MAAM,GAAG,CAAC,EAAE;MACzD,MAAMyW,eAAe,GAAGvT,OAAO,CAACyK,WAAW,CAACpK,IAAI,CAAEsK,GAAQ,IAAI;QAC5D,MAAMnP,IAAI,GAAGmP,GAAG,CAACnP,IAAI,EAAE8M,QAAQ,EAAE;QACjC,OACE9M,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,eAAe,IACxBA,IAAI,KAAK,OAAO,IAChBA,IAAI,KAAK,OAAO;MAEpB,CAAC,CAAC;MAEF,IAAI+X,eAAe,IAAIA,eAAe,CAACpY,QAAQ,EAAE;QAC/C,OAAOoY,eAAe,CAACpY,QAAQ;;;IAInC,OAAO,CAAC;EACV;EAEA;;;EAGAsY,iBAAiBA,CAAClS,KAAa;IAC7B,MAAMmS,OAAO,GAAG,CACd,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE,EAAE,CACxE;IACD,OAAOA,OAAO,CAACnS,KAAK,GAAGmS,OAAO,CAAC5W,MAAM,CAAC;EACxC;EAEA;;;EAGA6W,mBAAmBA,CAACC,OAAe;IACjC,IAAI,CAACA,OAAO,IAAIA,OAAO,KAAK,CAAC,EAAE;MAC7B,OAAO,MAAM;;IAGf,MAAMC,OAAO,GAAGhP,IAAI,CAACiP,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACxC,MAAMG,gBAAgB,GAAGlP,IAAI,CAACiP,KAAK,CAACF,OAAO,GAAG,EAAE,CAAC;IACjD,OAAO,GAAGC,OAAO,IAAIE,gBAAgB,CAACzL,QAAQ,EAAE,CAAC0L,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;EACrE;EAEA;;;EAGAC,WAAWA,CAACjU,OAAY;IACtB,IAAI,CAACA,OAAO,IAAI,CAACA,OAAO,CAACyK,WAAW,IAAIzK,OAAO,CAACyK,WAAW,CAAC3N,MAAM,KAAK,CAAC,EAAE;MACxE,OAAO,EAAE;;IAGX,MAAMsW,UAAU,GAAGpT,OAAO,CAACyK,WAAW,CAAC,CAAC,CAAC;IACzC,OAAO2I,UAAU,EAAElF,GAAG,IAAI,EAAE;EAC9B;EAEA;;;EAGAgG,cAAcA,CAAClU,OAAY;IACzB,IAAI,CAACA,OAAO,EAAE,OAAOnP,WAAW,CAACka,IAAI;IAErC,IAAI;MACF,IAAI/K,OAAO,CAACxE,IAAI,EAAE;QAChB,MAAM2Y,OAAO,GAAGnU,OAAO,CAACxE,IAAI,CAAC8M,QAAQ,EAAE;QACvC,IAAI6L,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UAC5C,OAAOtjB,WAAW,CAACka,IAAI;SACxB,MAAM,IAAIoJ,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOtjB,WAAW,CAACujB,KAAK;SACzB,MAAM,IAAID,OAAO,KAAK,MAAM,IAAIA,OAAO,KAAK,MAAM,EAAE;UACnD,OAAOtjB,WAAW,CAACwjB,IAAI;SACxB,MAAM,IAAIF,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOtjB,WAAW,CAAC4W,KAAK;SACzB,MAAM,IAAI0M,OAAO,KAAK,OAAO,IAAIA,OAAO,KAAK,OAAO,EAAE;UACrD,OAAOtjB,WAAW,CAACyjB,KAAK;SACzB,MAAM,IAAIH,OAAO,KAAK,QAAQ,IAAIA,OAAO,KAAK,QAAQ,EAAE;UACvD,OAAOtjB,WAAW,CAAC0jB,MAAM;;;MAI7B,IAAIvU,OAAO,CAACyK,WAAW,EAAE3N,MAAM,EAAE;QAC/B,MAAMsW,UAAU,GAAGpT,OAAO,CAACyK,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI2I,UAAU,IAAIA,UAAU,CAAC5X,IAAI,EAAE;UACjC,MAAMgZ,iBAAiB,GAAGpB,UAAU,CAAC5X,IAAI,CAAC8M,QAAQ,EAAE;UAEpD,IAAIkM,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO3jB,WAAW,CAACujB,KAAK;WACzB,MAAM,IACLI,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO3jB,WAAW,CAACwjB,IAAI;WACxB,MAAM,IACLG,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAO3jB,WAAW,CAAC4W,KAAK;WACzB,MAAM,IACL+M,iBAAiB,KAAK,OAAO,IAC7BA,iBAAiB,KAAK,OAAO,EAC7B;YACA,OAAO3jB,WAAW,CAACyjB,KAAK;;;QAI5B,OAAOzjB,WAAW,CAACwjB,IAAI;;MAGzB,OAAOxjB,WAAW,CAACka,IAAI;KACxB,CAAC,OAAO9S,KAAK,EAAE;MACd,OAAOpH,WAAW,CAACka,IAAI;;EAE3B;EAEA;;;EAGA0J,eAAeA,CAAA;IACb,OAAO,CACL,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,EACJ,IAAI,CACL;EACH;EAEA;;;EAGAC,mBAAmBA,CAAC1U,OAAY,EAAEE,aAA4B;IAC5D,IAAI,CAACF,OAAO,EAAE;MACZ,OAAO,kCAAkC;;IAG3C,IAAI;MACF,MAAM2U,aAAa,GACjB3U,OAAO,CAACjB,MAAM,EAAErH,EAAE,KAAKwI,aAAa,IACpCF,OAAO,CAACjB,MAAM,EAAE0B,GAAG,KAAKP,aAAa,IACrCF,OAAO,CAAC/C,QAAQ,KAAKiD,aAAa;MAEpC,MAAM0U,SAAS,GAAGD,aAAa,GAC3B,kDAAkD,GAClD,qDAAqD;MAEzD,MAAMjE,WAAW,GAAG,IAAI,CAACwD,cAAc,CAAClU,OAAO,CAAC;MAEhD,IAAIA,OAAO,CAACyK,WAAW,IAAIzK,OAAO,CAACyK,WAAW,CAAC3N,MAAM,GAAG,CAAC,EAAE;QACzD,MAAMsW,UAAU,GAAGpT,OAAO,CAACyK,WAAW,CAAC,CAAC,CAAC;QACzC,IAAI2I,UAAU,IAAIA,UAAU,CAAC5X,IAAI,EAAE;UACjC,MAAMgZ,iBAAiB,GAAGpB,UAAU,CAAC5X,IAAI,CAAC8M,QAAQ,EAAE;UACpD,IAAIkM,iBAAiB,KAAK,OAAO,IAAIA,iBAAiB,KAAK,OAAO,EAAE;YAClE,OAAO,cAAc;WACtB,MAAM,IACLA,iBAAiB,KAAK,MAAM,IAC5BA,iBAAiB,KAAK,MAAM,EAC5B;YACA,OAAO,GAAGI,SAAS,MAAM;;;;MAK/B;MAEA,OAAO,GAAGA,SAAS,wDAAwD;KAC5E,CAAC,OAAO3c,KAAK,EAAE;MACd,OAAO,gEAAgE;;EAE3E;EAEA;EACA;EACA;EACA;EACA;EAEA;EACA4c,oBAAoBA,CAAA;IAClB,IAAI,CAACrgB,aAAa,CAACgD,OAAO,CAAEyT,GAAG,IAAKA,GAAG,CAAC6J,WAAW,EAAE,CAAC;IACtD,IAAI,CAACtgB,aAAa,GAAG,EAAE;IACvB,IAAI,IAAI,CAACqY,eAAe,EAAE;MACxBkI,aAAa,CAAC,IAAI,CAAClI,eAAe,CAAC;;IAErC,IAAI,CAACzY,iBAAiB,CAACmD,KAAK,EAAE;IAC9B,IAAI,CAACvD,MAAM,CAACwI,KAAK,CAAC,+BAA+B,CAAC;EACpD;EAEAwY,WAAWA,CAAA;IACT,IAAI,CAACH,oBAAoB,EAAE;EAC7B;;;uBAzoIWhhB,cAAc,EAAAohB,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA,GAAAL,EAAA,CAAAC,QAAA,CAAAD,EAAA,CAAAM,MAAA;IAAA;EAAA;;;aAAd1hB,cAAc;MAAA2hB,OAAA,EAAd3hB,cAAc,CAAA4hB,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
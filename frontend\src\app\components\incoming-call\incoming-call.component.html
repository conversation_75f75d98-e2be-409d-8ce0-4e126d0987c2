<!-- Interface d'appel entrant moderne -->
<div
  *ngIf="incomingCall"
  class="fixed inset-0 z-[9999] bg-black/80 backdrop-blur-sm flex items-center justify-center p-4"
>
  <!-- Carte d'appel entrant -->
  <div
    class="bg-gradient-to-br from-gray-900 via-blue-900 to-purple-900 rounded-3xl p-8 max-w-md w-full mx-auto shadow-2xl border border-blue-500/30 animate-pulse-slow"
  >
    <!-- Header avec type d'appel -->
    <div class="text-center mb-6">
      <div
        class="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-blue-500/20 border border-blue-400/30"
      >
        <i class="fas text-blue-400" [class]="getCallTypeIcon()"></i>
        <span class="text-blue-300 font-medium"
          >{{ getCallTypeText() }} entrant</span
        >
      </div>
    </div>

    <!-- <PERSON><PERSON> de l'appelant avec effets -->
    <div class="relative mb-6 flex justify-center">
      <!-- Cercles d'animation -->
      <div
        class="absolute inset-0 rounded-full border-4 border-blue-400/30 animate-ping"
      ></div>
      <div
        class="absolute -inset-4 rounded-full border-2 border-blue-400/20 animate-ping"
        style="animation-delay: 0.5s"
      ></div>
      <div
        class="absolute -inset-8 rounded-full border border-blue-400/10 animate-ping"
        style="animation-delay: 1s"
      ></div>

      <!-- Avatar principal -->
      <div
        class="relative w-32 h-32 rounded-full overflow-hidden border-4 border-blue-500/50 shadow-2xl"
      >
        <img
          [src]="getCallerAvatar()"
          [alt]="getCallerName()"
          class="w-full h-full object-cover"
        />

        <!-- Overlay avec icône du type d'appel -->
        <div
          class="absolute bottom-0 right-0 w-10 h-10 bg-blue-500 rounded-full flex items-center justify-center border-2 border-white"
        >
          <i class="fas text-white text-sm" [class]="getCallTypeIcon()"></i>
        </div>
      </div>
    </div>

    <!-- Informations de l'appelant -->
    <div class="text-center mb-8">
      <h2 class="text-2xl font-bold text-white mb-2 drop-shadow-lg">
        {{ getCallerName() }}
      </h2>
      <!-- Message différent selon le rôle -->
      <p class="text-blue-300 text-lg" *ngIf="isReceiverUser()">
        vous appelle...
      </p>
      <p class="text-blue-300 text-lg" *ngIf="isSender()">Appel en cours...</p>
    </div>

    <!-- Indicateur vidéo pour appels vidéo -->
    <div *ngIf="isVideoCall()" class="text-center mb-6">
      <div
        class="inline-flex items-center space-x-2 px-4 py-2 rounded-full bg-purple-500/20 border border-purple-400/30"
      >
        <i class="fas fa-video text-purple-400"></i>
        <span class="text-purple-300 text-sm">Appel vidéo avec caméra</span>
      </div>
    </div>

    <!-- Boutons d'action pour RECEIVER -->
    <div *ngIf="isReceiverUser()" class="flex justify-center space-x-8">
      <!-- Bouton Rejeter -->
      <button
        (click)="rejectCall()"
        [disabled]="isProcessing"
        class="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 disabled:bg-red-400 flex items-center justify-center transition-all duration-300 transform hover:scale-110 active:scale-95 shadow-lg shadow-red-500/50"
        title="Rejeter l'appel"
      >
        <i
          class="fas fa-phone-slash text-white text-xl"
          [class.animate-spin]="isProcessing"
        ></i>
      </button>

      <!-- Bouton Accepter -->
      <button
        (click)="acceptCall()"
        [disabled]="isProcessing"
        class="w-16 h-16 rounded-full bg-green-500 hover:bg-green-600 disabled:bg-green-400 flex items-center justify-center transition-all duration-300 transform hover:scale-110 active:scale-95 shadow-lg shadow-green-500/50 animate-pulse"
        title="Accepter l'appel"
      >
        <i
          class="fas fa-phone text-white text-xl"
          [class.animate-spin]="isProcessing"
        ></i>
      </button>
    </div>

    <!-- Boutons d'action pour SENDER -->
    <div *ngIf="isSender()" class="flex justify-center space-x-8">
      <!-- Bouton Annuler l'appel -->
      <button
        (click)="rejectCall()"
        [disabled]="isProcessing"
        class="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 disabled:bg-red-400 flex items-center justify-center transition-all duration-300 transform hover:scale-110 active:scale-95 shadow-lg shadow-red-500/50"
        title="Annuler l'appel"
      >
        <i
          class="fas fa-phone-slash text-white text-xl"
          [class.animate-spin]="isProcessing"
        ></i>
      </button>
    </div>

    <!-- Indicateur de traitement -->
    <div *ngIf="isProcessing" class="text-center mt-6">
      <div class="inline-flex items-center space-x-2 text-blue-300">
        <div
          class="w-4 h-4 border-2 border-blue-400 border-t-transparent rounded-full animate-spin"
        ></div>
        <span class="text-sm">Connexion en cours...</span>
      </div>
    </div>

    <!-- Actions rapides (optionnel) -->
    <div class="flex justify-center space-x-4 mt-6">
      <!-- Message rapide -->
      <button
        class="px-4 py-2 rounded-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white text-sm transition-all duration-200"
        title="Envoyer un message"
      >
        <i class="fas fa-comment mr-2"></i>
        Message
      </button>

      <!-- Rappeler plus tard -->
      <button
        class="px-4 py-2 rounded-full bg-gray-700/50 hover:bg-gray-600/50 text-gray-300 hover:text-white text-sm transition-all duration-200"
        title="Rappeler plus tard"
      >
        <i class="fas fa-clock mr-2"></i>
        Plus tard
      </button>
    </div>
  </div>
</div>

<!-- Styles pour l'animation pulse lente -->
<style>
  @keyframes pulse-slow {
    0%,
    100% {
      transform: scale(1);
      opacity: 1;
    }
    50% {
      transform: scale(1.02);
      opacity: 0.9;
    }
  }

  .animate-pulse-slow {
    animation: pulse-slow 3s ease-in-out infinite;
  }
</style>

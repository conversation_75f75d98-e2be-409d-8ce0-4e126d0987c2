<!-- Interface d'appel actif -->
<div
  class="fixed inset-0 bg-black bg-opacity-90 flex items-center justify-center z-[9998]"
  *ngIf="activeCall && activeCall.status === 'CONNECTED'"
>
  <div
    class="bg-gray-900 rounded-2xl p-8 max-w-md w-full mx-4 text-center shadow-2xl border border-gray-700"
  >
    <!-- Avatar et informations de l'utilisateur -->
    <div class="mb-6">
      <div class="relative inline-block">
        <img
          [src]="getOtherParticipantAvatar()"
          [alt]="getOtherParticipantName()"
          class="w-24 h-24 rounded-full mx-auto mb-4 border-4 border-blue-500 shadow-lg"
        />
        <!-- Indicateur d'appel actif -->
        <div
          class="absolute -bottom-2 -right-2 w-6 h-6 bg-green-500 rounded-full border-2 border-gray-900 animate-pulse"
        ></div>
      </div>

      <h2 class="text-2xl font-bold text-white mb-2">
        {{ getOtherParticipantName() }}
      </h2>

      <p class="text-blue-400 text-lg font-medium">
        {{ getCallStatusText() }}
      </p>

      <!-- Durée de l'appel -->
      <p class="text-gray-400 text-sm mt-2">
        {{ callDuration }}
      </p>
    </div>

    <!-- Zone vidéo pour les appels vidéo -->
    <div *ngIf="isVideoCall()" class="mb-6">
      <div class="relative bg-gray-800 rounded-lg overflow-hidden">
        <!-- Vidéo distante (principale) -->
        <video
          #remoteVideo
          class="w-full h-48 object-cover"
          autoplay
          playsinline
          [muted]="false"
        ></video>

        <!-- Vidéo locale (petite fenêtre) -->
        <video
          #localVideo
          class="absolute top-2 right-2 w-20 h-15 object-cover rounded border-2 border-blue-500"
          autoplay
          playsinline
          [muted]="true"
        ></video>
      </div>
    </div>

    <!-- Zone audio pour les appels audio -->
    <div *ngIf="!isVideoCall()" class="mb-6">
      <!-- Éléments vidéo cachés pour l'audio -->
      <video
        #remoteVideo
        style="display: none"
        autoplay
        playsinline
        [muted]="false"
      ></video>
      <video
        #localVideo
        style="display: none"
        autoplay
        playsinline
        [muted]="true"
      ></video>

      <!-- Visualisation audio -->
      <div class="flex justify-center items-center space-x-2 mb-4">
        <div class="w-2 h-8 bg-blue-500 rounded animate-pulse"></div>
        <div
          class="w-2 h-12 bg-blue-400 rounded animate-pulse"
          style="animation-delay: 0.1s"
        ></div>
        <div
          class="w-2 h-6 bg-blue-500 rounded animate-pulse"
          style="animation-delay: 0.2s"
        ></div>
        <div
          class="w-2 h-10 bg-blue-400 rounded animate-pulse"
          style="animation-delay: 0.3s"
        ></div>
        <div
          class="w-2 h-4 bg-blue-500 rounded animate-pulse"
          style="animation-delay: 0.4s"
        ></div>
      </div>
    </div>

    <!-- Contrôles d'appel -->
    <div class="flex justify-center space-x-6 mt-8">
      <!-- Bouton Microphone -->
      <button
        (click)="toggleMicrophone()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
        [class]="
          isAudioMuted
            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50'
            : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'
        "
        [title]="isAudioMuted ? 'Activer le micro' : 'Couper le micro'"
      >
        <i
          class="fas text-white text-xl"
          [class]="isAudioMuted ? 'fa-microphone-slash' : 'fa-microphone'"
        ></i>
      </button>

      <!-- Bouton Caméra (seulement pour appels vidéo) -->
      <button
        *ngIf="isVideoCall()"
        (click)="toggleCamera()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
        [class]="
          isVideoMuted
            ? 'bg-red-500 hover:bg-red-600 shadow-red-500/50'
            : 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'
        "
        [title]="isVideoMuted ? 'Activer la caméra' : 'Désactiver la caméra'"
      >
        <i
          class="fas text-white text-xl"
          [class]="isVideoMuted ? 'fa-video-slash' : 'fa-video'"
        ></i>
      </button>

      <!-- Bouton Haut-parleur -->
      <button
        (click)="toggleSpeaker()"
        class="w-16 h-16 rounded-full flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg"
        [class]="
          isSpeakerOn
            ? 'bg-blue-500 hover:bg-blue-600 shadow-blue-500/50'
            : 'bg-gray-600 hover:bg-gray-700 shadow-gray-600/50'
        "
        [title]="
          isSpeakerOn ? 'Désactiver le haut-parleur' : 'Activer le haut-parleur'
        "
      >
        <i
          class="fas text-white text-xl"
          [class]="isSpeakerOn ? 'fa-volume-up' : 'fa-volume-mute'"
        ></i>
      </button>

      <!-- Bouton Raccrocher -->
      <button
        (click)="endCall()"
        class="w-16 h-16 rounded-full bg-red-500 hover:bg-red-600 flex items-center justify-center transition-all duration-300 transform hover:scale-110 shadow-lg shadow-red-500/50"
        title="Raccrocher"
      >
        <i class="fas fa-phone-slash text-white text-xl"></i>
      </button>
    </div>
  </div>
</div>

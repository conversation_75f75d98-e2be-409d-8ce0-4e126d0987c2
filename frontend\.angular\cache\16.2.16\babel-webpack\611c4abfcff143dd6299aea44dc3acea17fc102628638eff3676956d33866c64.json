{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { CallType } from '../../models/message.model';\nexport let IncomingCallComponent = class IncomingCallComponent {\n  constructor(callService, logger, authService) {\n    this.callService = callService;\n    this.logger = logger;\n    this.authService = authService;\n    this.incomingCall = null;\n    this.isProcessing = false;\n    this.currentUserId = null;\n    this.subscriptions = [];\n    // Exposer les énums au template\n    this.CallType = CallType;\n  }\n  ngOnInit() {\n    // Obtenir l'ID de l'utilisateur actuel\n    this.currentUserId = this.getCurrentUserId();\n    // S'abonner aux appels entrants\n    const incomingCallSub = this.callService.incomingCall$.subscribe(call => {\n      // Vérifier si l'utilisateur actuel est le receiver (pas le sender)\n      if (call && this.isReceiver(call)) {\n        this.incomingCall = call;\n        this.isProcessing = false;\n        console.log('📞 [IncomingCall] Incoming call received for receiver:', {\n          callId: call.id,\n          caller: call.caller?.username,\n          type: call.type,\n          currentUserId: this.currentUserId,\n          recipientId: call.recipient?.id || call.recipient?._id\n        });\n      } else if (call) {\n        // Si l'utilisateur est le sender, ne pas afficher le template\n        console.log('📞 [IncomingCall] Call initiated by current user (sender), hiding template');\n        this.incomingCall = null;\n      } else {\n        this.incomingCall = null;\n      }\n    });\n    this.subscriptions.push(incomingCallSub);\n  }\n  ngOnDestroy() {\n    this.subscriptions.forEach(sub => sub.unsubscribe());\n  }\n  /**\n   * Accepte l'appel entrant\n   */\n  acceptCall() {\n    if (!this.incomingCall || this.isProcessing) return;\n    console.log('✅ [IncomingCall] Accepting call:', this.incomingCall.id);\n    this.isProcessing = true;\n    this.callService.acceptCall(this.incomingCall).subscribe({\n      next: call => {\n        console.log('✅ [IncomingCall] Call accepted successfully:', call);\n        this.isProcessing = false;\n        // L'interface d'appel actif va automatiquement s'afficher\n      },\n\n      error: error => {\n        console.error('❌ [IncomingCall] Error accepting call:', error);\n        this.logger.error('Error accepting call:', error);\n        this.isProcessing = false;\n        // Optionnel : Afficher un message d'erreur à l'utilisateur\n        alert(\"Erreur lors de l'acceptation de l'appel. Veuillez réessayer.\");\n      }\n    });\n  }\n  /**\n   * Rejette l'appel entrant\n   */\n  rejectCall() {\n    if (!this.incomingCall || this.isProcessing) return;\n    console.log('❌ [IncomingCall] Rejecting call:', this.incomingCall.id);\n    this.isProcessing = true;\n    this.callService.rejectCall(this.incomingCall.id, 'User rejected').subscribe({\n      next: result => {\n        console.log('✅ [IncomingCall] Call rejected successfully:', result);\n        this.isProcessing = false;\n      },\n      error: error => {\n        console.error('❌ [IncomingCall] Error rejecting call:', error);\n        this.logger.error('Error rejecting call:', error);\n        this.isProcessing = false;\n      }\n    });\n  }\n  /**\n   * Obtient le nom de l'appelant\n   */\n  getCallerName() {\n    return this.incomingCall?.caller?.username || 'Utilisateur inconnu';\n  }\n  /**\n   * Obtient l'avatar de l'appelant\n   */\n  getCallerAvatar() {\n    return this.incomingCall?.caller?.image || '/assets/images/default-avatar.png';\n  }\n  /**\n   * Obtient le type d'appel (audio/vidéo)\n   */\n  getCallTypeText() {\n    if (!this.incomingCall) return '';\n    return this.incomingCall.type === CallType.VIDEO ? 'Appel vidéo' : 'Appel audio';\n  }\n  /**\n   * Obtient l'icône du type d'appel\n   */\n  getCallTypeIcon() {\n    if (!this.incomingCall) return 'fa-phone';\n    return this.incomingCall.type === CallType.VIDEO ? 'fa-video' : 'fa-phone';\n  }\n  /**\n   * Vérifie si c'est un appel vidéo\n   */\n  isVideoCall() {\n    return this.incomingCall?.type === CallType.VIDEO;\n  }\n  /**\n   * Obtient l'ID de l'utilisateur actuel depuis localStorage\n   */\n  getCurrentUserId() {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        return null;\n      }\n      const user = JSON.parse(userString);\n      return user._id || user.id || user.userId || null;\n    } catch (error) {\n      console.error('Error getting current user ID:', error);\n      return null;\n    }\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  isReceiver(call) {\n    if (!call || !this.currentUserId) return false;\n    const recipientId = call.recipient?.id || call.recipient?._id;\n    const callerId = call.caller?.id || call.caller?._id;\n    // L'utilisateur est le receiver si son ID correspond au recipient\n    const isRecipient = String(recipientId) === String(this.currentUserId);\n    console.log('📞 [IncomingCall] Checking if user is receiver:', {\n      currentUserId: this.currentUserId,\n      recipientId,\n      callerId,\n      isRecipient\n    });\n    return isRecipient;\n  }\n};\nIncomingCallComponent = __decorate([Component({\n  selector: 'app-incoming-call',\n  templateUrl: './incoming-call.component.html',\n  styleUrls: ['./incoming-call.component.css']\n})], IncomingCallComponent);", "map": {"version": 3, "names": ["Component", "CallType", "IncomingCallComponent", "constructor", "callService", "logger", "authService", "incomingCall", "isProcessing", "currentUserId", "subscriptions", "ngOnInit", "getCurrentUserId", "incomingCallSub", "incomingCall$", "subscribe", "call", "isReceiver", "console", "log", "callId", "id", "caller", "username", "type", "recipientId", "recipient", "_id", "push", "ngOnDestroy", "for<PERSON>ach", "sub", "unsubscribe", "acceptCall", "next", "error", "alert", "rejectCall", "result", "getCallerName", "getCallerAvatar", "image", "getCallTypeText", "VIDEO", "getCallTypeIcon", "isVideoCall", "userString", "localStorage", "getItem", "user", "JSON", "parse", "userId", "callerId", "isRecipient", "String", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\incoming-call\\incoming-call.component.ts"], "sourcesContent": ["import { Component, OnInit, OnD<PERSON>roy } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\nimport { IncomingCall, CallType } from '../../models/message.model';\n\n@Component({\n  selector: 'app-incoming-call',\n  templateUrl: './incoming-call.component.html',\n  styleUrls: ['./incoming-call.component.css'],\n})\nexport class IncomingCallComponent implements OnInit, OnDestroy {\n  incomingCall: IncomingCall | null = null;\n  isProcessing: boolean = false;\n  currentUserId: string | null = null;\n\n  private subscriptions: Subscription[] = [];\n\n  // Exposer les énums au template\n  CallType = CallType;\n\n  constructor(\n    private callService: CallService,\n    private logger: LoggerService,\n    private authService: AuthService\n  ) {}\n\n  ngOnInit(): void {\n    // Obtenir l'ID de l'utilisateur actuel\n    this.currentUserId = this.getCurrentUserId();\n\n    // S'abonner aux appels entrants\n    const incomingCallSub = this.callService.incomingCall$.subscribe(\n      (call: any) => {\n        // Vérifier si l'utilisateur actuel est le receiver (pas le sender)\n        if (call && this.isReceiver(call)) {\n          this.incomingCall = call;\n          this.isProcessing = false;\n\n          console.log(\n            '📞 [IncomingCall] Incoming call received for receiver:',\n            {\n              callId: call.id,\n              caller: call.caller?.username,\n              type: call.type,\n              currentUserId: this.currentUserId,\n              recipientId: call.recipient?.id || call.recipient?._id,\n            }\n          );\n        } else if (call) {\n          // Si l'utilisateur est le sender, ne pas afficher le template\n          console.log(\n            '📞 [IncomingCall] Call initiated by current user (sender), hiding template'\n          );\n          this.incomingCall = null;\n        } else {\n          this.incomingCall = null;\n        }\n      }\n    );\n\n    this.subscriptions.push(incomingCallSub);\n  }\n\n  ngOnDestroy(): void {\n    this.subscriptions.forEach((sub) => sub.unsubscribe());\n  }\n\n  /**\n   * Accepte l'appel entrant\n   */\n  acceptCall(): void {\n    if (!this.incomingCall || this.isProcessing) return;\n\n    console.log('✅ [IncomingCall] Accepting call:', this.incomingCall.id);\n    this.isProcessing = true;\n\n    this.callService.acceptCall(this.incomingCall).subscribe({\n      next: (call: any) => {\n        console.log('✅ [IncomingCall] Call accepted successfully:', call);\n        this.isProcessing = false;\n        // L'interface d'appel actif va automatiquement s'afficher\n      },\n      error: (error: any) => {\n        console.error('❌ [IncomingCall] Error accepting call:', error);\n        this.logger.error('Error accepting call:', error);\n        this.isProcessing = false;\n\n        // Optionnel : Afficher un message d'erreur à l'utilisateur\n        alert(\"Erreur lors de l'acceptation de l'appel. Veuillez réessayer.\");\n      },\n    });\n  }\n\n  /**\n   * Rejette l'appel entrant\n   */\n  rejectCall(): void {\n    if (!this.incomingCall || this.isProcessing) return;\n\n    console.log('❌ [IncomingCall] Rejecting call:', this.incomingCall.id);\n    this.isProcessing = true;\n\n    this.callService\n      .rejectCall(this.incomingCall.id, 'User rejected')\n      .subscribe({\n        next: (result: any) => {\n          console.log('✅ [IncomingCall] Call rejected successfully:', result);\n          this.isProcessing = false;\n        },\n        error: (error: any) => {\n          console.error('❌ [IncomingCall] Error rejecting call:', error);\n          this.logger.error('Error rejecting call:', error);\n          this.isProcessing = false;\n        },\n      });\n  }\n\n  /**\n   * Obtient le nom de l'appelant\n   */\n  getCallerName(): string {\n    return this.incomingCall?.caller?.username || 'Utilisateur inconnu';\n  }\n\n  /**\n   * Obtient l'avatar de l'appelant\n   */\n  getCallerAvatar(): string {\n    return (\n      this.incomingCall?.caller?.image || '/assets/images/default-avatar.png'\n    );\n  }\n\n  /**\n   * Obtient le type d'appel (audio/vidéo)\n   */\n  getCallTypeText(): string {\n    if (!this.incomingCall) return '';\n\n    return this.incomingCall.type === CallType.VIDEO\n      ? 'Appel vidéo'\n      : 'Appel audio';\n  }\n\n  /**\n   * Obtient l'icône du type d'appel\n   */\n  getCallTypeIcon(): string {\n    if (!this.incomingCall) return 'fa-phone';\n\n    return this.incomingCall.type === CallType.VIDEO ? 'fa-video' : 'fa-phone';\n  }\n\n  /**\n   * Vérifie si c'est un appel vidéo\n   */\n  isVideoCall(): boolean {\n    return this.incomingCall?.type === CallType.VIDEO;\n  }\n\n  /**\n   * Obtient l'ID de l'utilisateur actuel depuis localStorage\n   */\n  private getCurrentUserId(): string | null {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        return null;\n      }\n      const user = JSON.parse(userString);\n      return user._id || user.id || user.userId || null;\n    } catch (error) {\n      console.error('Error getting current user ID:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  private isReceiver(call: IncomingCall): boolean {\n    if (!call || !this.currentUserId) return false;\n\n    const recipientId = call.recipient?.id || call.recipient?._id;\n    const callerId = call.caller?.id || call.caller?._id;\n\n    // L'utilisateur est le receiver si son ID correspond au recipient\n    const isRecipient = String(recipientId) === String(this.currentUserId);\n\n    console.log('📞 [IncomingCall] Checking if user is receiver:', {\n      currentUserId: this.currentUserId,\n      recipientId,\n      callerId,\n      isRecipient,\n    });\n\n    return isRecipient;\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAA2B,eAAe;AAI5D,SAAuBC,QAAQ,QAAQ,4BAA4B;AAO5D,WAAMC,qBAAqB,GAA3B,MAAMA,qBAAqB;EAUhCC,YACUC,WAAwB,EACxBC,MAAqB,EACrBC,WAAwB;IAFxB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAZrB,KAAAC,YAAY,GAAwB,IAAI;IACxC,KAAAC,YAAY,GAAY,KAAK;IAC7B,KAAAC,aAAa,GAAkB,IAAI;IAE3B,KAAAC,aAAa,GAAmB,EAAE;IAE1C;IACA,KAAAT,QAAQ,GAAGA,QAAQ;EAMhB;EAEHU,QAAQA,CAAA;IACN;IACA,IAAI,CAACF,aAAa,GAAG,IAAI,CAACG,gBAAgB,EAAE;IAE5C;IACA,MAAMC,eAAe,GAAG,IAAI,CAACT,WAAW,CAACU,aAAa,CAACC,SAAS,CAC7DC,IAAS,IAAI;MACZ;MACA,IAAIA,IAAI,IAAI,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,EAAE;QACjC,IAAI,CAACT,YAAY,GAAGS,IAAI;QACxB,IAAI,CAACR,YAAY,GAAG,KAAK;QAEzBU,OAAO,CAACC,GAAG,CACT,wDAAwD,EACxD;UACEC,MAAM,EAAEJ,IAAI,CAACK,EAAE;UACfC,MAAM,EAAEN,IAAI,CAACM,MAAM,EAAEC,QAAQ;UAC7BC,IAAI,EAAER,IAAI,CAACQ,IAAI;UACff,aAAa,EAAE,IAAI,CAACA,aAAa;UACjCgB,WAAW,EAAET,IAAI,CAACU,SAAS,EAAEL,EAAE,IAAIL,IAAI,CAACU,SAAS,EAAEC;SACpD,CACF;OACF,MAAM,IAAIX,IAAI,EAAE;QACf;QACAE,OAAO,CAACC,GAAG,CACT,4EAA4E,CAC7E;QACD,IAAI,CAACZ,YAAY,GAAG,IAAI;OACzB,MAAM;QACL,IAAI,CAACA,YAAY,GAAG,IAAI;;IAE5B,CAAC,CACF;IAED,IAAI,CAACG,aAAa,CAACkB,IAAI,CAACf,eAAe,CAAC;EAC1C;EAEAgB,WAAWA,CAAA;IACT,IAAI,CAACnB,aAAa,CAACoB,OAAO,CAAEC,GAAG,IAAKA,GAAG,CAACC,WAAW,EAAE,CAAC;EACxD;EAEA;;;EAGAC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC1B,YAAY,IAAI,IAAI,CAACC,YAAY,EAAE;IAE7CU,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACZ,YAAY,CAACc,EAAE,CAAC;IACrE,IAAI,CAACb,YAAY,GAAG,IAAI;IAExB,IAAI,CAACJ,WAAW,CAAC6B,UAAU,CAAC,IAAI,CAAC1B,YAAY,CAAC,CAACQ,SAAS,CAAC;MACvDmB,IAAI,EAAGlB,IAAS,IAAI;QAClBE,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEH,IAAI,CAAC;QACjE,IAAI,CAACR,YAAY,GAAG,KAAK;QACzB;MACF,CAAC;;MACD2B,KAAK,EAAGA,KAAU,IAAI;QACpBjB,OAAO,CAACiB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC3B,YAAY,GAAG,KAAK;QAEzB;QACA4B,KAAK,CAAC,8DAA8D,CAAC;MACvE;KACD,CAAC;EACJ;EAEA;;;EAGAC,UAAUA,CAAA;IACR,IAAI,CAAC,IAAI,CAAC9B,YAAY,IAAI,IAAI,CAACC,YAAY,EAAE;IAE7CU,OAAO,CAACC,GAAG,CAAC,kCAAkC,EAAE,IAAI,CAACZ,YAAY,CAACc,EAAE,CAAC;IACrE,IAAI,CAACb,YAAY,GAAG,IAAI;IAExB,IAAI,CAACJ,WAAW,CACbiC,UAAU,CAAC,IAAI,CAAC9B,YAAY,CAACc,EAAE,EAAE,eAAe,CAAC,CACjDN,SAAS,CAAC;MACTmB,IAAI,EAAGI,MAAW,IAAI;QACpBpB,OAAO,CAACC,GAAG,CAAC,8CAA8C,EAAEmB,MAAM,CAAC;QACnE,IAAI,CAAC9B,YAAY,GAAG,KAAK;MAC3B,CAAC;MACD2B,KAAK,EAAGA,KAAU,IAAI;QACpBjB,OAAO,CAACiB,KAAK,CAAC,wCAAwC,EAAEA,KAAK,CAAC;QAC9D,IAAI,CAAC9B,MAAM,CAAC8B,KAAK,CAAC,uBAAuB,EAAEA,KAAK,CAAC;QACjD,IAAI,CAAC3B,YAAY,GAAG,KAAK;MAC3B;KACD,CAAC;EACN;EAEA;;;EAGA+B,aAAaA,CAAA;IACX,OAAO,IAAI,CAAChC,YAAY,EAAEe,MAAM,EAAEC,QAAQ,IAAI,qBAAqB;EACrE;EAEA;;;EAGAiB,eAAeA,CAAA;IACb,OACE,IAAI,CAACjC,YAAY,EAAEe,MAAM,EAAEmB,KAAK,IAAI,mCAAmC;EAE3E;EAEA;;;EAGAC,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACnC,YAAY,EAAE,OAAO,EAAE;IAEjC,OAAO,IAAI,CAACA,YAAY,CAACiB,IAAI,KAAKvB,QAAQ,CAAC0C,KAAK,GAC5C,aAAa,GACb,aAAa;EACnB;EAEA;;;EAGAC,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACrC,YAAY,EAAE,OAAO,UAAU;IAEzC,OAAO,IAAI,CAACA,YAAY,CAACiB,IAAI,KAAKvB,QAAQ,CAAC0C,KAAK,GAAG,UAAU,GAAG,UAAU;EAC5E;EAEA;;;EAGAE,WAAWA,CAAA;IACT,OAAO,IAAI,CAACtC,YAAY,EAAEiB,IAAI,KAAKvB,QAAQ,CAAC0C,KAAK;EACnD;EAEA;;;EAGQ/B,gBAAgBA,CAAA;IACtB,IAAI;MACF,MAAMkC,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAI,CAACF,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtE,OAAO,IAAI;;MAEb,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnC,OAAOG,IAAI,CAACtB,GAAG,IAAIsB,IAAI,CAAC5B,EAAE,IAAI4B,IAAI,CAACG,MAAM,IAAI,IAAI;KAClD,CAAC,OAAOjB,KAAK,EAAE;MACdjB,OAAO,CAACiB,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;MACtD,OAAO,IAAI;;EAEf;EAEA;;;EAGQlB,UAAUA,CAACD,IAAkB;IACnC,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACP,aAAa,EAAE,OAAO,KAAK;IAE9C,MAAMgB,WAAW,GAAGT,IAAI,CAACU,SAAS,EAAEL,EAAE,IAAIL,IAAI,CAACU,SAAS,EAAEC,GAAG;IAC7D,MAAM0B,QAAQ,GAAGrC,IAAI,CAACM,MAAM,EAAED,EAAE,IAAIL,IAAI,CAACM,MAAM,EAAEK,GAAG;IAEpD;IACA,MAAM2B,WAAW,GAAGC,MAAM,CAAC9B,WAAW,CAAC,KAAK8B,MAAM,CAAC,IAAI,CAAC9C,aAAa,CAAC;IAEtES,OAAO,CAACC,GAAG,CAAC,iDAAiD,EAAE;MAC7DV,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCgB,WAAW;MACX4B,QAAQ;MACRC;KACD,CAAC;IAEF,OAAOA,WAAW;EACpB;CACD;AA5LYpD,qBAAqB,GAAAsD,UAAA,EALjCxD,SAAS,CAAC;EACTyD,QAAQ,EAAE,mBAAmB;EAC7BC,WAAW,EAAE,gCAAgC;EAC7CC,SAAS,EAAE,CAAC,+BAA+B;CAC5C,CAAC,C,EACWzD,qBAAqB,CA4LjC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
import {
  Component,
  On<PERSON>nit,
  <PERSON><PERSON><PERSON><PERSON>,
  ViewChild,
  ElementRef,
  AfterViewInit,
} from '@angular/core';
import { Subscription } from 'rxjs';
import { CallService } from '../../services/call.service';
import { LoggerService } from '../../services/logger.service';
import { Call, CallType, CallStatus } from '../../models/message.model';

@Component({
  selector: 'app-active-call',
  templateUrl: './active-call.component.html',
  styleUrls: ['./active-call.component.css'],
})
export class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {
  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;
  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;

  activeCall: Call | null = null;
  callDuration = '00:00';
  currentUserId: string | null = null;

  // États des contrôles
  isAudioMuted = false;
  isVideoMuted = false;
  isSpeakerOn = true;

  private subscription: Subscription = new Subscription();
  private callStartTime: Date | null = null;
  private durationInterval: any;

  constructor(private callService: CallService, private logger: LoggerService) {
    this.logger.info('ActiveCall', '🚀 ActiveCall component initialized');
  }

  ngOnInit(): void {
    this.logger.info('ActiveCall', '📞 Setting up active call subscription');

    // Obtenir l'ID de l'utilisateur actuel
    this.currentUserId = this.getCurrentUserId();

    console.log(
      '🔍 [ActiveCall] Component initialized with currentUserId:',
      this.currentUserId
    );

    // S'abonner aux appels actifs
    this.subscription.add(
      this.callService.activeCall$.subscribe((call) => {
        console.log('📞 [ActiveCall] Received call data:', {
          call,
          callType: typeof call,
          callValue: call,
          isNull: call === null,
          isUndefined: call === undefined,
          isFalsy: !call,
          currentUserId: this.currentUserId,
          hasCall: !!call,
          callStatus: call?.status,
          callerId: call?.caller?.id || call?.caller?._id,
          recipientId: call?.recipient?.id || call?.recipient?._id,
        });

        if (call === null || call === undefined) {
          console.log(
            '📞 [ActiveCall] Call is null/undefined, hiding template'
          );
          this.activeCall = null;
          this.stopCallTimer();
          return;
        }

        // TEMPORAIRE : Afficher tous les appels pour diagnostiquer
        console.log(
          '📞 [ActiveCall] DIAGNOSTIC MODE - Showing all active calls'
        );
        this.activeCall = call;

        if (call.status === CallStatus.CONNECTED) {
          console.log('📞 [ActiveCall] Call connected, starting timer');
          this.startCallTimer();
        } else {
          console.log('📞 [ActiveCall] Call not connected, stopping timer');
          this.stopCallTimer();
        }

        // Vérifier si l'utilisateur actuel est le receiver (pas le sender)
        // if (call && this.isReceiver(call)) {
        //   this.activeCall = call;

        //   if (call.status === CallStatus.CONNECTED) {
        //     this.logger.info('ActiveCall', '✅ Call connected, starting timer');
        //     this.startCallTimer();
        //   } else {
        //     this.logger.debug(
        //       'ActiveCall',
        //       '⏹️ Call not connected, stopping timer'
        //     );
        //     this.stopCallTimer();
        //   }
        // } else if (call) {
        //   // Si l'utilisateur est le sender, ne pas afficher le template
        //   this.logger.info(
        //     'ActiveCall',
        //     '📞 Call initiated by current user (sender), hiding active call template'
        //   );
        //   this.activeCall = null;
        //   this.stopCallTimer();
        // } else {
        //   this.activeCall = null;
        //   this.stopCallTimer();
        // }
      })
    );
  }

  ngAfterViewInit(): void {
    // Configurer les éléments vidéo après l'initialisation de la vue
    if (this.localVideo && this.remoteVideo) {
      this.callService.setVideoElements(
        this.localVideo.nativeElement,
        this.remoteVideo.nativeElement
      );
    }
  }

  ngOnDestroy(): void {
    this.subscription.unsubscribe();
    this.stopCallTimer();
  }

  /**
   * Démarre le timer de durée d'appel
   */
  private startCallTimer(): void {
    this.callStartTime = new Date();
    this.durationInterval = setInterval(() => {
      if (this.callStartTime) {
        const now = new Date();
        const diff = now.getTime() - this.callStartTime.getTime();
        const minutes = Math.floor(diff / 60000);
        const seconds = Math.floor((diff % 60000) / 1000);
        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds
          .toString()
          .padStart(2, '0')}`;
      }
    }, 1000);
  }

  /**
   * Arrête le timer de durée d'appel
   */
  private stopCallTimer(): void {
    if (this.durationInterval) {
      clearInterval(this.durationInterval);
      this.durationInterval = null;
    }
    this.callDuration = '00:00';
    this.callStartTime = null;
  }

  /**
   * Bascule l'état du microphone
   */
  toggleMicrophone(): void {
    this.isAudioMuted = !this.isAudioMuted;
    console.log('Microphone:', this.isAudioMuted ? 'Muted' : 'Unmuted');
  }

  /**
   * Bascule l'état de la caméra
   */
  toggleCamera(): void {
    this.isVideoMuted = !this.isVideoMuted;
    console.log('Camera:', this.isVideoMuted ? 'Muted' : 'Unmuted');
  }

  /**
   * Bascule l'état du haut-parleur
   */
  toggleSpeaker(): void {
    this.isSpeakerOn = !this.isSpeakerOn;
    console.log('Speaker:', this.isSpeakerOn ? 'On' : 'Off');
  }

  /**
   * Termine l'appel
   */
  endCall(): void {
    if (!this.activeCall) return;

    console.log('Ending call:', this.activeCall.id);

    this.callService.endCall(this.activeCall.id).subscribe({
      next: () => {
        console.log('Call ended successfully');
      },
      error: (error: any) => {
        console.error('Error ending call:', error);
      },
    });
  }

  /**
   * Vérifie si c'est un appel vidéo
   */
  isVideoCall(): boolean {
    return this.activeCall?.type === CallType.VIDEO;
  }

  /**
   * Obtient le texte du statut de l'appel
   */
  getCallStatusText(): string {
    if (!this.activeCall) return '';

    switch (this.activeCall.status) {
      case CallStatus.RINGING:
        return 'Sonnerie...';
      case CallStatus.CONNECTED:
        return 'Connecté';
      case CallStatus.ENDED:
        return 'Terminé';
      case CallStatus.MISSED:
        return 'Manqué';
      case CallStatus.REJECTED:
        return 'Rejeté';
      case CallStatus.FAILED:
        return 'Échec';
      default:
        return 'En cours...';
    }
  }

  /**
   * Obtient le nom de l'autre participant
   */
  getOtherParticipantName(): string {
    if (!this.activeCall) return '';

    return (
      this.activeCall.recipient?.username ||
      this.activeCall.caller?.username ||
      'Utilisateur'
    );
  }

  /**
   * Obtient l'avatar de l'autre participant
   */
  getOtherParticipantAvatar(): string {
    if (!this.activeCall) return '/assets/images/default-avatar.png';

    return (
      this.activeCall.recipient?.image ||
      this.activeCall.caller?.image ||
      '/assets/images/default-avatar.png'
    );
  }

  /**
   * Obtient l'ID de l'utilisateur actuel depuis localStorage
   */
  private getCurrentUserId(): string | null {
    try {
      const userString = localStorage.getItem('user');
      if (!userString || userString === 'null' || userString === 'undefined') {
        return null;
      }
      const user = JSON.parse(userString);
      return user._id || user.id || user.userId || null;
    } catch (error) {
      this.logger.error('ActiveCall', 'Error getting current user ID:', error);
      return null;
    }
  }

  /**
   * Vérifie si l'utilisateur actuel est le receiver de l'appel
   */
  private isReceiver(call: Call): boolean {
    if (!call || !this.currentUserId) return false;

    const recipientId = call.recipient?.id || call.recipient?._id;
    const callerId = call.caller?.id || call.caller?._id;

    // L'utilisateur est le receiver si son ID correspond au recipient
    const isRecipient = String(recipientId) === String(this.currentUserId);

    this.logger.debug('ActiveCall', 'Checking if user is receiver:', {
      currentUserId: this.currentUserId,
      recipientId,
      callerId,
      isRecipient,
    });

    return isRecipient;
  }
}

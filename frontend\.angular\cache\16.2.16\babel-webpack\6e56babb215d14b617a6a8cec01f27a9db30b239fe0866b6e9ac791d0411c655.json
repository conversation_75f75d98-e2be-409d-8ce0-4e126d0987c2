{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/OneDrive/Bureau/Project PI/devBridge/frontend/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject, Observable, throwError, of } from 'rxjs';\nimport { map, catchError } from 'rxjs/operators';\nimport { CallType, CallStatus } from '../models/message.model';\nimport { INITIATE_CALL_MUTATION, ACCEPT_CALL_MUTATION, REJECT_CALL_MUTATION, END_CALL_MUTATION, TOGGLE_CALL_MEDIA_MUTATION, INCOMING_CALL_SUBSCRIPTION, CALL_STATUS_CHANGED_SUBSCRIPTION, CALL_SIGNAL_SUBSCRIPTION, SEND_CALL_SIGNAL_MUTATION } from '../graphql/message.graphql';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"apollo-angular\";\nimport * as i2 from \"./logger.service\";\nexport class CallService {\n  constructor(apollo, logger) {\n    this.apollo = apollo;\n    this.logger = logger;\n    // Observables pour l'état des appels\n    this.activeCall = new BehaviorSubject(null);\n    this.incomingCall = new BehaviorSubject(null);\n    this.callSignals = new BehaviorSubject(null);\n    // Observables publics\n    this.activeCall$ = this.activeCall.asObservable();\n    this.incomingCall$ = this.incomingCall.asObservable();\n    this.callSignals$ = this.callSignals.asObservable();\n    // État des médias\n    this.isAudioEnabled = true;\n    this.isVideoEnabled = true;\n    // Gestion des sons\n    this.sounds = {};\n    this.isPlaying = {};\n    // WebRTC\n    this.peerConnection = null;\n    this.localStream = null;\n    this.remoteStream = null;\n    this.localVideoElement = null;\n    this.remoteVideoElement = null;\n    this.currentCallId = null;\n    this.webrtcExchangeStarted = false;\n    // Configuration WebRTC\n    this.rtcConfig = {\n      iceServers: [{\n        urls: 'stun:stun.l.google.com:19302'\n      }, {\n        urls: 'stun:stun1.l.google.com:19302'\n      }, {\n        urls: 'stun:stun2.l.google.com:19302'\n      }]\n    };\n    this.logger.info('CallService', '🚀 Initializing CallService...');\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n    this.logger.info('CallService', '✅ CallService initialized successfully');\n  }\n  ngOnDestroy() {\n    this.logger.info('CallService', '🔄 Destroying CallService...');\n    this.cleanupWebRTC();\n    this.stopAllSounds();\n  }\n  /**\n   * Initialise les sons\n   */\n  initializeSounds() {\n    this.logger.debug('CallService', 'Initializing sounds...');\n    this.createSyntheticSounds();\n  }\n  /**\n   * Crée des sons synthétiques\n   */\n  createSyntheticSounds() {\n    this.logger.debug('CallService', 'Creating synthetic sounds...');\n    // Créer des sons synthétiques pour éviter les problèmes de fichiers manquants\n    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);\n    this.createSyntheticSound('call-connected', [523.25, 659.25, 783.99], 0.8, false);\n    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);\n  }\n  /**\n   * Crée un son synthétique\n   */\n  createSyntheticSound(name, frequencies, duration, loop) {\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext || window.webkitAudioContext)();\n      // Créer un buffer audio\n      const sampleRate = audioContext.sampleRate;\n      const frameCount = sampleRate * duration;\n      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);\n      const channelData = buffer.getChannelData(0);\n      // Générer le son\n      for (let i = 0; i < frameCount; i++) {\n        let sample = 0;\n        frequencies.forEach((freq, index) => {\n          const amplitude = 0.3 / frequencies.length;\n          const phase = i / sampleRate * freq * 2 * Math.PI;\n          sample += Math.sin(phase) * amplitude;\n        });\n        // Appliquer une enveloppe pour éviter les clics\n        const envelope = Math.sin(i / frameCount * Math.PI);\n        channelData[i] = sample * envelope;\n      }\n      // Créer un élément audio factice pour l'interface\n      const audio = new Audio();\n      audio.loop = loop;\n      // Stocker une fonction de lecture personnalisée\n      audio.customPlay = () => {\n        const source = audioContext.createBufferSource();\n        source.buffer = buffer;\n        source.loop = loop;\n        source.connect(audioContext.destination);\n        source.start();\n        if (!loop) {\n          setTimeout(() => {\n            this.isPlaying[name] = false;\n          }, duration * 1000);\n        }\n        return source;\n      };\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n      this.logger.debug('CallService', `Synthetic sound '${name}' created successfully`);\n    } catch (error) {\n      this.logger.error('CallService', `Error creating synthetic sound '${name}':`, error);\n    }\n  }\n  /**\n   * Joue un son\n   */\n  play(name, loop = false) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('CallService', `Sound '${name}' not found`);\n        return;\n      }\n      if (!this.isPlaying[name]) {\n        if (sound.customPlay) {\n          sound.currentSource = sound.customPlay();\n          this.isPlaying[name] = true;\n          this.logger.debug('CallService', `Playing synthetic sound: ${name}`);\n        } else {\n          sound.currentTime = 0;\n          sound.loop = loop;\n          sound.play().catch(error => {\n            this.logger.error('CallService', `Error playing sound '${name}':`, error);\n          });\n          this.isPlaying[name] = true;\n        }\n      }\n    } catch (error) {\n      this.logger.error('CallService', `Error in play method for '${name}':`, error);\n    }\n  }\n  /**\n   * Arrête un son\n   */\n  stop(name) {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n      if (this.isPlaying[name]) {\n        if (sound.currentSource) {\n          sound.currentSource.stop();\n          sound.currentSource = null;\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n        this.logger.debug('CallService', `Stopped sound: ${name}`);\n      }\n    } catch (error) {\n      this.logger.error('CallService', `Error stopping sound '${name}':`, error);\n    }\n  }\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds() {\n    Object.keys(this.sounds).forEach(name => {\n      this.stop(name);\n    });\n  }\n  /**\n   * Initialise les subscriptions\n   */\n  initializeSubscriptions() {\n    this.logger.debug('CallService', 'Initializing subscriptions...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n  /**\n   * S'abonne aux appels entrants\n   */\n  subscribeToIncomingCalls() {\n    this.logger.debug('CallService', 'Setting up incoming calls subscription...');\n    this.apollo.subscribe({\n      query: INCOMING_CALL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.incomingCall) {\n          this.logger.info('CallService', 'Incoming call received:', data.incomingCall);\n          this.handleIncomingCall(data.incomingCall);\n        }\n        if (errors) {\n          this.logger.error('CallService', 'Incoming call subscription errors:', errors);\n        }\n      },\n      error: error => {\n        this.logger.error('CallService', 'Error in incoming call subscription:', error);\n        // Retry subscription after 5 seconds\n        setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n      }\n    });\n  }\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  subscribeToCallStatusChanges() {\n    this.logger.debug('CallService', 'Setting up call status changes subscription...');\n    this.apollo.subscribe({\n      query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callStatusChanged) {\n          this.logger.info('CallService', 'Call status changed:', data.callStatusChanged);\n          this.handleCallStatusChange(data.callStatusChanged);\n        }\n        if (errors) {\n          this.logger.error('CallService', 'Call status subscription errors:', errors);\n        }\n      },\n      error: error => {\n        this.logger.error('CallService', 'Error in call status subscription:', error);\n        setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n      }\n    });\n  }\n  /**\n   * S'abonne aux signaux d'appel\n   */\n  subscribeToCallSignals() {\n    this.logger.debug('CallService', 'Setting up call signals subscription...');\n    this.apollo.subscribe({\n      query: CALL_SIGNAL_SUBSCRIPTION,\n      errorPolicy: 'all'\n    }).subscribe({\n      next: ({\n        data,\n        errors\n      }) => {\n        if (data?.callSignal) {\n          this.logger.debug('CallService', 'Call signal received:', data.callSignal);\n          this.handleCallSignal(data.callSignal);\n        }\n        if (errors) {\n          this.logger.error('CallService', 'Call signal subscription errors:', errors);\n        }\n      },\n      error: error => {\n        this.logger.error('CallService', 'Error in call signal subscription:', error);\n        setTimeout(() => this.subscribeToCallSignals(), 5000);\n      }\n    });\n  }\n  /**\n   * Initialise WebRTC\n   */\n  initializeWebRTC() {\n    this.logger.debug('CallService', 'Initializing WebRTC...');\n    this.createPeerConnection();\n  }\n  /**\n   * Crée une nouvelle PeerConnection\n   */\n  createPeerConnection() {\n    try {\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      this.logger.debug('CallService', 'PeerConnection created successfully');\n      // Gestion des candidats ICE\n      this.peerConnection.onicecandidate = event => {\n        if (event.candidate && this.currentCallId) {\n          this.logger.debug('CallService', 'ICE candidate generated:', event.candidate);\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n      // Gestion du stream distant\n      this.peerConnection.ontrack = event => {\n        this.logger.info('CallService', 'Remote track received:', event.track.kind);\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n      // Gestion des changements d'état de connexion\n      this.peerConnection.onconnectionstatechange = () => {\n        this.logger.debug('CallService', 'Connection state changed:', this.peerConnection?.connectionState);\n        if (this.peerConnection?.connectionState === 'connected') {\n          this.logger.info('CallService', '✅ WebRTC connection established');\n          this.stop('ringtone');\n          this.play('call-connected');\n        } else if (this.peerConnection?.connectionState === 'failed') {\n          this.logger.error('CallService', '❌ WebRTC connection failed');\n          this.handleConnectionFailure();\n        }\n      };\n      // Gestion des changements d'état ICE\n      this.peerConnection.oniceconnectionstatechange = () => {\n        this.logger.debug('CallService', 'ICE connection state:', this.peerConnection?.iceConnectionState);\n      };\n    } catch (error) {\n      this.logger.error('CallService', 'Error creating PeerConnection:', error);\n    }\n  }\n  /**\n   * Gère un appel entrant\n   */\n  handleIncomingCall(call) {\n    this.logger.info('CallService', 'Handling incoming call:', call);\n    this.incomingCall.next(call);\n    this.currentCallId = call.id;\n    this.play('ringtone', true);\n    // Préparer WebRTC pour l'appel entrant\n    this.prepareForIncomingCall(call);\n  }\n  /**\n   * Prépare WebRTC pour un appel entrant\n   */\n  prepareForIncomingCall(call) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        _this.logger.debug('CallService', 'Preparing WebRTC for incoming call');\n        // Créer une nouvelle PeerConnection si nécessaire\n        if (!_this.peerConnection) {\n          _this.createPeerConnection();\n        }\n        // Obtenir les médias utilisateur\n        const stream = yield _this.getUserMedia(call.type);\n        _this.addLocalStreamToPeerConnection(stream);\n      } catch (error) {\n        _this.logger.error('CallService', 'Error preparing for incoming call:', error);\n      }\n    })();\n  }\n  /**\n   * Gère les changements de statut d'appel\n   */\n  handleCallStatusChange(call) {\n    this.logger.info('CallService', 'Handling call status change:', call);\n    if (call.id === this.currentCallId) {\n      this.activeCall.next(call);\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.stop('ringtone');\n          this.play('call-connected');\n          break;\n        case CallStatus.ENDED:\n        case CallStatus.REJECTED:\n          this.handleCallEnd();\n          break;\n      }\n    }\n  }\n  /**\n   * Gère les signaux d'appel\n   */\n  handleCallSignal(signal) {\n    if (signal.callId !== this.currentCallId) {\n      this.logger.debug('CallService', 'Ignoring signal for different call');\n      return;\n    }\n    this.logger.debug('CallService', 'Processing call signal:', signal.type);\n    switch (signal.type) {\n      case 'offer':\n        this.handleRemoteOffer(signal);\n        break;\n      case 'answer':\n        this.handleRemoteAnswer(signal);\n        break;\n      case 'ice-candidate':\n        this.handleRemoteICECandidate(signal);\n        break;\n      default:\n        this.logger.warn('CallService', 'Unknown signal type:', signal.type);\n    }\n  }\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  getUserMedia(callType) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.logger.info('CallService', `🎥 Getting user media for: ${callType}`);\n      const constraints = {\n        audio: true,\n        video: callType === CallType.VIDEO\n      };\n      _this2.logger.debug('CallService', 'Media constraints:', constraints);\n      try {\n        const stream = yield navigator.mediaDevices.getUserMedia(constraints);\n        _this2.logger.info('CallService', '✅ User media obtained successfully');\n        _this2.localStream = stream;\n        // Log des détails du stream\n        _this2.logger.debug('CallService', `Stream tracks: ${stream.getTracks().length}`);\n        stream.getTracks().forEach(track => {\n          _this2.logger.debug('CallService', `Track: ${track.kind} - ${track.label}`);\n        });\n        return stream;\n      } catch (error) {\n        _this2.logger.error('CallService', '❌ Error getting user media:', error);\n        throw new Error(\"Impossible d'accéder au microphone/caméra\");\n      }\n    })();\n  }\n  // ===== MÉTHODES PUBLIQUES D'APPEL =====\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(recipientId, callType, conversationId) {\n    this.logger.info('CallService', '🔄 Initiating WebRTC call:', {\n      recipientId,\n      callType,\n      conversationId\n    });\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      this.logger.error('CallService', 'initiateCall error:', error);\n      return throwError(() => error);\n    }\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;\n    // Utiliser GraphQL pour initier l'appel\n    return this.apollo.mutate({\n      mutation: INITIATE_CALL_MUTATION,\n      variables: {\n        recipientId,\n        callType: callType,\n        callId,\n        offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}',\n        conversationId\n      }\n    }).pipe(map(result => {\n      const call = result.data?.initiateCall;\n      if (!call) {\n        throw new Error('Failed to initiate call');\n      }\n      this.logger.info('CallService', '✅ Call initiated successfully:', call);\n      // Démarrer la sonnerie\n      this.play('ringtone', true);\n      // Mettre à jour l'état\n      this.activeCall.next(call);\n      this.currentCallId = call.id;\n      // Démarrer les médias WebRTC\n      this.startOutgoingCallMedia(callType);\n      return call;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error initiating call:', error);\n      // Fallback vers version temporaire en cas d'erreur GraphQL\n      return this.initiateCallTemporary(recipientId, callType, conversationId);\n    }));\n  }\n  /**\n   * Version temporaire pour les tests sans backend\n   */\n  initiateCallTemporary(recipientId, callType, conversationId) {\n    this.logger.info('CallService', '🔄 Using temporary call initiation');\n    const fakeCall = {\n      id: 'call_' + Date.now(),\n      caller: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png'\n      },\n      recipient: {\n        _id: recipientId,\n        id: recipientId,\n        username: 'Other User',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png'\n      },\n      type: callType,\n      status: CallStatus.RINGING,\n      startTime: new Date().toISOString(),\n      conversationId: conversationId || ''\n    };\n    return new Observable(observer => {\n      setTimeout(() => {\n        this.logger.info('CallService', '✅ Temporary call initiated:', fakeCall);\n        this.play('ringtone', true);\n        this.activeCall.next(fakeCall);\n        this.currentCallId = fakeCall.id;\n        // Démarrer les médias pour l'appel sortant\n        this.startOutgoingCallMedia(callType);\n        // Simuler un appel entrant après 3 secondes pour tester l'acceptation\n        setTimeout(() => {\n          this.simulateIncomingCall(fakeCall);\n        }, 3000);\n        observer.next(fakeCall);\n        observer.complete();\n      }, 500);\n    });\n  }\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(call) {\n    this.logger.info('CallService', '✅ Accepting incoming call:', call.id);\n    if (!call) {\n      const error = new Error('No call to accept');\n      this.logger.error('CallService', 'acceptCall error:', error);\n      return throwError(() => error);\n    }\n    return this.apollo.mutate({\n      mutation: ACCEPT_CALL_MUTATION,\n      variables: {\n        callId: call.id\n      }\n    }).pipe(map(result => {\n      const acceptedCall = result.data?.acceptCall;\n      if (!acceptedCall) {\n        throw new Error('Failed to accept call');\n      }\n      this.logger.info('CallService', '✅ Call accepted successfully:', acceptedCall);\n      // Arrêter la sonnerie\n      this.stop('ringtone');\n      this.play('call-connected');\n      // Mettre à jour l'état\n      this.activeCall.next(acceptedCall);\n      this.incomingCall.next(null);\n      return acceptedCall;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error accepting call via GraphQL:', error);\n      // Fallback: accepter localement pour les tests\n      return this.acceptCallTemporary(call);\n    }));\n  }\n  /**\n   * Version temporaire pour accepter un appel (fallback)\n   */\n  acceptCallTemporary(call) {\n    this.logger.info('CallService', '🔄 Using temporary call acceptance');\n    const acceptedCall = {\n      id: call.id,\n      caller: call.caller,\n      recipient: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png'\n      },\n      type: call.type,\n      status: CallStatus.CONNECTED,\n      startTime: new Date().toISOString(),\n      conversationId: call.conversationId || ''\n    };\n    return new Observable(observer => {\n      setTimeout(() => {\n        this.logger.info('CallService', '✅ Temporary call accepted:', acceptedCall);\n        // Arrêter la sonnerie\n        this.stop('ringtone');\n        this.play('call-connected');\n        // Mettre à jour l'état\n        this.activeCall.next(acceptedCall);\n        this.incomingCall.next(null);\n        observer.next(acceptedCall);\n        observer.complete();\n      }, 300);\n    });\n  }\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId, reason) {\n    this.logger.info('CallService', '❌ Rejecting call:', callId);\n    return this.apollo.mutate({\n      mutation: REJECT_CALL_MUTATION,\n      variables: {\n        callId,\n        reason: reason || 'User rejected'\n      }\n    }).pipe(map(result => {\n      const success = result.data?.rejectCall;\n      if (!success) {\n        throw new Error('Failed to reject call');\n      }\n      this.logger.info('CallService', '✅ Call rejected successfully:', success);\n      // Arrêter la sonnerie et jouer le son de fin\n      this.stop('ringtone');\n      this.play('call-end');\n      // Nettoyer l'état\n      this.incomingCall.next(null);\n      this.activeCall.next(null);\n      this.cleanupWebRTC();\n      return success;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error rejecting call:', error);\n      return throwError(() => new Error('Failed to reject call'));\n    }));\n  }\n  /**\n   * Termine un appel actif\n   */\n  endCall(callId) {\n    this.logger.info('CallService', '🔚 Ending call:', callId);\n    return this.apollo.mutate({\n      mutation: END_CALL_MUTATION,\n      variables: {\n        callId\n      }\n    }).pipe(map(result => {\n      const success = result.data?.endCall;\n      if (!success) {\n        throw new Error('Failed to end call');\n      }\n      this.logger.info('CallService', '✅ Call ended successfully:', success);\n      // Jouer le son de fin\n      this.play('call-end');\n      // Nettoyer l'état\n      this.handleCallEnd();\n      return success;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error ending call:', error);\n      // Fallback: nettoyer localement même en cas d'erreur\n      this.handleCallEnd();\n      return of({\n        success: true,\n        message: 'Call ended locally (server error)'\n      });\n    }));\n  }\n  // ===== MÉTHODES WEBRTC PRIVÉES =====\n  /**\n   * Démarre les médias pour un appel sortant\n   */\n  startOutgoingCallMedia(callType) {\n    this.logger.info('CallService', '🎥 Starting outgoing call media');\n    this.getUserMedia(callType).then(stream => {\n      this.addLocalStreamToPeerConnection(stream);\n      this.attachLocalStream();\n      // Créer une offre WebRTC\n      return this.createOffer();\n    }).then(offer => {\n      this.logger.info('CallService', '✅ Offer created for outgoing call');\n      // Dans un vrai scénario, on enverrait cette offre au destinataire\n      this.sendSignal('offer', JSON.stringify(offer));\n    }).catch(error => {\n      this.logger.error('CallService', '❌ Error starting outgoing call media:', error);\n    });\n  }\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  addLocalStreamToPeerConnection(stream) {\n    if (!this.peerConnection) {\n      this.logger.error('CallService', 'No PeerConnection available');\n      return;\n    }\n    try {\n      stream.getTracks().forEach(track => {\n        this.peerConnection.addTrack(track, stream);\n        this.logger.debug('CallService', `Added ${track.kind} track to PeerConnection`);\n      });\n    } catch (error) {\n      this.logger.error('CallService', 'Error adding stream to PeerConnection:', error);\n    }\n  }\n  /**\n   * Attache le stream local à l'élément vidéo\n   */\n  attachLocalStream() {\n    if (this.localVideoElement && this.localStream) {\n      this.localVideoElement.srcObject = this.localStream;\n      this.logger.debug('CallService', 'Local stream attached to video element');\n    }\n  }\n  /**\n   * Attache le stream distant à l'élément vidéo\n   */\n  attachRemoteStream() {\n    if (this.remoteVideoElement && this.remoteStream) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n      this.logger.debug('CallService', 'Remote stream attached to video element');\n    }\n  }\n  /**\n   * Crée une offre WebRTC\n   */\n  createOffer() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.peerConnection) {\n        throw new Error('PeerConnection not initialized');\n      }\n      try {\n        const offer = yield _this3.peerConnection.createOffer({\n          offerToReceiveAudio: true,\n          offerToReceiveVideo: true\n        });\n        yield _this3.peerConnection.setLocalDescription(offer);\n        _this3.logger.info('CallService', '✅ Offer created and set as local description');\n        return offer;\n      } catch (error) {\n        _this3.logger.error('CallService', '❌ Error creating offer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Crée une réponse WebRTC\n   */\n  createAnswer(offer) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this4.peerConnection) {\n        throw new Error('PeerConnection not initialized');\n      }\n      try {\n        yield _this4.peerConnection.setRemoteDescription(offer);\n        _this4.logger.info('CallService', '✅ Remote description set');\n        const answer = yield _this4.peerConnection.createAnswer();\n        yield _this4.peerConnection.setLocalDescription(answer);\n        _this4.logger.info('CallService', '✅ Answer created and set as local description');\n        return answer;\n      } catch (error) {\n        _this4.logger.error('CallService', '❌ Error creating answer:', error);\n        throw error;\n      }\n    })();\n  }\n  /**\n   * Gère une offre distante\n   */\n  handleRemoteOffer(signal) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const offer = JSON.parse(signal.data);\n        _this5.logger.info('CallService', '📥 Handling remote offer:', offer);\n        // Obtenir les médias utilisateur\n        const stream = yield _this5.getUserMedia(CallType.AUDIO);\n        _this5.addLocalStreamToPeerConnection(stream);\n        // Créer et envoyer la réponse\n        const answer = yield _this5.createAnswer(offer);\n        _this5.sendSignal('answer', JSON.stringify(answer));\n        _this5.logger.info('CallService', '✅ Remote offer handled successfully');\n      } catch (error) {\n        _this5.logger.error('CallService', '❌ Error handling remote offer:', error);\n      }\n    })();\n  }\n  /**\n   * Gère une réponse distante\n   */\n  handleRemoteAnswer(signal) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const answer = JSON.parse(signal.data);\n        _this6.logger.info('CallService', '📥 Handling remote answer:', answer);\n        if (_this6.peerConnection) {\n          yield _this6.peerConnection.setRemoteDescription(answer);\n          _this6.logger.info('CallService', '✅ Remote answer handled successfully');\n        }\n      } catch (error) {\n        _this6.logger.error('CallService', '❌ Error handling remote answer:', error);\n      }\n    })();\n  }\n  /**\n   * Gère un candidat ICE distant\n   */\n  handleRemoteICECandidate(signal) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const candidate = JSON.parse(signal.data);\n        _this7.logger.debug('CallService', '📥 Handling remote ICE candidate:', candidate);\n        if (_this7.peerConnection && candidate) {\n          yield _this7.peerConnection.addIceCandidate(new RTCIceCandidate(candidate));\n          _this7.logger.debug('CallService', '✅ Remote ICE candidate added successfully');\n        }\n      } catch (error) {\n        _this7.logger.error('CallService', '❌ Error handling ICE candidate:', error);\n      }\n    })();\n  }\n  /**\n   * Envoie un signal WebRTC\n   */\n  sendSignal(type, data) {\n    if (!this.currentCallId) {\n      this.logger.error('CallService', 'No current call ID for signal');\n      return;\n    }\n    this.logger.debug('CallService', `📤 Sending signal: ${type}`);\n    // Utiliser GraphQL pour envoyer le signal\n    this.apollo.mutate({\n      mutation: SEND_CALL_SIGNAL_MUTATION,\n      variables: {\n        callId: this.currentCallId,\n        signalType: type,\n        signalData: data\n      }\n    }).subscribe({\n      next: result => {\n        this.logger.debug('CallService', '✅ Signal sent successfully');\n      },\n      error: error => {\n        this.logger.error('CallService', '❌ Error sending signal:', error);\n        // Fallback: simulation locale pour les tests\n        setTimeout(() => {\n          this.handleCallSignal({\n            callId: this.currentCallId,\n            senderId: 'other-user',\n            type,\n            data,\n            timestamp: new Date().toISOString()\n          });\n        }, 100);\n      }\n    });\n  }\n  // ===== MÉTHODES UTILITAIRES =====\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(localVideo, remoteVideo) {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    this.logger.debug('CallService', 'Video elements configured');\n  }\n  /**\n   * Bascule l'audio\n   */\n  toggleAudio() {\n    this.isAudioEnabled = !this.isAudioEnabled;\n    if (this.localStream) {\n      this.localStream.getAudioTracks().forEach(track => {\n        track.enabled = this.isAudioEnabled;\n      });\n    }\n    this.logger.info('CallService', `Audio ${this.isAudioEnabled ? 'enabled' : 'disabled'}`);\n    return this.isAudioEnabled;\n  }\n  /**\n   * Bascule la vidéo\n   */\n  toggleVideo() {\n    this.isVideoEnabled = !this.isVideoEnabled;\n    if (this.localStream) {\n      this.localStream.getVideoTracks().forEach(track => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n    this.logger.info('CallService', `Video ${this.isVideoEnabled ? 'enabled' : 'disabled'}`);\n    return this.isVideoEnabled;\n  }\n  /**\n   * Bascule les médias via GraphQL\n   */\n  toggleMedia(callId, enableVideo, enableAudio) {\n    return this.apollo.mutate({\n      mutation: TOGGLE_CALL_MEDIA_MUTATION,\n      variables: {\n        callId,\n        video: enableVideo,\n        audio: enableAudio\n      }\n    }).pipe(map(result => {\n      return result.data.toggleCallMedia;\n    }), catchError(error => {\n      this.logger.error('CallService', 'Error toggling media:', error);\n      return throwError(() => new Error('Erreur lors du changement des médias'));\n    }));\n  }\n  /**\n   * Gère la fin d'appel\n   */\n  handleCallEnd() {\n    this.logger.info('CallService', '🔚 Handling call end');\n    this.stopAllSounds();\n    this.activeCall.next(null);\n    this.incomingCall.next(null);\n    this.currentCallId = null;\n    this.cleanupWebRTC();\n  }\n  /**\n   * Gère l'échec de connexion WebRTC\n   */\n  handleConnectionFailure() {\n    this.logger.error('CallService', '❌ WebRTC connection failed');\n    this.play('call-end');\n    this.handleCallEnd();\n  }\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  cleanupWebRTC() {\n    this.logger.debug('CallService', '🧹 Cleaning up WebRTC resources');\n    if (this.localStream) {\n      this.localStream.getTracks().forEach(track => {\n        track.stop();\n        this.logger.debug('CallService', `Stopped ${track.kind} track`);\n      });\n      this.localStream = null;\n    }\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n      this.logger.debug('CallService', 'PeerConnection closed');\n    }\n    this.remoteStream = null;\n    this.webrtcExchangeStarted = false;\n    // Réinitialiser les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n  }\n  /**\n   * Simule un appel entrant (pour les tests)\n   */\n  simulateIncomingCall(originalCall) {\n    const incomingCall = {\n      id: originalCall.id,\n      caller: originalCall.recipient,\n      type: originalCall.type,\n      conversationId: originalCall.conversationId,\n      offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}',\n      timestamp: new Date().toISOString()\n    };\n    this.logger.info('CallService', '📞 Simulating incoming call:', incomingCall);\n    this.stop('ringtone'); // Arrêter la sonnerie sortante\n    this.incomingCall.next(incomingCall);\n    this.activeCall.next(null); // Nettoyer l'appel sortant\n    // Jouer la sonnerie d'appel entrant\n    this.play('ringtone', true);\n  }\n  /**\n   * Active les sons après interaction utilisateur\n   */\n  enableSounds() {\n    this.logger.info('CallService', 'Sounds enabled after user interaction');\n  }\n  // ===== GETTERS =====\n  /**\n   * Obtient l'état audio actuel\n   */\n  get audioEnabled() {\n    return this.isAudioEnabled;\n  }\n  /**\n   * Obtient l'état vidéo actuel\n   */\n  get videoEnabled() {\n    return this.isVideoEnabled;\n  }\n  /**\n   * Obtient l'appel actuel\n   */\n  get currentCall() {\n    return this.activeCall.value;\n  }\n  /**\n   * Obtient l'appel entrant actuel\n   */\n  get currentIncomingCall() {\n    return this.incomingCall.value;\n  }\n  static {\n    this.ɵfac = function CallService_Factory(t) {\n      return new (t || CallService)(i0.ɵɵinject(i1.Apollo), i0.ɵɵinject(i2.LoggerService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: CallService,\n      factory: CallService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "Observable", "throwError", "of", "map", "catchError", "CallType", "CallStatus", "INITIATE_CALL_MUTATION", "ACCEPT_CALL_MUTATION", "REJECT_CALL_MUTATION", "END_CALL_MUTATION", "TOGGLE_CALL_MEDIA_MUTATION", "INCOMING_CALL_SUBSCRIPTION", "CALL_STATUS_CHANGED_SUBSCRIPTION", "CALL_SIGNAL_SUBSCRIPTION", "SEND_CALL_SIGNAL_MUTATION", "CallService", "constructor", "apollo", "logger", "activeCall", "incomingCall", "callSignals", "activeCall$", "asObservable", "incomingCall$", "callSignals$", "isAudioEnabled", "isVideoEnabled", "sounds", "isPlaying", "peerConnection", "localStream", "remoteStream", "localVideoElement", "remoteVideoElement", "currentCallId", "webrtcExchangeStarted", "rtcConfig", "iceServers", "urls", "info", "initializeSounds", "initializeSubscriptions", "initializeWebRTC", "ngOnDestroy", "cleanupWebRTC", "stopAllSounds", "debug", "createSyntheticSounds", "createSyntheticSound", "name", "frequencies", "duration", "loop", "audioContext", "window", "AudioContext", "webkitAudioContext", "sampleRate", "frameCount", "buffer", "createBuffer", "channelData", "getChannelData", "i", "sample", "for<PERSON>ach", "freq", "index", "amplitude", "length", "phase", "Math", "PI", "sin", "envelope", "audio", "Audio", "customPlay", "source", "createBufferSource", "connect", "destination", "start", "setTimeout", "error", "play", "sound", "warn", "currentSource", "currentTime", "catch", "stop", "pause", "Object", "keys", "subscribeToIncomingCalls", "subscribeToCallStatusChanges", "subscribeToCallSignals", "subscribe", "query", "errorPolicy", "next", "data", "errors", "handleIncomingCall", "callStatusChanged", "handleCallStatusChange", "callSignal", "handleCallSignal", "createPeerConnection", "RTCPeerConnection", "onicecandidate", "event", "candidate", "sendSignal", "JSON", "stringify", "ontrack", "track", "kind", "streams", "attachRemoteStream", "onconnectionstatechange", "connectionState", "handleConnectionFailure", "oniceconnectionstatechange", "iceConnectionState", "call", "id", "prepareForIncomingCall", "_this", "_asyncToGenerator", "stream", "getUserMedia", "type", "addLocalStreamToPeerConnection", "status", "CONNECTED", "ENDED", "REJECTED", "handleCallEnd", "signal", "callId", "handleRemoteOffer", "handleRemoteAnswer", "handleRemoteICECandidate", "callType", "_this2", "constraints", "video", "VIDEO", "navigator", "mediaDevices", "getTracks", "label", "Error", "initiateCall", "recipientId", "conversationId", "Date", "now", "random", "toString", "substr", "mutate", "mutation", "variables", "offer", "pipe", "result", "startOutgoingCallMedia", "initiateCallTemporary", "fakeCall", "caller", "_id", "username", "email", "role", "isActive", "image", "recipient", "RINGING", "startTime", "toISOString", "observer", "simulateIncomingCall", "complete", "acceptCall", "acceptedCall", "acceptCallTemporary", "rejectCall", "reason", "success", "endCall", "message", "then", "attachLocalStream", "createOffer", "addTrack", "srcObject", "_this3", "offerToReceiveAudio", "offerToReceiveVideo", "setLocalDescription", "createAnswer", "_this4", "setRemoteDescription", "answer", "_this5", "parse", "AUDIO", "_this6", "_this7", "addIceCandidate", "RTCIceCandidate", "signalType", "signalData", "senderId", "timestamp", "setVideoElements", "localVideo", "remoteVideo", "toggleAudio", "getAudioTracks", "enabled", "toggleVideo", "getVideoTracks", "toggleMedia", "enableVideo", "enableAudio", "toggleCallMedia", "close", "originalCall", "enableSounds", "audioEnabled", "videoEnabled", "currentCall", "value", "currentIncomingCall", "i0", "ɵɵinject", "i1", "Apollo", "i2", "LoggerService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\services\\call.service.ts"], "sourcesContent": ["import { Injectable, On<PERSON><PERSON>roy } from '@angular/core';\nimport { Apollo } from 'apollo-angular';\nimport { BehaviorSubject, Observable, throwError, from, of } from 'rxjs';\nimport { map, catchError, switchMap, tap, retry } from 'rxjs/operators';\nimport {\n  Call,\n  CallType,\n  CallStatus,\n  IncomingCall,\n  CallSuccess,\n  CallSignal,\n} from '../models/message.model';\nimport {\n  INITIATE_CALL_MUTATION,\n  ACCEPT_CALL_MUTATION,\n  REJECT_CALL_MUTATION,\n  END_CALL_MUTATION,\n  TOGGLE_CALL_MEDIA_MUTATION,\n  INCOMING_CALL_SUBSCRIPTION,\n  CALL_STATUS_CHANGED_SUBSCRIPTION,\n  CALL_SIGNAL_SUBSCRIPTION,\n  SEND_CALL_SIGNAL_MUTATION,\n} from '../graphql/message.graphql';\nimport { LoggerService } from './logger.service';\n\n@Injectable({\n  providedIn: 'root',\n})\nexport class CallService implements OnD<PERSON>roy {\n  // Observables pour l'état des appels\n  private activeCall = new BehaviorSubject<Call | null>(null);\n  private incomingCall = new BehaviorSubject<IncomingCall | null>(null);\n  private callSignals = new BehaviorSubject<CallSignal | null>(null);\n\n  // Observables publics\n  public activeCall$ = this.activeCall.asObservable();\n  public incomingCall$ = this.incomingCall.asObservable();\n  public callSignals$ = this.callSignals.asObservable();\n\n  // État des médias\n  private isAudioEnabled = true;\n  private isVideoEnabled = true;\n\n  // Gestion des sons\n  private sounds: { [key: string]: HTMLAudioElement } = {};\n  private isPlaying: { [key: string]: boolean } = {};\n\n  // WebRTC\n  private peerConnection: RTCPeerConnection | null = null;\n  private localStream: MediaStream | null = null;\n  private remoteStream: MediaStream | null = null;\n  private localVideoElement: HTMLVideoElement | null = null;\n  private remoteVideoElement: HTMLVideoElement | null = null;\n  private currentCallId: string | null = null;\n  private webrtcExchangeStarted: boolean = false;\n\n  // Configuration WebRTC\n  private readonly rtcConfig: RTCConfiguration = {\n    iceServers: [\n      { urls: 'stun:stun.l.google.com:19302' },\n      { urls: 'stun:stun1.l.google.com:19302' },\n      { urls: 'stun:stun2.l.google.com:19302' },\n    ],\n  };\n\n  constructor(private apollo: Apollo, private logger: LoggerService) {\n    this.logger.info('CallService', '🚀 Initializing CallService...');\n    this.initializeSounds();\n    this.initializeSubscriptions();\n    this.initializeWebRTC();\n    this.logger.info('CallService', '✅ CallService initialized successfully');\n  }\n\n  ngOnDestroy(): void {\n    this.logger.info('CallService', '🔄 Destroying CallService...');\n    this.cleanupWebRTC();\n    this.stopAllSounds();\n  }\n\n  /**\n   * Initialise les sons\n   */\n  private initializeSounds(): void {\n    this.logger.debug('CallService', 'Initializing sounds...');\n    this.createSyntheticSounds();\n  }\n\n  /**\n   * Crée des sons synthétiques\n   */\n  private createSyntheticSounds(): void {\n    this.logger.debug('CallService', 'Creating synthetic sounds...');\n\n    // Créer des sons synthétiques pour éviter les problèmes de fichiers manquants\n    this.createSyntheticSound('ringtone', [440, 554.37], 1.5, true);\n    this.createSyntheticSound(\n      'call-connected',\n      [523.25, 659.25, 783.99],\n      0.8,\n      false\n    );\n    this.createSyntheticSound('call-end', [392, 329.63, 261.63], 1.2, false);\n  }\n\n  /**\n   * Crée un son synthétique\n   */\n  private createSyntheticSound(\n    name: string,\n    frequencies: number[],\n    duration: number,\n    loop: boolean\n  ): void {\n    try {\n      // Créer un contexte audio\n      const audioContext = new (window.AudioContext ||\n        (window as any).webkitAudioContext)();\n\n      // Créer un buffer audio\n      const sampleRate = audioContext.sampleRate;\n      const frameCount = sampleRate * duration;\n      const buffer = audioContext.createBuffer(1, frameCount, sampleRate);\n      const channelData = buffer.getChannelData(0);\n\n      // Générer le son\n      for (let i = 0; i < frameCount; i++) {\n        let sample = 0;\n        frequencies.forEach((freq, index) => {\n          const amplitude = 0.3 / frequencies.length;\n          const phase = (i / sampleRate) * freq * 2 * Math.PI;\n          sample += Math.sin(phase) * amplitude;\n        });\n\n        // Appliquer une enveloppe pour éviter les clics\n        const envelope = Math.sin((i / frameCount) * Math.PI);\n        channelData[i] = sample * envelope;\n      }\n\n      // Créer un élément audio factice pour l'interface\n      const audio = new Audio();\n      audio.loop = loop;\n\n      // Stocker une fonction de lecture personnalisée\n      (audio as any).customPlay = () => {\n        const source = audioContext.createBufferSource();\n        source.buffer = buffer;\n        source.loop = loop;\n        source.connect(audioContext.destination);\n        source.start();\n\n        if (!loop) {\n          setTimeout(() => {\n            this.isPlaying[name] = false;\n          }, duration * 1000);\n        }\n\n        return source;\n      };\n\n      this.sounds[name] = audio;\n      this.isPlaying[name] = false;\n\n      this.logger.debug(\n        'CallService',\n        `Synthetic sound '${name}' created successfully`\n      );\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error creating synthetic sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Joue un son\n   */\n  play(name: string, loop: boolean = false): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) {\n        this.logger.warn('CallService', `Sound '${name}' not found`);\n        return;\n      }\n\n      if (!this.isPlaying[name]) {\n        if ((sound as any).customPlay) {\n          (sound as any).currentSource = (sound as any).customPlay();\n          this.isPlaying[name] = true;\n          this.logger.debug('CallService', `Playing synthetic sound: ${name}`);\n        } else {\n          sound.currentTime = 0;\n          sound.loop = loop;\n          sound.play().catch((error) => {\n            this.logger.error(\n              'CallService',\n              `Error playing sound '${name}':`,\n              error\n            );\n          });\n          this.isPlaying[name] = true;\n        }\n      }\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error in play method for '${name}':`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Arrête un son\n   */\n  stop(name: string): void {\n    try {\n      const sound = this.sounds[name];\n      if (!sound) return;\n\n      if (this.isPlaying[name]) {\n        if ((sound as any).currentSource) {\n          (sound as any).currentSource.stop();\n          (sound as any).currentSource = null;\n        } else {\n          sound.pause();\n          sound.currentTime = 0;\n        }\n        this.isPlaying[name] = false;\n        this.logger.debug('CallService', `Stopped sound: ${name}`);\n      }\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        `Error stopping sound '${name}':`,\n        error\n      );\n    }\n  }\n\n  /**\n   * Arrête tous les sons\n   */\n  stopAllSounds(): void {\n    Object.keys(this.sounds).forEach((name) => {\n      this.stop(name);\n    });\n  }\n\n  /**\n   * Initialise les subscriptions\n   */\n  private initializeSubscriptions(): void {\n    this.logger.debug('CallService', 'Initializing subscriptions...');\n    this.subscribeToIncomingCalls();\n    this.subscribeToCallStatusChanges();\n    this.subscribeToCallSignals();\n  }\n\n  /**\n   * S'abonne aux appels entrants\n   */\n  private subscribeToIncomingCalls(): void {\n    this.logger.debug(\n      'CallService',\n      'Setting up incoming calls subscription...'\n    );\n\n    this.apollo\n      .subscribe<{ incomingCall: IncomingCall }>({\n        query: INCOMING_CALL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.incomingCall) {\n            this.logger.info(\n              'CallService',\n              'Incoming call received:',\n              data.incomingCall\n            );\n            this.handleIncomingCall(data.incomingCall);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Incoming call subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in incoming call subscription:',\n            error\n          );\n          // Retry subscription after 5 seconds\n          setTimeout(() => this.subscribeToIncomingCalls(), 5000);\n        },\n      });\n  }\n\n  /**\n   * S'abonne aux changements de statut d'appel\n   */\n  private subscribeToCallStatusChanges(): void {\n    this.logger.debug(\n      'CallService',\n      'Setting up call status changes subscription...'\n    );\n\n    this.apollo\n      .subscribe<{ callStatusChanged: Call }>({\n        query: CALL_STATUS_CHANGED_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callStatusChanged) {\n            this.logger.info(\n              'CallService',\n              'Call status changed:',\n              data.callStatusChanged\n            );\n            this.handleCallStatusChange(data.callStatusChanged);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call status subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call status subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallStatusChanges(), 5000);\n        },\n      });\n  }\n\n  /**\n   * S'abonne aux signaux d'appel\n   */\n  private subscribeToCallSignals(): void {\n    this.logger.debug('CallService', 'Setting up call signals subscription...');\n\n    this.apollo\n      .subscribe<{ callSignal: CallSignal }>({\n        query: CALL_SIGNAL_SUBSCRIPTION,\n        errorPolicy: 'all',\n      })\n      .subscribe({\n        next: ({ data, errors }) => {\n          if (data?.callSignal) {\n            this.logger.debug(\n              'CallService',\n              'Call signal received:',\n              data.callSignal\n            );\n            this.handleCallSignal(data.callSignal);\n          }\n          if (errors) {\n            this.logger.error(\n              'CallService',\n              'Call signal subscription errors:',\n              errors\n            );\n          }\n        },\n        error: (error) => {\n          this.logger.error(\n            'CallService',\n            'Error in call signal subscription:',\n            error\n          );\n          setTimeout(() => this.subscribeToCallSignals(), 5000);\n        },\n      });\n  }\n\n  /**\n   * Initialise WebRTC\n   */\n  private initializeWebRTC(): void {\n    this.logger.debug('CallService', 'Initializing WebRTC...');\n    this.createPeerConnection();\n  }\n\n  /**\n   * Crée une nouvelle PeerConnection\n   */\n  private createPeerConnection(): void {\n    try {\n      this.peerConnection = new RTCPeerConnection(this.rtcConfig);\n      this.logger.debug('CallService', 'PeerConnection created successfully');\n\n      // Gestion des candidats ICE\n      this.peerConnection.onicecandidate = (event) => {\n        if (event.candidate && this.currentCallId) {\n          this.logger.debug(\n            'CallService',\n            'ICE candidate generated:',\n            event.candidate\n          );\n          this.sendSignal('ice-candidate', JSON.stringify(event.candidate));\n        }\n      };\n\n      // Gestion du stream distant\n      this.peerConnection.ontrack = (event) => {\n        this.logger.info(\n          'CallService',\n          'Remote track received:',\n          event.track.kind\n        );\n        this.remoteStream = event.streams[0];\n        this.attachRemoteStream();\n      };\n\n      // Gestion des changements d'état de connexion\n      this.peerConnection.onconnectionstatechange = () => {\n        this.logger.debug(\n          'CallService',\n          'Connection state changed:',\n          this.peerConnection?.connectionState\n        );\n\n        if (this.peerConnection?.connectionState === 'connected') {\n          this.logger.info('CallService', '✅ WebRTC connection established');\n          this.stop('ringtone');\n          this.play('call-connected');\n        } else if (this.peerConnection?.connectionState === 'failed') {\n          this.logger.error('CallService', '❌ WebRTC connection failed');\n          this.handleConnectionFailure();\n        }\n      };\n\n      // Gestion des changements d'état ICE\n      this.peerConnection.oniceconnectionstatechange = () => {\n        this.logger.debug(\n          'CallService',\n          'ICE connection state:',\n          this.peerConnection?.iceConnectionState\n        );\n      };\n    } catch (error) {\n      this.logger.error('CallService', 'Error creating PeerConnection:', error);\n    }\n  }\n\n  /**\n   * Gère un appel entrant\n   */\n  private handleIncomingCall(call: IncomingCall): void {\n    this.logger.info('CallService', 'Handling incoming call:', call);\n\n    this.incomingCall.next(call);\n    this.currentCallId = call.id;\n    this.play('ringtone', true);\n\n    // Préparer WebRTC pour l'appel entrant\n    this.prepareForIncomingCall(call);\n  }\n\n  /**\n   * Prépare WebRTC pour un appel entrant\n   */\n  private async prepareForIncomingCall(call: IncomingCall): Promise<void> {\n    try {\n      this.logger.debug('CallService', 'Preparing WebRTC for incoming call');\n\n      // Créer une nouvelle PeerConnection si nécessaire\n      if (!this.peerConnection) {\n        this.createPeerConnection();\n      }\n\n      // Obtenir les médias utilisateur\n      const stream = await this.getUserMedia(call.type);\n      this.addLocalStreamToPeerConnection(stream);\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error preparing for incoming call:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Gère les changements de statut d'appel\n   */\n  private handleCallStatusChange(call: Call): void {\n    this.logger.info('CallService', 'Handling call status change:', call);\n\n    if (call.id === this.currentCallId) {\n      this.activeCall.next(call);\n\n      switch (call.status) {\n        case CallStatus.CONNECTED:\n          this.stop('ringtone');\n          this.play('call-connected');\n          break;\n        case CallStatus.ENDED:\n        case CallStatus.REJECTED:\n          this.handleCallEnd();\n          break;\n      }\n    }\n  }\n\n  /**\n   * Gère les signaux d'appel\n   */\n  private handleCallSignal(signal: CallSignal): void {\n    if (signal.callId !== this.currentCallId) {\n      this.logger.debug('CallService', 'Ignoring signal for different call');\n      return;\n    }\n\n    this.logger.debug('CallService', 'Processing call signal:', signal.type);\n\n    switch (signal.type) {\n      case 'offer':\n        this.handleRemoteOffer(signal);\n        break;\n      case 'answer':\n        this.handleRemoteAnswer(signal);\n        break;\n      case 'ice-candidate':\n        this.handleRemoteICECandidate(signal);\n        break;\n      default:\n        this.logger.warn('CallService', 'Unknown signal type:', signal.type);\n    }\n  }\n\n  /**\n   * Obtient les médias utilisateur (caméra/micro)\n   */\n  private async getUserMedia(callType: CallType): Promise<MediaStream> {\n    this.logger.info('CallService', `🎥 Getting user media for: ${callType}`);\n\n    const constraints: MediaStreamConstraints = {\n      audio: true,\n      video: callType === CallType.VIDEO,\n    };\n\n    this.logger.debug('CallService', 'Media constraints:', constraints);\n\n    try {\n      const stream = await navigator.mediaDevices.getUserMedia(constraints);\n      this.logger.info('CallService', '✅ User media obtained successfully');\n\n      this.localStream = stream;\n\n      // Log des détails du stream\n      this.logger.debug(\n        'CallService',\n        `Stream tracks: ${stream.getTracks().length}`\n      );\n      stream.getTracks().forEach((track) => {\n        this.logger.debug(\n          'CallService',\n          `Track: ${track.kind} - ${track.label}`\n        );\n      });\n\n      return stream;\n    } catch (error) {\n      this.logger.error('CallService', '❌ Error getting user media:', error);\n      throw new Error(\"Impossible d'accéder au microphone/caméra\");\n    }\n  }\n\n  // ===== MÉTHODES PUBLIQUES D'APPEL =====\n\n  /**\n   * Initie un appel WebRTC\n   */\n  initiateCall(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    this.logger.info('CallService', '🔄 Initiating WebRTC call:', {\n      recipientId,\n      callType,\n      conversationId,\n    });\n\n    if (!recipientId) {\n      const error = new Error('Recipient ID is required');\n      this.logger.error('CallService', 'initiateCall error:', error);\n      return throwError(() => error);\n    }\n\n    // Générer un ID unique pour l'appel\n    const callId = `call_${Date.now()}_${Math.random()\n      .toString(36)\n      .substr(2, 9)}`;\n\n    // Utiliser GraphQL pour initier l'appel\n    return this.apollo\n      .mutate<{ initiateCall: Call }>({\n        mutation: INITIATE_CALL_MUTATION,\n        variables: {\n          recipientId,\n          callType: callType,\n          callId,\n          offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}', // Sera remplacé par une vraie offre WebRTC\n          conversationId,\n        },\n      })\n      .pipe(\n        map((result) => {\n          const call = result.data?.initiateCall;\n          if (!call) {\n            throw new Error('Failed to initiate call');\n          }\n\n          this.logger.info(\n            'CallService',\n            '✅ Call initiated successfully:',\n            call\n          );\n\n          // Démarrer la sonnerie\n          this.play('ringtone', true);\n\n          // Mettre à jour l'état\n          this.activeCall.next(call);\n          this.currentCallId = call.id;\n\n          // Démarrer les médias WebRTC\n          this.startOutgoingCallMedia(callType);\n\n          return call;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error initiating call:', error);\n\n          // Fallback vers version temporaire en cas d'erreur GraphQL\n          return this.initiateCallTemporary(\n            recipientId,\n            callType,\n            conversationId\n          );\n        })\n      );\n  }\n\n  /**\n   * Version temporaire pour les tests sans backend\n   */\n  private initiateCallTemporary(\n    recipientId: string,\n    callType: CallType,\n    conversationId?: string\n  ): Observable<Call> {\n    this.logger.info('CallService', '🔄 Using temporary call initiation');\n\n    const fakeCall: Call = {\n      id: 'call_' + Date.now(),\n      caller: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png',\n      },\n      recipient: {\n        _id: recipientId,\n        id: recipientId,\n        username: 'Other User',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png',\n      },\n      type: callType,\n      status: CallStatus.RINGING,\n      startTime: new Date().toISOString(),\n      conversationId: conversationId || '',\n    };\n\n    return new Observable<Call>((observer) => {\n      setTimeout(() => {\n        this.logger.info(\n          'CallService',\n          '✅ Temporary call initiated:',\n          fakeCall\n        );\n\n        this.play('ringtone', true);\n        this.activeCall.next(fakeCall);\n        this.currentCallId = fakeCall.id;\n\n        // Démarrer les médias pour l'appel sortant\n        this.startOutgoingCallMedia(callType);\n\n        // Simuler un appel entrant après 3 secondes pour tester l'acceptation\n        setTimeout(() => {\n          this.simulateIncomingCall(fakeCall);\n        }, 3000);\n\n        observer.next(fakeCall);\n        observer.complete();\n      }, 500);\n    });\n  }\n\n  /**\n   * Accepte un appel entrant\n   */\n  acceptCall(call: IncomingCall): Observable<Call> {\n    this.logger.info('CallService', '✅ Accepting incoming call:', call.id);\n\n    if (!call) {\n      const error = new Error('No call to accept');\n      this.logger.error('CallService', 'acceptCall error:', error);\n      return throwError(() => error);\n    }\n\n    return this.apollo\n      .mutate<{ acceptCall: Call }>({\n        mutation: ACCEPT_CALL_MUTATION,\n        variables: { callId: call.id },\n      })\n      .pipe(\n        map((result) => {\n          const acceptedCall = result.data?.acceptCall;\n          if (!acceptedCall) {\n            throw new Error('Failed to accept call');\n          }\n\n          this.logger.info(\n            'CallService',\n            '✅ Call accepted successfully:',\n            acceptedCall\n          );\n\n          // Arrêter la sonnerie\n          this.stop('ringtone');\n          this.play('call-connected');\n\n          // Mettre à jour l'état\n          this.activeCall.next(acceptedCall);\n          this.incomingCall.next(null);\n\n          return acceptedCall;\n        }),\n        catchError((error) => {\n          this.logger.error(\n            'CallService',\n            'Error accepting call via GraphQL:',\n            error\n          );\n\n          // Fallback: accepter localement pour les tests\n          return this.acceptCallTemporary(call);\n        })\n      );\n  }\n\n  /**\n   * Version temporaire pour accepter un appel (fallback)\n   */\n  private acceptCallTemporary(call: IncomingCall): Observable<Call> {\n    this.logger.info('CallService', '🔄 Using temporary call acceptance');\n\n    const acceptedCall: Call = {\n      id: call.id,\n      caller: call.caller,\n      recipient: {\n        _id: 'current-user',\n        id: 'current-user',\n        username: 'You',\n        email: '<EMAIL>',\n        role: 'user',\n        isActive: true,\n        image: '/assets/images/default-avatar.png',\n      },\n      type: call.type,\n      status: CallStatus.CONNECTED,\n      startTime: new Date().toISOString(),\n      conversationId: call.conversationId || '',\n    };\n\n    return new Observable<Call>((observer) => {\n      setTimeout(() => {\n        this.logger.info(\n          'CallService',\n          '✅ Temporary call accepted:',\n          acceptedCall\n        );\n\n        // Arrêter la sonnerie\n        this.stop('ringtone');\n        this.play('call-connected');\n\n        // Mettre à jour l'état\n        this.activeCall.next(acceptedCall);\n        this.incomingCall.next(null);\n\n        observer.next(acceptedCall);\n        observer.complete();\n      }, 300);\n    });\n  }\n\n  /**\n   * Rejette un appel entrant\n   */\n  rejectCall(callId: string, reason?: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '❌ Rejecting call:', callId);\n\n    return this.apollo\n      .mutate<{ rejectCall: CallSuccess }>({\n        mutation: REJECT_CALL_MUTATION,\n        variables: { callId, reason: reason || 'User rejected' },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.rejectCall;\n          if (!success) {\n            throw new Error('Failed to reject call');\n          }\n\n          this.logger.info(\n            'CallService',\n            '✅ Call rejected successfully:',\n            success\n          );\n\n          // Arrêter la sonnerie et jouer le son de fin\n          this.stop('ringtone');\n          this.play('call-end');\n\n          // Nettoyer l'état\n          this.incomingCall.next(null);\n          this.activeCall.next(null);\n          this.cleanupWebRTC();\n\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error rejecting call:', error);\n          return throwError(() => new Error('Failed to reject call'));\n        })\n      );\n  }\n\n  /**\n   * Termine un appel actif\n   */\n  endCall(callId: string): Observable<CallSuccess> {\n    this.logger.info('CallService', '🔚 Ending call:', callId);\n\n    return this.apollo\n      .mutate<{ endCall: CallSuccess }>({\n        mutation: END_CALL_MUTATION,\n        variables: { callId },\n      })\n      .pipe(\n        map((result) => {\n          const success = result.data?.endCall;\n          if (!success) {\n            throw new Error('Failed to end call');\n          }\n\n          this.logger.info(\n            'CallService',\n            '✅ Call ended successfully:',\n            success\n          );\n\n          // Jouer le son de fin\n          this.play('call-end');\n\n          // Nettoyer l'état\n          this.handleCallEnd();\n\n          return success;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error ending call:', error);\n\n          // Fallback: nettoyer localement même en cas d'erreur\n          this.handleCallEnd();\n\n          return of({\n            success: true,\n            message: 'Call ended locally (server error)',\n          });\n        })\n      );\n  }\n\n  // ===== MÉTHODES WEBRTC PRIVÉES =====\n\n  /**\n   * Démarre les médias pour un appel sortant\n   */\n  private startOutgoingCallMedia(callType: CallType): void {\n    this.logger.info('CallService', '🎥 Starting outgoing call media');\n\n    this.getUserMedia(callType)\n      .then((stream) => {\n        this.addLocalStreamToPeerConnection(stream);\n        this.attachLocalStream();\n\n        // Créer une offre WebRTC\n        return this.createOffer();\n      })\n      .then((offer) => {\n        this.logger.info('CallService', '✅ Offer created for outgoing call');\n        // Dans un vrai scénario, on enverrait cette offre au destinataire\n        this.sendSignal('offer', JSON.stringify(offer));\n      })\n      .catch((error) => {\n        this.logger.error(\n          'CallService',\n          '❌ Error starting outgoing call media:',\n          error\n        );\n      });\n  }\n\n  /**\n   * Ajoute le stream local à la PeerConnection\n   */\n  private addLocalStreamToPeerConnection(stream: MediaStream): void {\n    if (!this.peerConnection) {\n      this.logger.error('CallService', 'No PeerConnection available');\n      return;\n    }\n\n    try {\n      stream.getTracks().forEach((track) => {\n        this.peerConnection!.addTrack(track, stream);\n        this.logger.debug(\n          'CallService',\n          `Added ${track.kind} track to PeerConnection`\n        );\n      });\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        'Error adding stream to PeerConnection:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Attache le stream local à l'élément vidéo\n   */\n  private attachLocalStream(): void {\n    if (this.localVideoElement && this.localStream) {\n      this.localVideoElement.srcObject = this.localStream;\n      this.logger.debug(\n        'CallService',\n        'Local stream attached to video element'\n      );\n    }\n  }\n\n  /**\n   * Attache le stream distant à l'élément vidéo\n   */\n  private attachRemoteStream(): void {\n    if (this.remoteVideoElement && this.remoteStream) {\n      this.remoteVideoElement.srcObject = this.remoteStream;\n      this.logger.debug(\n        'CallService',\n        'Remote stream attached to video element'\n      );\n    }\n  }\n\n  /**\n   * Crée une offre WebRTC\n   */\n  private async createOffer(): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('PeerConnection not initialized');\n    }\n\n    try {\n      const offer = await this.peerConnection.createOffer({\n        offerToReceiveAudio: true,\n        offerToReceiveVideo: true,\n      });\n\n      await this.peerConnection.setLocalDescription(offer);\n      this.logger.info(\n        'CallService',\n        '✅ Offer created and set as local description'\n      );\n\n      return offer;\n    } catch (error) {\n      this.logger.error('CallService', '❌ Error creating offer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Crée une réponse WebRTC\n   */\n  private async createAnswer(\n    offer: RTCSessionDescriptionInit\n  ): Promise<RTCSessionDescriptionInit> {\n    if (!this.peerConnection) {\n      throw new Error('PeerConnection not initialized');\n    }\n\n    try {\n      await this.peerConnection.setRemoteDescription(offer);\n      this.logger.info('CallService', '✅ Remote description set');\n\n      const answer = await this.peerConnection.createAnswer();\n      await this.peerConnection.setLocalDescription(answer);\n      this.logger.info(\n        'CallService',\n        '✅ Answer created and set as local description'\n      );\n\n      return answer;\n    } catch (error) {\n      this.logger.error('CallService', '❌ Error creating answer:', error);\n      throw error;\n    }\n  }\n\n  /**\n   * Gère une offre distante\n   */\n  private async handleRemoteOffer(signal: CallSignal): Promise<void> {\n    try {\n      const offer = JSON.parse(signal.data);\n      this.logger.info('CallService', '📥 Handling remote offer:', offer);\n\n      // Obtenir les médias utilisateur\n      const stream = await this.getUserMedia(CallType.AUDIO);\n      this.addLocalStreamToPeerConnection(stream);\n\n      // Créer et envoyer la réponse\n      const answer = await this.createAnswer(offer);\n      this.sendSignal('answer', JSON.stringify(answer));\n\n      this.logger.info('CallService', '✅ Remote offer handled successfully');\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        '❌ Error handling remote offer:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Gère une réponse distante\n   */\n  private async handleRemoteAnswer(signal: CallSignal): Promise<void> {\n    try {\n      const answer = JSON.parse(signal.data);\n      this.logger.info('CallService', '📥 Handling remote answer:', answer);\n\n      if (this.peerConnection) {\n        await this.peerConnection.setRemoteDescription(answer);\n        this.logger.info(\n          'CallService',\n          '✅ Remote answer handled successfully'\n        );\n      }\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        '❌ Error handling remote answer:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Gère un candidat ICE distant\n   */\n  private async handleRemoteICECandidate(signal: CallSignal): Promise<void> {\n    try {\n      const candidate = JSON.parse(signal.data);\n      this.logger.debug(\n        'CallService',\n        '📥 Handling remote ICE candidate:',\n        candidate\n      );\n\n      if (this.peerConnection && candidate) {\n        await this.peerConnection.addIceCandidate(\n          new RTCIceCandidate(candidate)\n        );\n        this.logger.debug(\n          'CallService',\n          '✅ Remote ICE candidate added successfully'\n        );\n      }\n    } catch (error) {\n      this.logger.error(\n        'CallService',\n        '❌ Error handling ICE candidate:',\n        error\n      );\n    }\n  }\n\n  /**\n   * Envoie un signal WebRTC\n   */\n  private sendSignal(type: string, data: string): void {\n    if (!this.currentCallId) {\n      this.logger.error('CallService', 'No current call ID for signal');\n      return;\n    }\n\n    this.logger.debug('CallService', `📤 Sending signal: ${type}`);\n\n    // Utiliser GraphQL pour envoyer le signal\n    this.apollo\n      .mutate({\n        mutation: SEND_CALL_SIGNAL_MUTATION,\n        variables: {\n          callId: this.currentCallId,\n          signalType: type,\n          signalData: data,\n        },\n      })\n      .subscribe({\n        next: (result) => {\n          this.logger.debug('CallService', '✅ Signal sent successfully');\n        },\n        error: (error) => {\n          this.logger.error('CallService', '❌ Error sending signal:', error);\n\n          // Fallback: simulation locale pour les tests\n          setTimeout(() => {\n            this.handleCallSignal({\n              callId: this.currentCallId!,\n              senderId: 'other-user',\n              type,\n              data,\n              timestamp: new Date().toISOString(),\n            });\n          }, 100);\n        },\n      });\n  }\n\n  // ===== MÉTHODES UTILITAIRES =====\n\n  /**\n   * Configure les éléments vidéo\n   */\n  setVideoElements(\n    localVideo: HTMLVideoElement,\n    remoteVideo: HTMLVideoElement\n  ): void {\n    this.localVideoElement = localVideo;\n    this.remoteVideoElement = remoteVideo;\n    this.logger.debug('CallService', 'Video elements configured');\n  }\n\n  /**\n   * Bascule l'audio\n   */\n  toggleAudio(): boolean {\n    this.isAudioEnabled = !this.isAudioEnabled;\n\n    if (this.localStream) {\n      this.localStream.getAudioTracks().forEach((track) => {\n        track.enabled = this.isAudioEnabled;\n      });\n    }\n\n    this.logger.info(\n      'CallService',\n      `Audio ${this.isAudioEnabled ? 'enabled' : 'disabled'}`\n    );\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Bascule la vidéo\n   */\n  toggleVideo(): boolean {\n    this.isVideoEnabled = !this.isVideoEnabled;\n\n    if (this.localStream) {\n      this.localStream.getVideoTracks().forEach((track) => {\n        track.enabled = this.isVideoEnabled;\n      });\n    }\n\n    this.logger.info(\n      'CallService',\n      `Video ${this.isVideoEnabled ? 'enabled' : 'disabled'}`\n    );\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Bascule les médias via GraphQL\n   */\n  toggleMedia(\n    callId: string,\n    enableVideo?: boolean,\n    enableAudio?: boolean\n  ): Observable<CallSuccess> {\n    return this.apollo\n      .mutate<{ toggleCallMedia: CallSuccess }>({\n        mutation: TOGGLE_CALL_MEDIA_MUTATION,\n        variables: { callId, video: enableVideo, audio: enableAudio },\n      })\n      .pipe(\n        map((result) => {\n          return result.data!.toggleCallMedia;\n        }),\n        catchError((error) => {\n          this.logger.error('CallService', 'Error toggling media:', error);\n          return throwError(\n            () => new Error('Erreur lors du changement des médias')\n          );\n        })\n      );\n  }\n\n  /**\n   * Gère la fin d'appel\n   */\n  private handleCallEnd(): void {\n    this.logger.info('CallService', '🔚 Handling call end');\n\n    this.stopAllSounds();\n    this.activeCall.next(null);\n    this.incomingCall.next(null);\n    this.currentCallId = null;\n    this.cleanupWebRTC();\n  }\n\n  /**\n   * Gère l'échec de connexion WebRTC\n   */\n  private handleConnectionFailure(): void {\n    this.logger.error('CallService', '❌ WebRTC connection failed');\n\n    this.play('call-end');\n    this.handleCallEnd();\n  }\n\n  /**\n   * Nettoie les ressources WebRTC\n   */\n  private cleanupWebRTC(): void {\n    this.logger.debug('CallService', '🧹 Cleaning up WebRTC resources');\n\n    if (this.localStream) {\n      this.localStream.getTracks().forEach((track) => {\n        track.stop();\n        this.logger.debug('CallService', `Stopped ${track.kind} track`);\n      });\n      this.localStream = null;\n    }\n\n    if (this.peerConnection) {\n      this.peerConnection.close();\n      this.peerConnection = null;\n      this.logger.debug('CallService', 'PeerConnection closed');\n    }\n\n    this.remoteStream = null;\n    this.webrtcExchangeStarted = false;\n\n    // Réinitialiser les éléments vidéo\n    if (this.localVideoElement) {\n      this.localVideoElement.srcObject = null;\n    }\n    if (this.remoteVideoElement) {\n      this.remoteVideoElement.srcObject = null;\n    }\n  }\n\n  /**\n   * Simule un appel entrant (pour les tests)\n   */\n  private simulateIncomingCall(originalCall: Call): void {\n    const incomingCall: IncomingCall = {\n      id: originalCall.id,\n      caller: originalCall.recipient, // Inverser caller/recipient\n      type: originalCall.type,\n      conversationId: originalCall.conversationId,\n      offer: '{\"type\":\"offer\",\"sdp\":\"fake-offer\"}',\n      timestamp: new Date().toISOString(),\n    };\n\n    this.logger.info(\n      'CallService',\n      '📞 Simulating incoming call:',\n      incomingCall\n    );\n\n    this.stop('ringtone'); // Arrêter la sonnerie sortante\n    this.incomingCall.next(incomingCall);\n    this.activeCall.next(null); // Nettoyer l'appel sortant\n\n    // Jouer la sonnerie d'appel entrant\n    this.play('ringtone', true);\n  }\n\n  /**\n   * Active les sons après interaction utilisateur\n   */\n  enableSounds(): void {\n    this.logger.info('CallService', 'Sounds enabled after user interaction');\n  }\n\n  // ===== GETTERS =====\n\n  /**\n   * Obtient l'état audio actuel\n   */\n  get audioEnabled(): boolean {\n    return this.isAudioEnabled;\n  }\n\n  /**\n   * Obtient l'état vidéo actuel\n   */\n  get videoEnabled(): boolean {\n    return this.isVideoEnabled;\n  }\n\n  /**\n   * Obtient l'appel actuel\n   */\n  get currentCall(): Call | null {\n    return this.activeCall.value;\n  }\n\n  /**\n   * Obtient l'appel entrant actuel\n   */\n  get currentIncomingCall(): IncomingCall | null {\n    return this.incomingCall.value;\n  }\n}\n"], "mappings": ";AAEA,SAASA,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAQC,EAAE,QAAQ,MAAM;AACxE,SAASC,GAAG,EAAEC,UAAU,QAA+B,gBAAgB;AACvE,SAEEC,QAAQ,EACRC,UAAU,QAIL,yBAAyB;AAChC,SACEC,sBAAsB,EACtBC,oBAAoB,EACpBC,oBAAoB,EACpBC,iBAAiB,EACjBC,0BAA0B,EAC1BC,0BAA0B,EAC1BC,gCAAgC,EAChCC,wBAAwB,EACxBC,yBAAyB,QACpB,4BAA4B;;;;AAMnC,OAAM,MAAOC,WAAW;EAqCtBC,YAAoBC,MAAc,EAAUC,MAAqB;IAA7C,KAAAD,MAAM,GAANA,MAAM;IAAkB,KAAAC,MAAM,GAANA,MAAM;IApClD;IACQ,KAAAC,UAAU,GAAG,IAAIrB,eAAe,CAAc,IAAI,CAAC;IACnD,KAAAsB,YAAY,GAAG,IAAItB,eAAe,CAAsB,IAAI,CAAC;IAC7D,KAAAuB,WAAW,GAAG,IAAIvB,eAAe,CAAoB,IAAI,CAAC;IAElE;IACO,KAAAwB,WAAW,GAAG,IAAI,CAACH,UAAU,CAACI,YAAY,EAAE;IAC5C,KAAAC,aAAa,GAAG,IAAI,CAACJ,YAAY,CAACG,YAAY,EAAE;IAChD,KAAAE,YAAY,GAAG,IAAI,CAACJ,WAAW,CAACE,YAAY,EAAE;IAErD;IACQ,KAAAG,cAAc,GAAG,IAAI;IACrB,KAAAC,cAAc,GAAG,IAAI;IAE7B;IACQ,KAAAC,MAAM,GAAwC,EAAE;IAChD,KAAAC,SAAS,GAA+B,EAAE;IAElD;IACQ,KAAAC,cAAc,GAA6B,IAAI;IAC/C,KAAAC,WAAW,GAAuB,IAAI;IACtC,KAAAC,YAAY,GAAuB,IAAI;IACvC,KAAAC,iBAAiB,GAA4B,IAAI;IACjD,KAAAC,kBAAkB,GAA4B,IAAI;IAClD,KAAAC,aAAa,GAAkB,IAAI;IACnC,KAAAC,qBAAqB,GAAY,KAAK;IAE9C;IACiB,KAAAC,SAAS,GAAqB;MAC7CC,UAAU,EAAE,CACV;QAAEC,IAAI,EAAE;MAA8B,CAAE,EACxC;QAAEA,IAAI,EAAE;MAA+B,CAAE,EACzC;QAAEA,IAAI,EAAE;MAA+B,CAAE;KAE5C;IAGC,IAAI,CAACrB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,gCAAgC,CAAC;IACjE,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,uBAAuB,EAAE;IAC9B,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACzB,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,wCAAwC,CAAC;EAC3E;EAEAI,WAAWA,CAAA;IACT,IAAI,CAAC1B,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAC/D,IAAI,CAACK,aAAa,EAAE;IACpB,IAAI,CAACC,aAAa,EAAE;EACtB;EAEA;;;EAGQL,gBAAgBA,CAAA;IACtB,IAAI,CAACvB,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEA;;;EAGQA,qBAAqBA,CAAA;IAC3B,IAAI,CAAC9B,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,8BAA8B,CAAC;IAEhE;IACA,IAAI,CAACE,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,IAAI,CAAC;IAC/D,IAAI,CAACA,oBAAoB,CACvB,gBAAgB,EAChB,CAAC,MAAM,EAAE,MAAM,EAAE,MAAM,CAAC,EACxB,GAAG,EACH,KAAK,CACN;IACD,IAAI,CAACA,oBAAoB,CAAC,UAAU,EAAE,CAAC,GAAG,EAAE,MAAM,EAAE,MAAM,CAAC,EAAE,GAAG,EAAE,KAAK,CAAC;EAC1E;EAEA;;;EAGQA,oBAAoBA,CAC1BC,IAAY,EACZC,WAAqB,EACrBC,QAAgB,EAChBC,IAAa;IAEb,IAAI;MACF;MACA,MAAMC,YAAY,GAAG,KAAKC,MAAM,CAACC,YAAY,IAC1CD,MAAc,CAACE,kBAAkB,EAAC,CAAE;MAEvC;MACA,MAAMC,UAAU,GAAGJ,YAAY,CAACI,UAAU;MAC1C,MAAMC,UAAU,GAAGD,UAAU,GAAGN,QAAQ;MACxC,MAAMQ,MAAM,GAAGN,YAAY,CAACO,YAAY,CAAC,CAAC,EAAEF,UAAU,EAAED,UAAU,CAAC;MACnE,MAAMI,WAAW,GAAGF,MAAM,CAACG,cAAc,CAAC,CAAC,CAAC;MAE5C;MACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGL,UAAU,EAAEK,CAAC,EAAE,EAAE;QACnC,IAAIC,MAAM,GAAG,CAAC;QACdd,WAAW,CAACe,OAAO,CAAC,CAACC,IAAI,EAAEC,KAAK,KAAI;UAClC,MAAMC,SAAS,GAAG,GAAG,GAAGlB,WAAW,CAACmB,MAAM;UAC1C,MAAMC,KAAK,GAAIP,CAAC,GAAGN,UAAU,GAAIS,IAAI,GAAG,CAAC,GAAGK,IAAI,CAACC,EAAE;UACnDR,MAAM,IAAIO,IAAI,CAACE,GAAG,CAACH,KAAK,CAAC,GAAGF,SAAS;QACvC,CAAC,CAAC;QAEF;QACA,MAAMM,QAAQ,GAAGH,IAAI,CAACE,GAAG,CAAEV,CAAC,GAAGL,UAAU,GAAIa,IAAI,CAACC,EAAE,CAAC;QACrDX,WAAW,CAACE,CAAC,CAAC,GAAGC,MAAM,GAAGU,QAAQ;;MAGpC;MACA,MAAMC,KAAK,GAAG,IAAIC,KAAK,EAAE;MACzBD,KAAK,CAACvB,IAAI,GAAGA,IAAI;MAEjB;MACCuB,KAAa,CAACE,UAAU,GAAG,MAAK;QAC/B,MAAMC,MAAM,GAAGzB,YAAY,CAAC0B,kBAAkB,EAAE;QAChDD,MAAM,CAACnB,MAAM,GAAGA,MAAM;QACtBmB,MAAM,CAAC1B,IAAI,GAAGA,IAAI;QAClB0B,MAAM,CAACE,OAAO,CAAC3B,YAAY,CAAC4B,WAAW,CAAC;QACxCH,MAAM,CAACI,KAAK,EAAE;QAEd,IAAI,CAAC9B,IAAI,EAAE;UACT+B,UAAU,CAAC,MAAK;YACd,IAAI,CAACvD,SAAS,CAACqB,IAAI,CAAC,GAAG,KAAK;UAC9B,CAAC,EAAEE,QAAQ,GAAG,IAAI,CAAC;;QAGrB,OAAO2B,MAAM;MACf,CAAC;MAED,IAAI,CAACnD,MAAM,CAACsB,IAAI,CAAC,GAAG0B,KAAK;MACzB,IAAI,CAAC/C,SAAS,CAACqB,IAAI,CAAC,GAAG,KAAK;MAE5B,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,oBAAoBG,IAAI,wBAAwB,CACjD;KACF,CAAC,OAAOmC,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,mCAAmCnC,IAAI,IAAI,EAC3CmC,KAAK,CACN;;EAEL;EAEA;;;EAGAC,IAAIA,CAACpC,IAAY,EAAEG,IAAA,GAAgB,KAAK;IACtC,IAAI;MACF,MAAMkC,KAAK,GAAG,IAAI,CAAC3D,MAAM,CAACsB,IAAI,CAAC;MAC/B,IAAI,CAACqC,KAAK,EAAE;QACV,IAAI,CAACrE,MAAM,CAACsE,IAAI,CAAC,aAAa,EAAE,UAAUtC,IAAI,aAAa,CAAC;QAC5D;;MAGF,IAAI,CAAC,IAAI,CAACrB,SAAS,CAACqB,IAAI,CAAC,EAAE;QACzB,IAAKqC,KAAa,CAACT,UAAU,EAAE;UAC5BS,KAAa,CAACE,aAAa,GAAIF,KAAa,CAACT,UAAU,EAAE;UAC1D,IAAI,CAACjD,SAAS,CAACqB,IAAI,CAAC,GAAG,IAAI;UAC3B,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,4BAA4BG,IAAI,EAAE,CAAC;SACrE,MAAM;UACLqC,KAAK,CAACG,WAAW,GAAG,CAAC;UACrBH,KAAK,CAAClC,IAAI,GAAGA,IAAI;UACjBkC,KAAK,CAACD,IAAI,EAAE,CAACK,KAAK,CAAEN,KAAK,IAAI;YAC3B,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,wBAAwBnC,IAAI,IAAI,EAChCmC,KAAK,CACN;UACH,CAAC,CAAC;UACF,IAAI,CAACxD,SAAS,CAACqB,IAAI,CAAC,GAAG,IAAI;;;KAGhC,CAAC,OAAOmC,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,6BAA6BnC,IAAI,IAAI,EACrCmC,KAAK,CACN;;EAEL;EAEA;;;EAGAO,IAAIA,CAAC1C,IAAY;IACf,IAAI;MACF,MAAMqC,KAAK,GAAG,IAAI,CAAC3D,MAAM,CAACsB,IAAI,CAAC;MAC/B,IAAI,CAACqC,KAAK,EAAE;MAEZ,IAAI,IAAI,CAAC1D,SAAS,CAACqB,IAAI,CAAC,EAAE;QACxB,IAAKqC,KAAa,CAACE,aAAa,EAAE;UAC/BF,KAAa,CAACE,aAAa,CAACG,IAAI,EAAE;UAClCL,KAAa,CAACE,aAAa,GAAG,IAAI;SACpC,MAAM;UACLF,KAAK,CAACM,KAAK,EAAE;UACbN,KAAK,CAACG,WAAW,GAAG,CAAC;;QAEvB,IAAI,CAAC7D,SAAS,CAACqB,IAAI,CAAC,GAAG,KAAK;QAC5B,IAAI,CAAChC,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,kBAAkBG,IAAI,EAAE,CAAC;;KAE7D,CAAC,OAAOmC,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,yBAAyBnC,IAAI,IAAI,EACjCmC,KAAK,CACN;;EAEL;EAEA;;;EAGAvC,aAAaA,CAAA;IACXgD,MAAM,CAACC,IAAI,CAAC,IAAI,CAACnE,MAAM,CAAC,CAACsC,OAAO,CAAEhB,IAAI,IAAI;MACxC,IAAI,CAAC0C,IAAI,CAAC1C,IAAI,CAAC;IACjB,CAAC,CAAC;EACJ;EAEA;;;EAGQR,uBAAuBA,CAAA;IAC7B,IAAI,CAACxB,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;IACjE,IAAI,CAACiD,wBAAwB,EAAE;IAC/B,IAAI,CAACC,4BAA4B,EAAE;IACnC,IAAI,CAACC,sBAAsB,EAAE;EAC/B;EAEA;;;EAGQF,wBAAwBA,CAAA;IAC9B,IAAI,CAAC9E,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,2CAA2C,CAC5C;IAED,IAAI,CAAC9B,MAAM,CACRkF,SAAS,CAAiC;MACzCC,KAAK,EAAEzF,0BAA0B;MACjC0F,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEnF,YAAY,EAAE;UACtB,IAAI,CAACF,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,yBAAyB,EACzB+D,IAAI,CAACnF,YAAY,CAClB;UACD,IAAI,CAACqF,kBAAkB,CAACF,IAAI,CAACnF,YAAY,CAAC;;QAE5C,IAAIoF,MAAM,EAAE;UACV,IAAI,CAACtF,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCmB,MAAM,CACP;;MAEL,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,sCAAsC,EACtCA,KAAK,CACN;QACD;QACAD,UAAU,CAAC,MAAM,IAAI,CAACY,wBAAwB,EAAE,EAAE,IAAI,CAAC;MACzD;KACD,CAAC;EACN;EAEA;;;EAGQC,4BAA4BA,CAAA;IAClC,IAAI,CAAC/E,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,gDAAgD,CACjD;IAED,IAAI,CAAC9B,MAAM,CACRkF,SAAS,CAA8B;MACtCC,KAAK,EAAExF,gCAAgC;MACvCyF,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEG,iBAAiB,EAAE;UAC3B,IAAI,CAACxF,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,sBAAsB,EACtB+D,IAAI,CAACG,iBAAiB,CACvB;UACD,IAAI,CAACC,sBAAsB,CAACJ,IAAI,CAACG,iBAAiB,CAAC;;QAErD,IAAIF,MAAM,EAAE;UACV,IAAI,CAACtF,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCmB,MAAM,CACP;;MAEL,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDD,UAAU,CAAC,MAAM,IAAI,CAACa,4BAA4B,EAAE,EAAE,IAAI,CAAC;MAC7D;KACD,CAAC;EACN;EAEA;;;EAGQC,sBAAsBA,CAAA;IAC5B,IAAI,CAAChF,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,yCAAyC,CAAC;IAE3E,IAAI,CAAC9B,MAAM,CACRkF,SAAS,CAA6B;MACrCC,KAAK,EAAEvF,wBAAwB;MAC/BwF,WAAW,EAAE;KACd,CAAC,CACDF,SAAS,CAAC;MACTG,IAAI,EAAEA,CAAC;QAAEC,IAAI;QAAEC;MAAM,CAAE,KAAI;QACzB,IAAID,IAAI,EAAEK,UAAU,EAAE;UACpB,IAAI,CAAC1F,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,uBAAuB,EACvBwD,IAAI,CAACK,UAAU,CAChB;UACD,IAAI,CAACC,gBAAgB,CAACN,IAAI,CAACK,UAAU,CAAC;;QAExC,IAAIJ,MAAM,EAAE;UACV,IAAI,CAACtF,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,kCAAkC,EAClCmB,MAAM,CACP;;MAEL,CAAC;MACDnB,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;QACDD,UAAU,CAAC,MAAM,IAAI,CAACc,sBAAsB,EAAE,EAAE,IAAI,CAAC;MACvD;KACD,CAAC;EACN;EAEA;;;EAGQvD,gBAAgBA,CAAA;IACtB,IAAI,CAACzB,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,wBAAwB,CAAC;IAC1D,IAAI,CAAC+D,oBAAoB,EAAE;EAC7B;EAEA;;;EAGQA,oBAAoBA,CAAA;IAC1B,IAAI;MACF,IAAI,CAAChF,cAAc,GAAG,IAAIiF,iBAAiB,CAAC,IAAI,CAAC1E,SAAS,CAAC;MAC3D,IAAI,CAACnB,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,qCAAqC,CAAC;MAEvE;MACA,IAAI,CAACjB,cAAc,CAACkF,cAAc,GAAIC,KAAK,IAAI;QAC7C,IAAIA,KAAK,CAACC,SAAS,IAAI,IAAI,CAAC/E,aAAa,EAAE;UACzC,IAAI,CAACjB,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,0BAA0B,EAC1BkE,KAAK,CAACC,SAAS,CAChB;UACD,IAAI,CAACC,UAAU,CAAC,eAAe,EAAEC,IAAI,CAACC,SAAS,CAACJ,KAAK,CAACC,SAAS,CAAC,CAAC;;MAErE,CAAC;MAED;MACA,IAAI,CAACpF,cAAc,CAACwF,OAAO,GAAIL,KAAK,IAAI;QACtC,IAAI,CAAC/F,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,wBAAwB,EACxByE,KAAK,CAACM,KAAK,CAACC,IAAI,CACjB;QACD,IAAI,CAACxF,YAAY,GAAGiF,KAAK,CAACQ,OAAO,CAAC,CAAC,CAAC;QACpC,IAAI,CAACC,kBAAkB,EAAE;MAC3B,CAAC;MAED;MACA,IAAI,CAAC5F,cAAc,CAAC6F,uBAAuB,GAAG,MAAK;QACjD,IAAI,CAACzG,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,2BAA2B,EAC3B,IAAI,CAACjB,cAAc,EAAE8F,eAAe,CACrC;QAED,IAAI,IAAI,CAAC9F,cAAc,EAAE8F,eAAe,KAAK,WAAW,EAAE;UACxD,IAAI,CAAC1G,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;UAClE,IAAI,CAACoD,IAAI,CAAC,UAAU,CAAC;UACrB,IAAI,CAACN,IAAI,CAAC,gBAAgB,CAAC;SAC5B,MAAM,IAAI,IAAI,CAACxD,cAAc,EAAE8F,eAAe,KAAK,QAAQ,EAAE;UAC5D,IAAI,CAAC1G,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,4BAA4B,CAAC;UAC9D,IAAI,CAACwC,uBAAuB,EAAE;;MAElC,CAAC;MAED;MACA,IAAI,CAAC/F,cAAc,CAACgG,0BAA0B,GAAG,MAAK;QACpD,IAAI,CAAC5G,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,uBAAuB,EACvB,IAAI,CAACjB,cAAc,EAAEiG,kBAAkB,CACxC;MACH,CAAC;KACF,CAAC,OAAO1C,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,gCAAgC,EAAEA,KAAK,CAAC;;EAE7E;EAEA;;;EAGQoB,kBAAkBA,CAACuB,IAAkB;IAC3C,IAAI,CAAC9G,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,yBAAyB,EAAEwF,IAAI,CAAC;IAEhE,IAAI,CAAC5G,YAAY,CAACkF,IAAI,CAAC0B,IAAI,CAAC;IAC5B,IAAI,CAAC7F,aAAa,GAAG6F,IAAI,CAACC,EAAE;IAC5B,IAAI,CAAC3C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;IAE3B;IACA,IAAI,CAAC4C,sBAAsB,CAACF,IAAI,CAAC;EACnC;EAEA;;;EAGcE,sBAAsBA,CAACF,IAAkB;IAAA,IAAAG,KAAA;IAAA,OAAAC,iBAAA;MACrD,IAAI;QACFD,KAAI,CAACjH,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,oCAAoC,CAAC;QAEtE;QACA,IAAI,CAACoF,KAAI,CAACrG,cAAc,EAAE;UACxBqG,KAAI,CAACrB,oBAAoB,EAAE;;QAG7B;QACA,MAAMuB,MAAM,SAASF,KAAI,CAACG,YAAY,CAACN,IAAI,CAACO,IAAI,CAAC;QACjDJ,KAAI,CAACK,8BAA8B,CAACH,MAAM,CAAC;OAC5C,CAAC,OAAOhD,KAAK,EAAE;QACd8C,KAAI,CAACjH,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,oCAAoC,EACpCA,KAAK,CACN;;IACF;EACH;EAEA;;;EAGQsB,sBAAsBA,CAACqB,IAAU;IACvC,IAAI,CAAC9G,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B,EAAEwF,IAAI,CAAC;IAErE,IAAIA,IAAI,CAACC,EAAE,KAAK,IAAI,CAAC9F,aAAa,EAAE;MAClC,IAAI,CAAChB,UAAU,CAACmF,IAAI,CAAC0B,IAAI,CAAC;MAE1B,QAAQA,IAAI,CAACS,MAAM;QACjB,KAAKpI,UAAU,CAACqI,SAAS;UACvB,IAAI,CAAC9C,IAAI,CAAC,UAAU,CAAC;UACrB,IAAI,CAACN,IAAI,CAAC,gBAAgB,CAAC;UAC3B;QACF,KAAKjF,UAAU,CAACsI,KAAK;QACrB,KAAKtI,UAAU,CAACuI,QAAQ;UACtB,IAAI,CAACC,aAAa,EAAE;UACpB;;;EAGR;EAEA;;;EAGQhC,gBAAgBA,CAACiC,MAAkB;IACzC,IAAIA,MAAM,CAACC,MAAM,KAAK,IAAI,CAAC5G,aAAa,EAAE;MACxC,IAAI,CAACjB,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,oCAAoC,CAAC;MACtE;;IAGF,IAAI,CAAC7B,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,yBAAyB,EAAE+F,MAAM,CAACP,IAAI,CAAC;IAExE,QAAQO,MAAM,CAACP,IAAI;MACjB,KAAK,OAAO;QACV,IAAI,CAACS,iBAAiB,CAACF,MAAM,CAAC;QAC9B;MACF,KAAK,QAAQ;QACX,IAAI,CAACG,kBAAkB,CAACH,MAAM,CAAC;QAC/B;MACF,KAAK,eAAe;QAClB,IAAI,CAACI,wBAAwB,CAACJ,MAAM,CAAC;QACrC;MACF;QACE,IAAI,CAAC5H,MAAM,CAACsE,IAAI,CAAC,aAAa,EAAE,sBAAsB,EAAEsD,MAAM,CAACP,IAAI,CAAC;;EAE1E;EAEA;;;EAGcD,YAAYA,CAACa,QAAkB;IAAA,IAAAC,MAAA;IAAA,OAAAhB,iBAAA;MAC3CgB,MAAI,CAAClI,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,8BAA8B2G,QAAQ,EAAE,CAAC;MAEzE,MAAME,WAAW,GAA2B;QAC1CzE,KAAK,EAAE,IAAI;QACX0E,KAAK,EAAEH,QAAQ,KAAK/I,QAAQ,CAACmJ;OAC9B;MAEDH,MAAI,CAAClI,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,oBAAoB,EAAEsG,WAAW,CAAC;MAEnE,IAAI;QACF,MAAMhB,MAAM,SAASmB,SAAS,CAACC,YAAY,CAACnB,YAAY,CAACe,WAAW,CAAC;QACrED,MAAI,CAAClI,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,oCAAoC,CAAC;QAErE4G,MAAI,CAACrH,WAAW,GAAGsG,MAAM;QAEzB;QACAe,MAAI,CAAClI,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,kBAAkBsF,MAAM,CAACqB,SAAS,EAAE,CAACpF,MAAM,EAAE,CAC9C;QACD+D,MAAM,CAACqB,SAAS,EAAE,CAACxF,OAAO,CAAEqD,KAAK,IAAI;UACnC6B,MAAI,CAAClI,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,UAAUwE,KAAK,CAACC,IAAI,MAAMD,KAAK,CAACoC,KAAK,EAAE,CACxC;QACH,CAAC,CAAC;QAEF,OAAOtB,MAAM;OACd,CAAC,OAAOhD,KAAK,EAAE;QACd+D,MAAI,CAAClI,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,6BAA6B,EAAEA,KAAK,CAAC;QACtE,MAAM,IAAIuE,KAAK,CAAC,2CAA2C,CAAC;;IAC7D;EACH;EAEA;EAEA;;;EAGAC,YAAYA,CACVC,WAAmB,EACnBX,QAAkB,EAClBY,cAAuB;IAEvB,IAAI,CAAC7I,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAE;MAC5DsH,WAAW;MACXX,QAAQ;MACRY;KACD,CAAC;IAEF,IAAI,CAACD,WAAW,EAAE;MAChB,MAAMzE,KAAK,GAAG,IAAIuE,KAAK,CAAC,0BAA0B,CAAC;MACnD,IAAI,CAAC1I,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,qBAAqB,EAAEA,KAAK,CAAC;MAC9D,OAAOrF,UAAU,CAAC,MAAMqF,KAAK,CAAC;;IAGhC;IACA,MAAM0D,MAAM,GAAG,QAAQiB,IAAI,CAACC,GAAG,EAAE,IAAIzF,IAAI,CAAC0F,MAAM,EAAE,CAC/CC,QAAQ,CAAC,EAAE,CAAC,CACZC,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,EAAE;IAEjB;IACA,OAAO,IAAI,CAACnJ,MAAM,CACfoJ,MAAM,CAAyB;MAC9BC,QAAQ,EAAEhK,sBAAsB;MAChCiK,SAAS,EAAE;QACTT,WAAW;QACXX,QAAQ,EAAEA,QAAQ;QAClBJ,MAAM;QACNyB,KAAK,EAAE,qCAAqC;QAC5CT;;KAEH,CAAC,CACDU,IAAI,CACHvK,GAAG,CAAEwK,MAAM,IAAI;MACb,MAAM1C,IAAI,GAAG0C,MAAM,CAACnE,IAAI,EAAEsD,YAAY;MACtC,IAAI,CAAC7B,IAAI,EAAE;QACT,MAAM,IAAI4B,KAAK,CAAC,yBAAyB,CAAC;;MAG5C,IAAI,CAAC1I,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,gCAAgC,EAChCwF,IAAI,CACL;MAED;MACA,IAAI,CAAC1C,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;MAE3B;MACA,IAAI,CAACnE,UAAU,CAACmF,IAAI,CAAC0B,IAAI,CAAC;MAC1B,IAAI,CAAC7F,aAAa,GAAG6F,IAAI,CAACC,EAAE;MAE5B;MACA,IAAI,CAAC0C,sBAAsB,CAACxB,QAAQ,CAAC;MAErC,OAAOnB,IAAI;IACb,CAAC,CAAC,EACF7H,UAAU,CAAEkF,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,wBAAwB,EAAEA,KAAK,CAAC;MAEjE;MACA,OAAO,IAAI,CAACuF,qBAAqB,CAC/Bd,WAAW,EACXX,QAAQ,EACRY,cAAc,CACf;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQa,qBAAqBA,CAC3Bd,WAAmB,EACnBX,QAAkB,EAClBY,cAAuB;IAEvB,IAAI,CAAC7I,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,oCAAoC,CAAC;IAErE,MAAMqI,QAAQ,GAAS;MACrB5C,EAAE,EAAE,OAAO,GAAG+B,IAAI,CAACC,GAAG,EAAE;MACxBa,MAAM,EAAE;QACNC,GAAG,EAAE,cAAc;QACnB9C,EAAE,EAAE,cAAc;QAClB+C,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,qBAAqB;QAC5BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;OACR;MACDC,SAAS,EAAE;QACTN,GAAG,EAAEjB,WAAW;QAChB7B,EAAE,EAAE6B,WAAW;QACfkB,QAAQ,EAAE,YAAY;QACtBC,KAAK,EAAE,mBAAmB;QAC1BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;OACR;MACD7C,IAAI,EAAEY,QAAQ;MACdV,MAAM,EAAEpI,UAAU,CAACiL,OAAO;MAC1BC,SAAS,EAAE,IAAIvB,IAAI,EAAE,CAACwB,WAAW,EAAE;MACnCzB,cAAc,EAAEA,cAAc,IAAI;KACnC;IAED,OAAO,IAAIhK,UAAU,CAAQ0L,QAAQ,IAAI;MACvCrG,UAAU,CAAC,MAAK;QACd,IAAI,CAAClE,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,6BAA6B,EAC7BqI,QAAQ,CACT;QAED,IAAI,CAACvF,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;QAC3B,IAAI,CAACnE,UAAU,CAACmF,IAAI,CAACuE,QAAQ,CAAC;QAC9B,IAAI,CAAC1I,aAAa,GAAG0I,QAAQ,CAAC5C,EAAE;QAEhC;QACA,IAAI,CAAC0C,sBAAsB,CAACxB,QAAQ,CAAC;QAErC;QACA/D,UAAU,CAAC,MAAK;UACd,IAAI,CAACsG,oBAAoB,CAACb,QAAQ,CAAC;QACrC,CAAC,EAAE,IAAI,CAAC;QAERY,QAAQ,CAACnF,IAAI,CAACuE,QAAQ,CAAC;QACvBY,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;;;EAGAC,UAAUA,CAAC5D,IAAkB;IAC3B,IAAI,CAAC9G,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAEwF,IAAI,CAACC,EAAE,CAAC;IAEtE,IAAI,CAACD,IAAI,EAAE;MACT,MAAM3C,KAAK,GAAG,IAAIuE,KAAK,CAAC,mBAAmB,CAAC;MAC5C,IAAI,CAAC1I,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,mBAAmB,EAAEA,KAAK,CAAC;MAC5D,OAAOrF,UAAU,CAAC,MAAMqF,KAAK,CAAC;;IAGhC,OAAO,IAAI,CAACpE,MAAM,CACfoJ,MAAM,CAAuB;MAC5BC,QAAQ,EAAE/J,oBAAoB;MAC9BgK,SAAS,EAAE;QAAExB,MAAM,EAAEf,IAAI,CAACC;MAAE;KAC7B,CAAC,CACDwC,IAAI,CACHvK,GAAG,CAAEwK,MAAM,IAAI;MACb,MAAMmB,YAAY,GAAGnB,MAAM,CAACnE,IAAI,EAAEqF,UAAU;MAC5C,IAAI,CAACC,YAAY,EAAE;QACjB,MAAM,IAAIjC,KAAK,CAAC,uBAAuB,CAAC;;MAG1C,IAAI,CAAC1I,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,+BAA+B,EAC/BqJ,YAAY,CACb;MAED;MACA,IAAI,CAACjG,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACN,IAAI,CAAC,gBAAgB,CAAC;MAE3B;MACA,IAAI,CAACnE,UAAU,CAACmF,IAAI,CAACuF,YAAY,CAAC;MAClC,IAAI,CAACzK,YAAY,CAACkF,IAAI,CAAC,IAAI,CAAC;MAE5B,OAAOuF,YAAY;IACrB,CAAC,CAAC,EACF1L,UAAU,CAAEkF,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,mCAAmC,EACnCA,KAAK,CACN;MAED;MACA,OAAO,IAAI,CAACyG,mBAAmB,CAAC9D,IAAI,CAAC;IACvC,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQ8D,mBAAmBA,CAAC9D,IAAkB;IAC5C,IAAI,CAAC9G,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,oCAAoC,CAAC;IAErE,MAAMqJ,YAAY,GAAS;MACzB5D,EAAE,EAAED,IAAI,CAACC,EAAE;MACX6C,MAAM,EAAE9C,IAAI,CAAC8C,MAAM;MACnBO,SAAS,EAAE;QACTN,GAAG,EAAE,cAAc;QACnB9C,EAAE,EAAE,cAAc;QAClB+C,QAAQ,EAAE,KAAK;QACfC,KAAK,EAAE,qBAAqB;QAC5BC,IAAI,EAAE,MAAM;QACZC,QAAQ,EAAE,IAAI;QACdC,KAAK,EAAE;OACR;MACD7C,IAAI,EAAEP,IAAI,CAACO,IAAI;MACfE,MAAM,EAAEpI,UAAU,CAACqI,SAAS;MAC5B6C,SAAS,EAAE,IAAIvB,IAAI,EAAE,CAACwB,WAAW,EAAE;MACnCzB,cAAc,EAAE/B,IAAI,CAAC+B,cAAc,IAAI;KACxC;IAED,OAAO,IAAIhK,UAAU,CAAQ0L,QAAQ,IAAI;MACvCrG,UAAU,CAAC,MAAK;QACd,IAAI,CAAClE,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,4BAA4B,EAC5BqJ,YAAY,CACb;QAED;QACA,IAAI,CAACjG,IAAI,CAAC,UAAU,CAAC;QACrB,IAAI,CAACN,IAAI,CAAC,gBAAgB,CAAC;QAE3B;QACA,IAAI,CAACnE,UAAU,CAACmF,IAAI,CAACuF,YAAY,CAAC;QAClC,IAAI,CAACzK,YAAY,CAACkF,IAAI,CAAC,IAAI,CAAC;QAE5BmF,QAAQ,CAACnF,IAAI,CAACuF,YAAY,CAAC;QAC3BJ,QAAQ,CAACE,QAAQ,EAAE;MACrB,CAAC,EAAE,GAAG,CAAC;IACT,CAAC,CAAC;EACJ;EAEA;;;EAGAI,UAAUA,CAAChD,MAAc,EAAEiD,MAAe;IACxC,IAAI,CAAC9K,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mBAAmB,EAAEuG,MAAM,CAAC;IAE5D,OAAO,IAAI,CAAC9H,MAAM,CACfoJ,MAAM,CAA8B;MACnCC,QAAQ,EAAE9J,oBAAoB;MAC9B+J,SAAS,EAAE;QAAExB,MAAM;QAAEiD,MAAM,EAAEA,MAAM,IAAI;MAAe;KACvD,CAAC,CACDvB,IAAI,CACHvK,GAAG,CAAEwK,MAAM,IAAI;MACb,MAAMuB,OAAO,GAAGvB,MAAM,CAACnE,IAAI,EAAEwF,UAAU;MACvC,IAAI,CAACE,OAAO,EAAE;QACZ,MAAM,IAAIrC,KAAK,CAAC,uBAAuB,CAAC;;MAG1C,IAAI,CAAC1I,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,+BAA+B,EAC/ByJ,OAAO,CACR;MAED;MACA,IAAI,CAACrG,IAAI,CAAC,UAAU,CAAC;MACrB,IAAI,CAACN,IAAI,CAAC,UAAU,CAAC;MAErB;MACA,IAAI,CAAClE,YAAY,CAACkF,IAAI,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACnF,UAAU,CAACmF,IAAI,CAAC,IAAI,CAAC;MAC1B,IAAI,CAACzD,aAAa,EAAE;MAEpB,OAAOoJ,OAAO;IAChB,CAAC,CAAC,EACF9L,UAAU,CAAEkF,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,OAAOrF,UAAU,CAAC,MAAM,IAAI4J,KAAK,CAAC,uBAAuB,CAAC,CAAC;IAC7D,CAAC,CAAC,CACH;EACL;EAEA;;;EAGAsC,OAAOA,CAACnD,MAAc;IACpB,IAAI,CAAC7H,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iBAAiB,EAAEuG,MAAM,CAAC;IAE1D,OAAO,IAAI,CAAC9H,MAAM,CACfoJ,MAAM,CAA2B;MAChCC,QAAQ,EAAE7J,iBAAiB;MAC3B8J,SAAS,EAAE;QAAExB;MAAM;KACpB,CAAC,CACD0B,IAAI,CACHvK,GAAG,CAAEwK,MAAM,IAAI;MACb,MAAMuB,OAAO,GAAGvB,MAAM,CAACnE,IAAI,EAAE2F,OAAO;MACpC,IAAI,CAACD,OAAO,EAAE;QACZ,MAAM,IAAIrC,KAAK,CAAC,oBAAoB,CAAC;;MAGvC,IAAI,CAAC1I,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,4BAA4B,EAC5ByJ,OAAO,CACR;MAED;MACA,IAAI,CAAC3G,IAAI,CAAC,UAAU,CAAC;MAErB;MACA,IAAI,CAACuD,aAAa,EAAE;MAEpB,OAAOoD,OAAO;IAChB,CAAC,CAAC,EACF9L,UAAU,CAAEkF,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,oBAAoB,EAAEA,KAAK,CAAC;MAE7D;MACA,IAAI,CAACwD,aAAa,EAAE;MAEpB,OAAO5I,EAAE,CAAC;QACRgM,OAAO,EAAE,IAAI;QACbE,OAAO,EAAE;OACV,CAAC;IACJ,CAAC,CAAC,CACH;EACL;EAEA;EAEA;;;EAGQxB,sBAAsBA,CAACxB,QAAkB;IAC/C,IAAI,CAACjI,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,iCAAiC,CAAC;IAElE,IAAI,CAAC8F,YAAY,CAACa,QAAQ,CAAC,CACxBiD,IAAI,CAAE/D,MAAM,IAAI;MACf,IAAI,CAACG,8BAA8B,CAACH,MAAM,CAAC;MAC3C,IAAI,CAACgE,iBAAiB,EAAE;MAExB;MACA,OAAO,IAAI,CAACC,WAAW,EAAE;IAC3B,CAAC,CAAC,CACDF,IAAI,CAAE5B,KAAK,IAAI;MACd,IAAI,CAACtJ,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,mCAAmC,CAAC;MACpE;MACA,IAAI,CAAC2E,UAAU,CAAC,OAAO,EAAEC,IAAI,CAACC,SAAS,CAACmD,KAAK,CAAC,CAAC;IACjD,CAAC,CAAC,CACD7E,KAAK,CAAEN,KAAK,IAAI;MACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,uCAAuC,EACvCA,KAAK,CACN;IACH,CAAC,CAAC;EACN;EAEA;;;EAGQmD,8BAA8BA,CAACH,MAAmB;IACxD,IAAI,CAAC,IAAI,CAACvG,cAAc,EAAE;MACxB,IAAI,CAACZ,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,6BAA6B,CAAC;MAC/D;;IAGF,IAAI;MACFgD,MAAM,CAACqB,SAAS,EAAE,CAACxF,OAAO,CAAEqD,KAAK,IAAI;QACnC,IAAI,CAACzF,cAAe,CAACyK,QAAQ,CAAChF,KAAK,EAAEc,MAAM,CAAC;QAC5C,IAAI,CAACnH,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,SAASwE,KAAK,CAACC,IAAI,0BAA0B,CAC9C;MACH,CAAC,CAAC;KACH,CAAC,OAAOnC,KAAK,EAAE;MACd,IAAI,CAACnE,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,wCAAwC,EACxCA,KAAK,CACN;;EAEL;EAEA;;;EAGQgH,iBAAiBA,CAAA;IACvB,IAAI,IAAI,CAACpK,iBAAiB,IAAI,IAAI,CAACF,WAAW,EAAE;MAC9C,IAAI,CAACE,iBAAiB,CAACuK,SAAS,GAAG,IAAI,CAACzK,WAAW;MACnD,IAAI,CAACb,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,wCAAwC,CACzC;;EAEL;EAEA;;;EAGQ2E,kBAAkBA,CAAA;IACxB,IAAI,IAAI,CAACxF,kBAAkB,IAAI,IAAI,CAACF,YAAY,EAAE;MAChD,IAAI,CAACE,kBAAkB,CAACsK,SAAS,GAAG,IAAI,CAACxK,YAAY;MACrD,IAAI,CAACd,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,yCAAyC,CAC1C;;EAEL;EAEA;;;EAGcuJ,WAAWA,CAAA;IAAA,IAAAG,MAAA;IAAA,OAAArE,iBAAA;MACvB,IAAI,CAACqE,MAAI,CAAC3K,cAAc,EAAE;QACxB,MAAM,IAAI8H,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMY,KAAK,SAASiC,MAAI,CAAC3K,cAAc,CAACwK,WAAW,CAAC;UAClDI,mBAAmB,EAAE,IAAI;UACzBC,mBAAmB,EAAE;SACtB,CAAC;QAEF,MAAMF,MAAI,CAAC3K,cAAc,CAAC8K,mBAAmB,CAACpC,KAAK,CAAC;QACpDiC,MAAI,CAACvL,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,8CAA8C,CAC/C;QAED,OAAOgI,KAAK;OACb,CAAC,OAAOnF,KAAK,EAAE;QACdoH,MAAI,CAACvL,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,yBAAyB,EAAEA,KAAK,CAAC;QAClE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGcwH,YAAYA,CACxBrC,KAAgC;IAAA,IAAAsC,MAAA;IAAA,OAAA1E,iBAAA;MAEhC,IAAI,CAAC0E,MAAI,CAAChL,cAAc,EAAE;QACxB,MAAM,IAAI8H,KAAK,CAAC,gCAAgC,CAAC;;MAGnD,IAAI;QACF,MAAMkD,MAAI,CAAChL,cAAc,CAACiL,oBAAoB,CAACvC,KAAK,CAAC;QACrDsC,MAAI,CAAC5L,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,0BAA0B,CAAC;QAE3D,MAAMwK,MAAM,SAASF,MAAI,CAAChL,cAAc,CAAC+K,YAAY,EAAE;QACvD,MAAMC,MAAI,CAAChL,cAAc,CAAC8K,mBAAmB,CAACI,MAAM,CAAC;QACrDF,MAAI,CAAC5L,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,+CAA+C,CAChD;QAED,OAAOwK,MAAM;OACd,CAAC,OAAO3H,KAAK,EAAE;QACdyH,MAAI,CAAC5L,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,0BAA0B,EAAEA,KAAK,CAAC;QACnE,MAAMA,KAAK;;IACZ;EACH;EAEA;;;EAGc2D,iBAAiBA,CAACF,MAAkB;IAAA,IAAAmE,MAAA;IAAA,OAAA7E,iBAAA;MAChD,IAAI;QACF,MAAMoC,KAAK,GAAGpD,IAAI,CAAC8F,KAAK,CAACpE,MAAM,CAACvC,IAAI,CAAC;QACrC0G,MAAI,CAAC/L,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,2BAA2B,EAAEgI,KAAK,CAAC;QAEnE;QACA,MAAMnC,MAAM,SAAS4E,MAAI,CAAC3E,YAAY,CAAClI,QAAQ,CAAC+M,KAAK,CAAC;QACtDF,MAAI,CAACzE,8BAA8B,CAACH,MAAM,CAAC;QAE3C;QACA,MAAM2E,MAAM,SAASC,MAAI,CAACJ,YAAY,CAACrC,KAAK,CAAC;QAC7CyC,MAAI,CAAC9F,UAAU,CAAC,QAAQ,EAAEC,IAAI,CAACC,SAAS,CAAC2F,MAAM,CAAC,CAAC;QAEjDC,MAAI,CAAC/L,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,qCAAqC,CAAC;OACvE,CAAC,OAAO6C,KAAK,EAAE;QACd4H,MAAI,CAAC/L,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,gCAAgC,EAChCA,KAAK,CACN;;IACF;EACH;EAEA;;;EAGc4D,kBAAkBA,CAACH,MAAkB;IAAA,IAAAsE,MAAA;IAAA,OAAAhF,iBAAA;MACjD,IAAI;QACF,MAAM4E,MAAM,GAAG5F,IAAI,CAAC8F,KAAK,CAACpE,MAAM,CAACvC,IAAI,CAAC;QACtC6G,MAAI,CAAClM,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,4BAA4B,EAAEwK,MAAM,CAAC;QAErE,IAAII,MAAI,CAACtL,cAAc,EAAE;UACvB,MAAMsL,MAAI,CAACtL,cAAc,CAACiL,oBAAoB,CAACC,MAAM,CAAC;UACtDI,MAAI,CAAClM,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,sCAAsC,CACvC;;OAEJ,CAAC,OAAO6C,KAAK,EAAE;QACd+H,MAAI,CAAClM,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,iCAAiC,EACjCA,KAAK,CACN;;IACF;EACH;EAEA;;;EAGc6D,wBAAwBA,CAACJ,MAAkB;IAAA,IAAAuE,MAAA;IAAA,OAAAjF,iBAAA;MACvD,IAAI;QACF,MAAMlB,SAAS,GAAGE,IAAI,CAAC8F,KAAK,CAACpE,MAAM,CAACvC,IAAI,CAAC;QACzC8G,MAAI,CAACnM,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,mCAAmC,EACnCmE,SAAS,CACV;QAED,IAAImG,MAAI,CAACvL,cAAc,IAAIoF,SAAS,EAAE;UACpC,MAAMmG,MAAI,CAACvL,cAAc,CAACwL,eAAe,CACvC,IAAIC,eAAe,CAACrG,SAAS,CAAC,CAC/B;UACDmG,MAAI,CAACnM,MAAM,CAAC6B,KAAK,CACf,aAAa,EACb,2CAA2C,CAC5C;;OAEJ,CAAC,OAAOsC,KAAK,EAAE;QACdgI,MAAI,CAACnM,MAAM,CAACmE,KAAK,CACf,aAAa,EACb,iCAAiC,EACjCA,KAAK,CACN;;IACF;EACH;EAEA;;;EAGQ8B,UAAUA,CAACoB,IAAY,EAAEhC,IAAY;IAC3C,IAAI,CAAC,IAAI,CAACpE,aAAa,EAAE;MACvB,IAAI,CAACjB,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,+BAA+B,CAAC;MACjE;;IAGF,IAAI,CAACnE,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,sBAAsBwF,IAAI,EAAE,CAAC;IAE9D;IACA,IAAI,CAACtH,MAAM,CACRoJ,MAAM,CAAC;MACNC,QAAQ,EAAExJ,yBAAyB;MACnCyJ,SAAS,EAAE;QACTxB,MAAM,EAAE,IAAI,CAAC5G,aAAa;QAC1BqL,UAAU,EAAEjF,IAAI;QAChBkF,UAAU,EAAElH;;KAEf,CAAC,CACDJ,SAAS,CAAC;MACTG,IAAI,EAAGoE,MAAM,IAAI;QACf,IAAI,CAACxJ,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,4BAA4B,CAAC;MAChE,CAAC;MACDsC,KAAK,EAAGA,KAAK,IAAI;QACf,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,yBAAyB,EAAEA,KAAK,CAAC;QAElE;QACAD,UAAU,CAAC,MAAK;UACd,IAAI,CAACyB,gBAAgB,CAAC;YACpBkC,MAAM,EAAE,IAAI,CAAC5G,aAAc;YAC3BuL,QAAQ,EAAE,YAAY;YACtBnF,IAAI;YACJhC,IAAI;YACJoH,SAAS,EAAE,IAAI3D,IAAI,EAAE,CAACwB,WAAW;WAClC,CAAC;QACJ,CAAC,EAAE,GAAG,CAAC;MACT;KACD,CAAC;EACN;EAEA;EAEA;;;EAGAoC,gBAAgBA,CACdC,UAA4B,EAC5BC,WAA6B;IAE7B,IAAI,CAAC7L,iBAAiB,GAAG4L,UAAU;IACnC,IAAI,CAAC3L,kBAAkB,GAAG4L,WAAW;IACrC,IAAI,CAAC5M,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,2BAA2B,CAAC;EAC/D;EAEA;;;EAGAgL,WAAWA,CAAA;IACT,IAAI,CAACrM,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C,IAAI,IAAI,CAACK,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACiM,cAAc,EAAE,CAAC9J,OAAO,CAAEqD,KAAK,IAAI;QAClDA,KAAK,CAAC0G,OAAO,GAAG,IAAI,CAACvM,cAAc;MACrC,CAAC,CAAC;;IAGJ,IAAI,CAACR,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,SAAS,IAAI,CAACd,cAAc,GAAG,SAAS,GAAG,UAAU,EAAE,CACxD;IACD,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAwM,WAAWA,CAAA;IACT,IAAI,CAACvM,cAAc,GAAG,CAAC,IAAI,CAACA,cAAc;IAE1C,IAAI,IAAI,CAACI,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAACoM,cAAc,EAAE,CAACjK,OAAO,CAAEqD,KAAK,IAAI;QAClDA,KAAK,CAAC0G,OAAO,GAAG,IAAI,CAACtM,cAAc;MACrC,CAAC,CAAC;;IAGJ,IAAI,CAACT,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,SAAS,IAAI,CAACb,cAAc,GAAG,SAAS,GAAG,UAAU,EAAE,CACxD;IACD,OAAO,IAAI,CAACA,cAAc;EAC5B;EAEA;;;EAGAyM,WAAWA,CACTrF,MAAc,EACdsF,WAAqB,EACrBC,WAAqB;IAErB,OAAO,IAAI,CAACrN,MAAM,CACfoJ,MAAM,CAAmC;MACxCC,QAAQ,EAAE5J,0BAA0B;MACpC6J,SAAS,EAAE;QAAExB,MAAM;QAAEO,KAAK,EAAE+E,WAAW;QAAEzJ,KAAK,EAAE0J;MAAW;KAC5D,CAAC,CACD7D,IAAI,CACHvK,GAAG,CAAEwK,MAAM,IAAI;MACb,OAAOA,MAAM,CAACnE,IAAK,CAACgI,eAAe;IACrC,CAAC,CAAC,EACFpO,UAAU,CAAEkF,KAAK,IAAI;MACnB,IAAI,CAACnE,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,uBAAuB,EAAEA,KAAK,CAAC;MAChE,OAAOrF,UAAU,CACf,MAAM,IAAI4J,KAAK,CAAC,sCAAsC,CAAC,CACxD;IACH,CAAC,CAAC,CACH;EACL;EAEA;;;EAGQf,aAAaA,CAAA;IACnB,IAAI,CAAC3H,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,sBAAsB,CAAC;IAEvD,IAAI,CAACM,aAAa,EAAE;IACpB,IAAI,CAAC3B,UAAU,CAACmF,IAAI,CAAC,IAAI,CAAC;IAC1B,IAAI,CAAClF,YAAY,CAACkF,IAAI,CAAC,IAAI,CAAC;IAC5B,IAAI,CAACnE,aAAa,GAAG,IAAI;IACzB,IAAI,CAACU,aAAa,EAAE;EACtB;EAEA;;;EAGQgF,uBAAuBA,CAAA;IAC7B,IAAI,CAAC3G,MAAM,CAACmE,KAAK,CAAC,aAAa,EAAE,4BAA4B,CAAC;IAE9D,IAAI,CAACC,IAAI,CAAC,UAAU,CAAC;IACrB,IAAI,CAACuD,aAAa,EAAE;EACtB;EAEA;;;EAGQhG,aAAaA,CAAA;IACnB,IAAI,CAAC3B,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,iCAAiC,CAAC;IAEnE,IAAI,IAAI,CAAChB,WAAW,EAAE;MACpB,IAAI,CAACA,WAAW,CAAC2H,SAAS,EAAE,CAACxF,OAAO,CAAEqD,KAAK,IAAI;QAC7CA,KAAK,CAAC3B,IAAI,EAAE;QACZ,IAAI,CAAC1E,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,WAAWwE,KAAK,CAACC,IAAI,QAAQ,CAAC;MACjE,CAAC,CAAC;MACF,IAAI,CAACzF,WAAW,GAAG,IAAI;;IAGzB,IAAI,IAAI,CAACD,cAAc,EAAE;MACvB,IAAI,CAACA,cAAc,CAAC0M,KAAK,EAAE;MAC3B,IAAI,CAAC1M,cAAc,GAAG,IAAI;MAC1B,IAAI,CAACZ,MAAM,CAAC6B,KAAK,CAAC,aAAa,EAAE,uBAAuB,CAAC;;IAG3D,IAAI,CAACf,YAAY,GAAG,IAAI;IACxB,IAAI,CAACI,qBAAqB,GAAG,KAAK;IAElC;IACA,IAAI,IAAI,CAACH,iBAAiB,EAAE;MAC1B,IAAI,CAACA,iBAAiB,CAACuK,SAAS,GAAG,IAAI;;IAEzC,IAAI,IAAI,CAACtK,kBAAkB,EAAE;MAC3B,IAAI,CAACA,kBAAkB,CAACsK,SAAS,GAAG,IAAI;;EAE5C;EAEA;;;EAGQd,oBAAoBA,CAAC+C,YAAkB;IAC7C,MAAMrN,YAAY,GAAiB;MACjC6G,EAAE,EAAEwG,YAAY,CAACxG,EAAE;MACnB6C,MAAM,EAAE2D,YAAY,CAACpD,SAAS;MAC9B9C,IAAI,EAAEkG,YAAY,CAAClG,IAAI;MACvBwB,cAAc,EAAE0E,YAAY,CAAC1E,cAAc;MAC3CS,KAAK,EAAE,qCAAqC;MAC5CmD,SAAS,EAAE,IAAI3D,IAAI,EAAE,CAACwB,WAAW;KAClC;IAED,IAAI,CAACtK,MAAM,CAACsB,IAAI,CACd,aAAa,EACb,8BAA8B,EAC9BpB,YAAY,CACb;IAED,IAAI,CAACwE,IAAI,CAAC,UAAU,CAAC,CAAC,CAAC;IACvB,IAAI,CAACxE,YAAY,CAACkF,IAAI,CAAClF,YAAY,CAAC;IACpC,IAAI,CAACD,UAAU,CAACmF,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;IAE5B;IACA,IAAI,CAAChB,IAAI,CAAC,UAAU,EAAE,IAAI,CAAC;EAC7B;EAEA;;;EAGAoJ,YAAYA,CAAA;IACV,IAAI,CAACxN,MAAM,CAACsB,IAAI,CAAC,aAAa,EAAE,uCAAuC,CAAC;EAC1E;EAEA;EAEA;;;EAGA,IAAImM,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjN,cAAc;EAC5B;EAEA;;;EAGA,IAAIkN,YAAYA,CAAA;IACd,OAAO,IAAI,CAACjN,cAAc;EAC5B;EAEA;;;EAGA,IAAIkN,WAAWA,CAAA;IACb,OAAO,IAAI,CAAC1N,UAAU,CAAC2N,KAAK;EAC9B;EAEA;;;EAGA,IAAIC,mBAAmBA,CAAA;IACrB,OAAO,IAAI,CAAC3N,YAAY,CAAC0N,KAAK;EAChC;;;uBArzCW/N,WAAW,EAAAiO,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,aAAA;IAAA;EAAA;;;aAAXtO,WAAW;MAAAuO,OAAA,EAAXvO,WAAW,CAAAwO,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}
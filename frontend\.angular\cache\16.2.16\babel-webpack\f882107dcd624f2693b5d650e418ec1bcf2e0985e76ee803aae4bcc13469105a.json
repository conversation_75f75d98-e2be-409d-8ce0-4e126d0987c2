{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component, ViewChild } from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallType, CallStatus } from '../../models/message.model';\nexport let ActiveCallComponent = class ActiveCallComponent {\n  constructor(callService, logger, authService) {\n    this.callService = callService;\n    this.logger = logger;\n    this.authService = authService;\n    this.activeCall = null;\n    this.callDuration = '00:00';\n    this.currentUserId = null;\n    // États des contrôles\n    this.isAudioMuted = false;\n    this.isVideoMuted = false;\n    this.isSpeakerOn = true;\n    this.subscription = new Subscription();\n    this.callStartTime = null;\n    this.logger.info('ActiveCall', '🚀 ActiveCall component initialized');\n  }\n  ngOnInit() {\n    this.logger.info('ActiveCall', '📞 Setting up active call subscription');\n    // Obtenir l'ID de l'utilisateur actuel\n    this.currentUserId = this.getCurrentUserId();\n    // S'abonner aux appels actifs\n    this.subscription.add(this.callService.activeCall$.subscribe(call => {\n      this.logger.debug('ActiveCall', '📞 Active call update received:', call);\n      // Vérifier si l'utilisateur actuel est le receiver (pas le sender)\n      if (call && this.isReceiver(call)) {\n        this.activeCall = call;\n        if (call.status === CallStatus.CONNECTED) {\n          this.logger.info('ActiveCall', '✅ Call connected, starting timer');\n          this.startCallTimer();\n        } else {\n          this.logger.debug('ActiveCall', '⏹️ Call not connected, stopping timer');\n          this.stopCallTimer();\n        }\n      } else if (call) {\n        // Si l'utilisateur est le sender, ne pas afficher le template\n        this.logger.info('ActiveCall', '📞 Call initiated by current user (sender), hiding active call template');\n        this.activeCall = null;\n        this.stopCallTimer();\n      } else {\n        this.activeCall = null;\n        this.stopCallTimer();\n      }\n    }));\n  }\n  ngAfterViewInit() {\n    // Configurer les éléments vidéo après l'initialisation de la vue\n    if (this.localVideo && this.remoteVideo) {\n      this.callService.setVideoElements(this.localVideo.nativeElement, this.remoteVideo.nativeElement);\n    }\n  }\n  ngOnDestroy() {\n    this.subscription.unsubscribe();\n    this.stopCallTimer();\n  }\n  /**\n   * Démarre le timer de durée d'appel\n   */\n  startCallTimer() {\n    this.callStartTime = new Date();\n    this.durationInterval = setInterval(() => {\n      if (this.callStartTime) {\n        const now = new Date();\n        const diff = now.getTime() - this.callStartTime.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const seconds = Math.floor(diff % 60000 / 1000);\n        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;\n      }\n    }, 1000);\n  }\n  /**\n   * Arrête le timer de durée d'appel\n   */\n  stopCallTimer() {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n    this.callDuration = '00:00';\n    this.callStartTime = null;\n  }\n  /**\n   * Bascule l'état du microphone\n   */\n  toggleMicrophone() {\n    this.isAudioMuted = !this.isAudioMuted;\n    console.log('Microphone:', this.isAudioMuted ? 'Muted' : 'Unmuted');\n  }\n  /**\n   * Bascule l'état de la caméra\n   */\n  toggleCamera() {\n    this.isVideoMuted = !this.isVideoMuted;\n    console.log('Camera:', this.isVideoMuted ? 'Muted' : 'Unmuted');\n  }\n  /**\n   * Bascule l'état du haut-parleur\n   */\n  toggleSpeaker() {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    console.log('Speaker:', this.isSpeakerOn ? 'On' : 'Off');\n  }\n  /**\n   * Termine l'appel\n   */\n  endCall() {\n    if (!this.activeCall) return;\n    console.log('Ending call:', this.activeCall.id);\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: () => {\n        console.log('Call ended successfully');\n      },\n      error: error => {\n        console.error('Error ending call:', error);\n      }\n    });\n  }\n  /**\n   * Vérifie si c'est un appel vidéo\n   */\n  isVideoCall() {\n    return this.activeCall?.type === CallType.VIDEO;\n  }\n  /**\n   * Obtient le texte du statut de l'appel\n   */\n  getCallStatusText() {\n    if (!this.activeCall) return '';\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Sonnerie...';\n      case CallStatus.CONNECTED:\n        return 'Connecté';\n      case CallStatus.ENDED:\n        return 'Terminé';\n      case CallStatus.MISSED:\n        return 'Manqué';\n      case CallStatus.REJECTED:\n        return 'Rejeté';\n      case CallStatus.FAILED:\n        return 'Échec';\n      default:\n        return 'En cours...';\n    }\n  }\n  /**\n   * Obtient le nom de l'autre participant\n   */\n  getOtherParticipantName() {\n    if (!this.activeCall) return '';\n    return this.activeCall.recipient?.username || this.activeCall.caller?.username || 'Utilisateur';\n  }\n  /**\n   * Obtient l'avatar de l'autre participant\n   */\n  getOtherParticipantAvatar() {\n    if (!this.activeCall) return '/assets/images/default-avatar.png';\n    return this.activeCall.recipient?.image || this.activeCall.caller?.image || '/assets/images/default-avatar.png';\n  }\n  /**\n   * Obtient l'ID de l'utilisateur actuel depuis localStorage\n   */\n  getCurrentUserId() {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        return null;\n      }\n      const user = JSON.parse(userString);\n      return user._id || user.id || user.userId || null;\n    } catch (error) {\n      this.logger.error('ActiveCall', 'Error getting current user ID:', error);\n      return null;\n    }\n  }\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  isReceiver(call) {\n    if (!call || !this.currentUserId) return false;\n    const recipientId = call.recipient?.id || call.recipient?._id;\n    const callerId = call.caller?.id || call.caller?._id;\n    // L'utilisateur est le receiver si son ID correspond au recipient\n    const isRecipient = String(recipientId) === String(this.currentUserId);\n    this.logger.debug('ActiveCall', 'Checking if user is receiver:', {\n      currentUserId: this.currentUserId,\n      recipientId,\n      callerId,\n      isRecipient\n    });\n    return isRecipient;\n  }\n};\n__decorate([ViewChild('localVideo')], ActiveCallComponent.prototype, \"localVideo\", void 0);\n__decorate([ViewChild('remoteVideo')], ActiveCallComponent.prototype, \"remoteVideo\", void 0);\nActiveCallComponent = __decorate([Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css']\n})], ActiveCallComponent);", "map": {"version": 3, "names": ["Component", "ViewChild", "Subscription", "CallType", "CallStatus", "ActiveCallComponent", "constructor", "callService", "logger", "authService", "activeCall", "callDuration", "currentUserId", "isAudioMuted", "isVideoMuted", "isSpeakerOn", "subscription", "callStartTime", "info", "ngOnInit", "getCurrentUserId", "add", "activeCall$", "subscribe", "call", "debug", "isReceiver", "status", "CONNECTED", "startCallTimer", "stopCallTimer", "ngAfterViewInit", "localVideo", "remoteVideo", "setVideoElements", "nativeElement", "ngOnDestroy", "unsubscribe", "Date", "durationInterval", "setInterval", "now", "diff", "getTime", "minutes", "Math", "floor", "seconds", "toString", "padStart", "clearInterval", "toggleMicrophone", "console", "log", "toggleCamera", "toggleSpeaker", "endCall", "id", "next", "error", "isVideoCall", "type", "VIDEO", "getCallStatusText", "RINGING", "ENDED", "MISSED", "REJECTED", "FAILED", "getOtherParticipantName", "recipient", "username", "caller", "getOtherParticipantAvatar", "image", "userString", "localStorage", "getItem", "user", "JSON", "parse", "_id", "userId", "recipientId", "callerId", "isRecipient", "String", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\OneDrive\\Bureau\\Project PI\\devBridge\\frontend\\src\\app\\components\\active-call\\active-call.component.ts"], "sourcesContent": ["import {\n  Component,\n  On<PERSON>nit,\n  <PERSON><PERSON><PERSON><PERSON>,\n  ViewChild,\n  ElementRef,\n  AfterViewInit,\n} from '@angular/core';\nimport { Subscription } from 'rxjs';\nimport { CallService } from '../../services/call.service';\nimport { LoggerService } from '../../services/logger.service';\nimport { Call, CallType, CallStatus } from '../../models/message.model';\n\n@Component({\n  selector: 'app-active-call',\n  templateUrl: './active-call.component.html',\n  styleUrls: ['./active-call.component.css'],\n})\nexport class ActiveCallComponent implements OnInit, OnDestroy, AfterViewInit {\n  @ViewChild('localVideo') localVideo!: ElementRef<HTMLVideoElement>;\n  @ViewChild('remoteVideo') remoteVideo!: ElementRef<HTMLVideoElement>;\n\n  activeCall: Call | null = null;\n  callDuration = '00:00';\n  currentUserId: string | null = null;\n\n  // États des contrôles\n  isAudioMuted = false;\n  isVideoMuted = false;\n  isSpeakerOn = true;\n\n  private subscription: Subscription = new Subscription();\n  private callStartTime: Date | null = null;\n  private durationInterval: any;\n\n  constructor(\n    private callService: CallService,\n    private logger: LoggerService,\n    private authService: AuthService\n  ) {\n    this.logger.info('ActiveCall', '🚀 ActiveCall component initialized');\n  }\n\n  ngOnInit(): void {\n    this.logger.info('ActiveCall', '📞 Setting up active call subscription');\n\n    // Obtenir l'ID de l'utilisateur actuel\n    this.currentUserId = this.getCurrentUserId();\n\n    // S'abonner aux appels actifs\n    this.subscription.add(\n      this.callService.activeCall$.subscribe((call) => {\n        this.logger.debug(\n          'ActiveCall',\n          '📞 Active call update received:',\n          call\n        );\n\n        // Vérifier si l'utilisateur actuel est le receiver (pas le sender)\n        if (call && this.isReceiver(call)) {\n          this.activeCall = call;\n\n          if (call.status === CallStatus.CONNECTED) {\n            this.logger.info('ActiveCall', '✅ Call connected, starting timer');\n            this.startCallTimer();\n          } else {\n            this.logger.debug(\n              'ActiveCall',\n              '⏹️ Call not connected, stopping timer'\n            );\n            this.stopCallTimer();\n          }\n        } else if (call) {\n          // Si l'utilisateur est le sender, ne pas afficher le template\n          this.logger.info(\n            'ActiveCall',\n            '📞 Call initiated by current user (sender), hiding active call template'\n          );\n          this.activeCall = null;\n          this.stopCallTimer();\n        } else {\n          this.activeCall = null;\n          this.stopCallTimer();\n        }\n      })\n    );\n  }\n\n  ngAfterViewInit(): void {\n    // Configurer les éléments vidéo après l'initialisation de la vue\n    if (this.localVideo && this.remoteVideo) {\n      this.callService.setVideoElements(\n        this.localVideo.nativeElement,\n        this.remoteVideo.nativeElement\n      );\n    }\n  }\n\n  ngOnDestroy(): void {\n    this.subscription.unsubscribe();\n    this.stopCallTimer();\n  }\n\n  /**\n   * Démarre le timer de durée d'appel\n   */\n  private startCallTimer(): void {\n    this.callStartTime = new Date();\n    this.durationInterval = setInterval(() => {\n      if (this.callStartTime) {\n        const now = new Date();\n        const diff = now.getTime() - this.callStartTime.getTime();\n        const minutes = Math.floor(diff / 60000);\n        const seconds = Math.floor((diff % 60000) / 1000);\n        this.callDuration = `${minutes.toString().padStart(2, '0')}:${seconds\n          .toString()\n          .padStart(2, '0')}`;\n      }\n    }, 1000);\n  }\n\n  /**\n   * Arrête le timer de durée d'appel\n   */\n  private stopCallTimer(): void {\n    if (this.durationInterval) {\n      clearInterval(this.durationInterval);\n      this.durationInterval = null;\n    }\n    this.callDuration = '00:00';\n    this.callStartTime = null;\n  }\n\n  /**\n   * Bascule l'état du microphone\n   */\n  toggleMicrophone(): void {\n    this.isAudioMuted = !this.isAudioMuted;\n    console.log('Microphone:', this.isAudioMuted ? 'Muted' : 'Unmuted');\n  }\n\n  /**\n   * Bascule l'état de la caméra\n   */\n  toggleCamera(): void {\n    this.isVideoMuted = !this.isVideoMuted;\n    console.log('Camera:', this.isVideoMuted ? 'Muted' : 'Unmuted');\n  }\n\n  /**\n   * Bascule l'état du haut-parleur\n   */\n  toggleSpeaker(): void {\n    this.isSpeakerOn = !this.isSpeakerOn;\n    console.log('Speaker:', this.isSpeakerOn ? 'On' : 'Off');\n  }\n\n  /**\n   * Termine l'appel\n   */\n  endCall(): void {\n    if (!this.activeCall) return;\n\n    console.log('Ending call:', this.activeCall.id);\n\n    this.callService.endCall(this.activeCall.id).subscribe({\n      next: () => {\n        console.log('Call ended successfully');\n      },\n      error: (error: any) => {\n        console.error('Error ending call:', error);\n      },\n    });\n  }\n\n  /**\n   * Vérifie si c'est un appel vidéo\n   */\n  isVideoCall(): boolean {\n    return this.activeCall?.type === CallType.VIDEO;\n  }\n\n  /**\n   * Obtient le texte du statut de l'appel\n   */\n  getCallStatusText(): string {\n    if (!this.activeCall) return '';\n\n    switch (this.activeCall.status) {\n      case CallStatus.RINGING:\n        return 'Sonnerie...';\n      case CallStatus.CONNECTED:\n        return 'Connecté';\n      case CallStatus.ENDED:\n        return 'Terminé';\n      case CallStatus.MISSED:\n        return 'Manqué';\n      case CallStatus.REJECTED:\n        return 'Rejeté';\n      case CallStatus.FAILED:\n        return 'Échec';\n      default:\n        return 'En cours...';\n    }\n  }\n\n  /**\n   * Obtient le nom de l'autre participant\n   */\n  getOtherParticipantName(): string {\n    if (!this.activeCall) return '';\n\n    return (\n      this.activeCall.recipient?.username ||\n      this.activeCall.caller?.username ||\n      'Utilisateur'\n    );\n  }\n\n  /**\n   * Obtient l'avatar de l'autre participant\n   */\n  getOtherParticipantAvatar(): string {\n    if (!this.activeCall) return '/assets/images/default-avatar.png';\n\n    return (\n      this.activeCall.recipient?.image ||\n      this.activeCall.caller?.image ||\n      '/assets/images/default-avatar.png'\n    );\n  }\n\n  /**\n   * Obtient l'ID de l'utilisateur actuel depuis localStorage\n   */\n  private getCurrentUserId(): string | null {\n    try {\n      const userString = localStorage.getItem('user');\n      if (!userString || userString === 'null' || userString === 'undefined') {\n        return null;\n      }\n      const user = JSON.parse(userString);\n      return user._id || user.id || user.userId || null;\n    } catch (error) {\n      this.logger.error('ActiveCall', 'Error getting current user ID:', error);\n      return null;\n    }\n  }\n\n  /**\n   * Vérifie si l'utilisateur actuel est le receiver de l'appel\n   */\n  private isReceiver(call: Call): boolean {\n    if (!call || !this.currentUserId) return false;\n\n    const recipientId = call.recipient?.id || call.recipient?._id;\n    const callerId = call.caller?.id || call.caller?._id;\n\n    // L'utilisateur est le receiver si son ID correspond au recipient\n    const isRecipient = String(recipientId) === String(this.currentUserId);\n\n    this.logger.debug('ActiveCall', 'Checking if user is receiver:', {\n      currentUserId: this.currentUserId,\n      recipientId,\n      callerId,\n      isRecipient,\n    });\n\n    return isRecipient;\n  }\n}\n"], "mappings": ";AAAA,SACEA,SAAS,EAGTC,SAAS,QAGJ,eAAe;AACtB,SAASC,YAAY,QAAQ,MAAM;AAGnC,SAAeC,QAAQ,EAAEC,UAAU,QAAQ,4BAA4B;AAOhE,WAAMC,mBAAmB,GAAzB,MAAMA,mBAAmB;EAiB9BC,YACUC,WAAwB,EACxBC,MAAqB,EACrBC,WAAwB;IAFxB,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,WAAW,GAAXA,WAAW;IAhBrB,KAAAC,UAAU,GAAgB,IAAI;IAC9B,KAAAC,YAAY,GAAG,OAAO;IACtB,KAAAC,aAAa,GAAkB,IAAI;IAEnC;IACA,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,YAAY,GAAG,KAAK;IACpB,KAAAC,WAAW,GAAG,IAAI;IAEV,KAAAC,YAAY,GAAiB,IAAId,YAAY,EAAE;IAC/C,KAAAe,aAAa,GAAgB,IAAI;IAQvC,IAAI,CAACT,MAAM,CAACU,IAAI,CAAC,YAAY,EAAE,qCAAqC,CAAC;EACvE;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACX,MAAM,CAACU,IAAI,CAAC,YAAY,EAAE,wCAAwC,CAAC;IAExE;IACA,IAAI,CAACN,aAAa,GAAG,IAAI,CAACQ,gBAAgB,EAAE;IAE5C;IACA,IAAI,CAACJ,YAAY,CAACK,GAAG,CACnB,IAAI,CAACd,WAAW,CAACe,WAAW,CAACC,SAAS,CAAEC,IAAI,IAAI;MAC9C,IAAI,CAAChB,MAAM,CAACiB,KAAK,CACf,YAAY,EACZ,iCAAiC,EACjCD,IAAI,CACL;MAED;MACA,IAAIA,IAAI,IAAI,IAAI,CAACE,UAAU,CAACF,IAAI,CAAC,EAAE;QACjC,IAAI,CAACd,UAAU,GAAGc,IAAI;QAEtB,IAAIA,IAAI,CAACG,MAAM,KAAKvB,UAAU,CAACwB,SAAS,EAAE;UACxC,IAAI,CAACpB,MAAM,CAACU,IAAI,CAAC,YAAY,EAAE,kCAAkC,CAAC;UAClE,IAAI,CAACW,cAAc,EAAE;SACtB,MAAM;UACL,IAAI,CAACrB,MAAM,CAACiB,KAAK,CACf,YAAY,EACZ,uCAAuC,CACxC;UACD,IAAI,CAACK,aAAa,EAAE;;OAEvB,MAAM,IAAIN,IAAI,EAAE;QACf;QACA,IAAI,CAAChB,MAAM,CAACU,IAAI,CACd,YAAY,EACZ,yEAAyE,CAC1E;QACD,IAAI,CAACR,UAAU,GAAG,IAAI;QACtB,IAAI,CAACoB,aAAa,EAAE;OACrB,MAAM;QACL,IAAI,CAACpB,UAAU,GAAG,IAAI;QACtB,IAAI,CAACoB,aAAa,EAAE;;IAExB,CAAC,CAAC,CACH;EACH;EAEAC,eAAeA,CAAA;IACb;IACA,IAAI,IAAI,CAACC,UAAU,IAAI,IAAI,CAACC,WAAW,EAAE;MACvC,IAAI,CAAC1B,WAAW,CAAC2B,gBAAgB,CAC/B,IAAI,CAACF,UAAU,CAACG,aAAa,EAC7B,IAAI,CAACF,WAAW,CAACE,aAAa,CAC/B;;EAEL;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACpB,YAAY,CAACqB,WAAW,EAAE;IAC/B,IAAI,CAACP,aAAa,EAAE;EACtB;EAEA;;;EAGQD,cAAcA,CAAA;IACpB,IAAI,CAACZ,aAAa,GAAG,IAAIqB,IAAI,EAAE;IAC/B,IAAI,CAACC,gBAAgB,GAAGC,WAAW,CAAC,MAAK;MACvC,IAAI,IAAI,CAACvB,aAAa,EAAE;QACtB,MAAMwB,GAAG,GAAG,IAAIH,IAAI,EAAE;QACtB,MAAMI,IAAI,GAAGD,GAAG,CAACE,OAAO,EAAE,GAAG,IAAI,CAAC1B,aAAa,CAAC0B,OAAO,EAAE;QACzD,MAAMC,OAAO,GAAGC,IAAI,CAACC,KAAK,CAACJ,IAAI,GAAG,KAAK,CAAC;QACxC,MAAMK,OAAO,GAAGF,IAAI,CAACC,KAAK,CAAEJ,IAAI,GAAG,KAAK,GAAI,IAAI,CAAC;QACjD,IAAI,CAAC/B,YAAY,GAAG,GAAGiC,OAAO,CAACI,QAAQ,EAAE,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,IAAIF,OAAO,CAClEC,QAAQ,EAAE,CACVC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,EAAE;;IAEzB,CAAC,EAAE,IAAI,CAAC;EACV;EAEA;;;EAGQnB,aAAaA,CAAA;IACnB,IAAI,IAAI,CAACS,gBAAgB,EAAE;MACzBW,aAAa,CAAC,IAAI,CAACX,gBAAgB,CAAC;MACpC,IAAI,CAACA,gBAAgB,GAAG,IAAI;;IAE9B,IAAI,CAAC5B,YAAY,GAAG,OAAO;IAC3B,IAAI,CAACM,aAAa,GAAG,IAAI;EAC3B;EAEA;;;EAGAkC,gBAAgBA,CAAA;IACd,IAAI,CAACtC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCuC,OAAO,CAACC,GAAG,CAAC,aAAa,EAAE,IAAI,CAACxC,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;EACrE;EAEA;;;EAGAyC,YAAYA,CAAA;IACV,IAAI,CAACxC,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCsC,OAAO,CAACC,GAAG,CAAC,SAAS,EAAE,IAAI,CAACvC,YAAY,GAAG,OAAO,GAAG,SAAS,CAAC;EACjE;EAEA;;;EAGAyC,aAAaA,CAAA;IACX,IAAI,CAACxC,WAAW,GAAG,CAAC,IAAI,CAACA,WAAW;IACpCqC,OAAO,CAACC,GAAG,CAAC,UAAU,EAAE,IAAI,CAACtC,WAAW,GAAG,IAAI,GAAG,KAAK,CAAC;EAC1D;EAEA;;;EAGAyC,OAAOA,CAAA;IACL,IAAI,CAAC,IAAI,CAAC9C,UAAU,EAAE;IAEtB0C,OAAO,CAACC,GAAG,CAAC,cAAc,EAAE,IAAI,CAAC3C,UAAU,CAAC+C,EAAE,CAAC;IAE/C,IAAI,CAAClD,WAAW,CAACiD,OAAO,CAAC,IAAI,CAAC9C,UAAU,CAAC+C,EAAE,CAAC,CAAClC,SAAS,CAAC;MACrDmC,IAAI,EAAEA,CAAA,KAAK;QACTN,OAAO,CAACC,GAAG,CAAC,yBAAyB,CAAC;MACxC,CAAC;MACDM,KAAK,EAAGA,KAAU,IAAI;QACpBP,OAAO,CAACO,KAAK,CAAC,oBAAoB,EAAEA,KAAK,CAAC;MAC5C;KACD,CAAC;EACJ;EAEA;;;EAGAC,WAAWA,CAAA;IACT,OAAO,IAAI,CAAClD,UAAU,EAAEmD,IAAI,KAAK1D,QAAQ,CAAC2D,KAAK;EACjD;EAEA;;;EAGAC,iBAAiBA,CAAA;IACf,IAAI,CAAC,IAAI,CAACrD,UAAU,EAAE,OAAO,EAAE;IAE/B,QAAQ,IAAI,CAACA,UAAU,CAACiB,MAAM;MAC5B,KAAKvB,UAAU,CAAC4D,OAAO;QACrB,OAAO,aAAa;MACtB,KAAK5D,UAAU,CAACwB,SAAS;QACvB,OAAO,UAAU;MACnB,KAAKxB,UAAU,CAAC6D,KAAK;QACnB,OAAO,SAAS;MAClB,KAAK7D,UAAU,CAAC8D,MAAM;QACpB,OAAO,QAAQ;MACjB,KAAK9D,UAAU,CAAC+D,QAAQ;QACtB,OAAO,QAAQ;MACjB,KAAK/D,UAAU,CAACgE,MAAM;QACpB,OAAO,OAAO;MAChB;QACE,OAAO,aAAa;;EAE1B;EAEA;;;EAGAC,uBAAuBA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC3D,UAAU,EAAE,OAAO,EAAE;IAE/B,OACE,IAAI,CAACA,UAAU,CAAC4D,SAAS,EAAEC,QAAQ,IACnC,IAAI,CAAC7D,UAAU,CAAC8D,MAAM,EAAED,QAAQ,IAChC,aAAa;EAEjB;EAEA;;;EAGAE,yBAAyBA,CAAA;IACvB,IAAI,CAAC,IAAI,CAAC/D,UAAU,EAAE,OAAO,mCAAmC;IAEhE,OACE,IAAI,CAACA,UAAU,CAAC4D,SAAS,EAAEI,KAAK,IAChC,IAAI,CAAChE,UAAU,CAAC8D,MAAM,EAAEE,KAAK,IAC7B,mCAAmC;EAEvC;EAEA;;;EAGQtD,gBAAgBA,CAAA;IACtB,IAAI;MACF,MAAMuD,UAAU,GAAGC,YAAY,CAACC,OAAO,CAAC,MAAM,CAAC;MAC/C,IAAI,CAACF,UAAU,IAAIA,UAAU,KAAK,MAAM,IAAIA,UAAU,KAAK,WAAW,EAAE;QACtE,OAAO,IAAI;;MAEb,MAAMG,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACL,UAAU,CAAC;MACnC,OAAOG,IAAI,CAACG,GAAG,IAAIH,IAAI,CAACrB,EAAE,IAAIqB,IAAI,CAACI,MAAM,IAAI,IAAI;KAClD,CAAC,OAAOvB,KAAK,EAAE;MACd,IAAI,CAACnD,MAAM,CAACmD,KAAK,CAAC,YAAY,EAAE,gCAAgC,EAAEA,KAAK,CAAC;MACxE,OAAO,IAAI;;EAEf;EAEA;;;EAGQjC,UAAUA,CAACF,IAAU;IAC3B,IAAI,CAACA,IAAI,IAAI,CAAC,IAAI,CAACZ,aAAa,EAAE,OAAO,KAAK;IAE9C,MAAMuE,WAAW,GAAG3D,IAAI,CAAC8C,SAAS,EAAEb,EAAE,IAAIjC,IAAI,CAAC8C,SAAS,EAAEW,GAAG;IAC7D,MAAMG,QAAQ,GAAG5D,IAAI,CAACgD,MAAM,EAAEf,EAAE,IAAIjC,IAAI,CAACgD,MAAM,EAAES,GAAG;IAEpD;IACA,MAAMI,WAAW,GAAGC,MAAM,CAACH,WAAW,CAAC,KAAKG,MAAM,CAAC,IAAI,CAAC1E,aAAa,CAAC;IAEtE,IAAI,CAACJ,MAAM,CAACiB,KAAK,CAAC,YAAY,EAAE,+BAA+B,EAAE;MAC/Db,aAAa,EAAE,IAAI,CAACA,aAAa;MACjCuE,WAAW;MACXC,QAAQ;MACRC;KACD,CAAC;IAEF,OAAOA,WAAW;EACpB;CACD;AA3P0BE,UAAA,EAAxBtF,SAAS,CAAC,YAAY,CAAC,C,sDAA2C;AACzCsF,UAAA,EAAzBtF,SAAS,CAAC,aAAa,CAAC,C,uDAA4C;AAF1DI,mBAAmB,GAAAkF,UAAA,EAL/BvF,SAAS,CAAC;EACTwF,QAAQ,EAAE,iBAAiB;EAC3BC,WAAW,EAAE,8BAA8B;EAC3CC,SAAS,EAAE,CAAC,6BAA6B;CAC1C,CAAC,C,EACWrF,mBAAmB,CA4P/B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}